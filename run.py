"""Application entry point for SaaS POS System."""

import os
from app import create_app, db
from config import config

# Get configuration from environment
config_name = os.environ.get('FLASK_ENV', 'development')
app = create_app(config[config_name])


@app.shell_context_processor
def make_shell_context():
    """Make database and models available in Flask shell."""
    from app.models.user import User, Tenant
    from app.models.product import Product, Category
    from app.models.transaction import Transaction, TransactionItem
    from app.models.business import BusinessSettings
    
    return {
        'db': db,
        'User': User,
        'Tenant': Tenant,
        'Product': Product,
        'Category': Category,
        'Transaction': Transaction,
        'TransactionItem': TransactionItem,
        'BusinessSettings': BusinessSettings
    }


if __name__ == '__main__':
    app.run(debug=True)