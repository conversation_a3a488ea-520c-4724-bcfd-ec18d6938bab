#!/usr/bin/env python3
"""
Create admin account for SaaS POS System.

This script creates a default admin account with a demo business
for immediate access to the system.
"""

import os
import sys
from datetime import datetime

# Add the app directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models.user import User, Tenant
from app.services.audit_service import AuditService
from app.models.audit import AuditAction, AuditSeverity
from config import DevelopmentConfig


def create_admin_account():
    """Create default admin account and demo business."""
    
    print("🚀 Creating admin account for SaaS POS System...")
    
    # Create Flask app
    app = create_app(DevelopmentConfig)
    
    with app.app_context():
        try:
            # Create database tables if they don't exist
            db.create_all()
            print("✅ Database tables created/verified")
            
            # Check if admin already exists
            existing_admin = User.query.filter_by(email='<EMAIL>').first()
            if existing_admin:
                print("⚠️  Admin account already exists!")
                print(f"   Email: <EMAIL>")
                print(f"   Business: {existing_admin.tenant.name}")
                
                response = input("Do you want to reset the admin password? (y/N): ")
                if response.lower() in ['y', 'yes']:
                    existing_admin.set_password('Azerty10')
                    existing_admin.save()
                    print("✅ Admin password reset to 'Azerty10'")
                    
                    # Log the password reset
                    AuditService.log(
                        action=AuditAction.PASSWORD_RESET,
                        description="Admin password reset via create_admin.py script",
                        user_id=existing_admin.id,
                        tenant_id=existing_admin.tenant_id,
                        severity=AuditSeverity.HIGH
                    )
                else:
                    print("ℹ️  Admin account unchanged")
                
                return existing_admin.tenant, existing_admin
            
            # Create demo business (tenant)
            print("📊 Creating demo business...")
            demo_business = Tenant(
                name='Demo Business',
                business_type='retail',
                email='<EMAIL>',
                phone='******-0123',
                address='123 Demo Street, Demo City, DC 12345',
                subscription_plan='premium',
                timezone='UTC',
                currency='USD'
            )
            demo_business.save()
            print(f"✅ Demo business created: {demo_business.name}")
            
            # Create admin user
            print("👤 Creating admin user...")
            admin_user = User.create_user(
                email='<EMAIL>',
                password='Azerty10',
                first_name='System',
                last_name='Administrator',
                tenant_id=demo_business.id,
                role='admin'
            )
            
            # Mark email as verified
            admin_user.email_verified = True
            admin_user.save()
            
            print(f"✅ Admin user created: {admin_user.email}")
            
            # Log the admin creation
            AuditService.log(
                action=AuditAction.USER_CREATED,
                description="System administrator account created via setup script",
                user_id=admin_user.id,
                tenant_id=demo_business.id,
                severity=AuditSeverity.HIGH,
                resource_type='user',
                resource_id=str(admin_user.id),
                new_values={
                    'email': admin_user.email,
                    'role': admin_user.role,
                    'tenant_name': demo_business.name
                }
            )
            
            return demo_business, admin_user
            
        except Exception as e:
            print(f"❌ Error creating admin account: {str(e)}")
            import traceback
            traceback.print_exc()
            return None, None


def create_sample_data(tenant, admin_user):
    """Create some sample data for demonstration."""
    
    print("\n📦 Creating sample data...")
    
    try:
        from app.models.product import Product, Category
        from app.models.business import BusinessSettings
        
        # Create sample categories
        categories = [
            Category(name='Beverages', description='Hot and cold drinks', tenant_id=tenant.id),
            Category(name='Food', description='Food items and snacks', tenant_id=tenant.id),
            Category(name='Retail', description='Retail merchandise', tenant_id=tenant.id)
        ]
        
        for category in categories:
            category.save()
        
        print(f"✅ Created {len(categories)} product categories")
        
        # Create sample products
        products = [
            Product(
                name='Coffee',
                description='Premium coffee blend',
                price=3.50,
                cost=1.20,
                sku='COFFEE-001',
                quantity=100,
                category_id=categories[0].id,
                tenant_id=tenant.id,
                is_active=True
            ),
            Product(
                name='Tea',
                description='Herbal tea selection',
                price=2.75,
                cost=0.90,
                sku='TEA-001',
                quantity=75,
                category_id=categories[0].id,
                tenant_id=tenant.id,
                is_active=True
            ),
            Product(
                name='Sandwich',
                description='Fresh deli sandwich',
                price=8.99,
                cost=4.50,
                sku='SAND-001',
                quantity=25,
                category_id=categories[1].id,
                tenant_id=tenant.id,
                is_active=True
            ),
            Product(
                name='T-Shirt',
                description='Cotton t-shirt with logo',
                price=19.99,
                cost=8.00,
                sku='SHIRT-001',
                quantity=50,
                category_id=categories[2].id,
                tenant_id=tenant.id,
                is_active=True
            )
        ]
        
        for product in products:
            product.save()
        
        print(f"✅ Created {len(products)} sample products")
        
        # Create business settings
        business_settings = BusinessSettings(
            tenant_id=tenant.id,
            business_name=tenant.name,
            tax_rate=8.25,
            currency=tenant.currency,
            timezone=tenant.timezone,
            receipt_footer='Thank you for your business!',
            low_stock_threshold=10,
            enable_inventory_tracking=True,
            enable_customer_display=True,
            enable_receipt_printing=True
        )
        business_settings.save()
        
        print("✅ Created business settings")
        
        # Log sample data creation
        AuditService.log(
            action=AuditAction.SETTINGS_CHANGED,
            description="Sample data created during system setup",
            user_id=admin_user.id,
            tenant_id=tenant.id,
            severity=AuditSeverity.LOW,
            new_values={
                'categories_created': len(categories),
                'products_created': len(products),
                'business_settings_created': True
            }
        )
        
        return True
        
    except Exception as e:
        print(f"⚠️  Warning: Could not create sample data: {str(e)}")
        return False


def display_login_info(tenant, admin_user):
    """Display login information."""
    
    print("\n" + "="*60)
    print("🎉 ADMIN ACCOUNT CREATED SUCCESSFULLY!")
    print("="*60)
    print()
    print("📋 LOGIN INFORMATION:")
    print(f"   URL: http://localhost:5000")
    print(f"   Email: {admin_user.email}")
    print(f"   Password: Azerty10")
    print(f"   Business: {tenant.name}")
    print(f"   Role: {admin_user.role.title()}")
    print()
    print("🏢 BUSINESS INFORMATION:")
    print(f"   Name: {tenant.name}")
    print(f"   Type: {tenant.business_type.title()}")
    print(f"   Plan: {tenant.subscription_plan.title()}")
    print(f"   Currency: {tenant.currency}")
    print()
    print("⚠️  SECURITY NOTICE:")
    print("   - Change the admin password immediately after first login!")
    print("   - This is a demo account for development/testing only")
    print("   - Do not use these credentials in production")
    print()
    print("🚀 NEXT STEPS:")
    print("   1. Start the application: python run.py")
    print("   2. Open http://localhost:5000 in your browser")
    print("   3. Log in with the credentials above")
    print("   4. Change the admin password in User Settings")
    print("   5. Configure your business settings")
    print()
    print("📚 DOCUMENTATION:")
    print("   - README.md - Getting started guide")
    print("   - docs/BACKUP_AND_AUDIT.md - Backup and audit system")
    print("   - docs/INPUT_VALIDATION.md - Security features")
    print()
    print("="*60)


def main():
    """Main function."""
    
    print("SaaS POS System - Admin Account Setup")
    print("=====================================\n")
    
    # Create admin account
    tenant, admin_user = create_admin_account()
    
    if not tenant or not admin_user:
        print("❌ Failed to create admin account")
        return 1
    
    # Ask if user wants sample data
    print("\n" + "-"*40)
    response = input("Do you want to create sample data (products, categories)? (Y/n): ")
    
    if response.lower() not in ['n', 'no']:
        create_sample_data(tenant, admin_user)
    
    # Display login information
    display_login_info(tenant, admin_user)
    
    return 0


if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)