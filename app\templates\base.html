<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}SaaS POS System{% endblock %}</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/responsive.css') }}">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    screens: {
                        'xs': '475px',
                    }
                }
            }
        }
    </script>
    <style>
        /* Custom styles for keyboard shortcuts */
        .keyboard-shortcut {
            display: inline-block;
            padding: 2px 6px;
            background-color: #f3f4f6;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            font-size: 0.75rem;
            font-family: monospace;
            color: #374151;
        }
        
        /* Loading animation */
        .loading-spinner {
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        
        /* Mobile-first responsive utilities */
        @media (max-width: 640px) {
            .mobile-hidden { display: none !important; }
            .mobile-full { width: 100% !important; }
        }
        
        /* Touch-friendly button sizes */
        .touch-target {
            min-height: 44px;
            min-width: 44px;
        }
    </style>
</head>
<body class="bg-gray-100 antialiased">
    <div class="min-h-screen">
        {% if current_user and current_user.is_authenticated %}
            {% include 'components/navigation.html' %}
        {% endif %}
        
        <!-- Flash messages container -->
        <div id="flash-messages" class="fixed top-0 left-0 right-0 z-50 p-4 space-y-2">
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="flash-message rounded-md p-4 shadow-lg max-w-md mx-auto {% if category == 'error' %}bg-red-50 text-red-800 border border-red-200{% elif category == 'success' %}bg-green-50 text-green-800 border border-green-200{% else %}bg-blue-50 text-blue-800 border border-blue-200{% endif %}" 
                             data-auto-dismiss="true">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        {% if category == 'error' %}
                                            <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                            </svg>
                                        {% elif category == 'success' %}
                                            <svg class="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                            </svg>
                                        {% else %}
                                            <svg class="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                                            </svg>
                                        {% endif %}
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm font-medium">{{ message }}</p>
                                    </div>
                                </div>
                                <button type="button" class="ml-4 text-gray-400 hover:text-gray-600" onclick="this.parentElement.parentElement.remove()">
                                    <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}
        </div>
        
        <main class="{% if current_user and current_user.is_authenticated %}pt-16{% endif %}">
            {% block content %}{% endblock %}
        </main>
    </div>
    
    <!-- Global JavaScript -->
    <script>
        // Auto-dismiss flash messages
        document.addEventListener('DOMContentLoaded', function() {
            const flashMessages = document.querySelectorAll('.flash-message[data-auto-dismiss="true"]');
            flashMessages.forEach(function(message) {
                setTimeout(function() {
                    message.style.transition = 'opacity 0.5s ease-out';
                    message.style.opacity = '0';
                    setTimeout(function() {
                        message.remove();
                    }, 500);
                }, 5000);
            });
        });
        
        // Global keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Alt + H for Home/Dashboard
            if (e.altKey && e.key === 'h') {
                e.preventDefault();
                window.location.href = '/dashboard';
            }
            
            // Alt + P for POS
            if (e.altKey && e.key === 'p') {
                e.preventDefault();
                window.location.href = '/pos';
            }
            
            // Alt + I for Inventory
            if (e.altKey && e.key === 'i') {
                e.preventDefault();
                window.location.href = '/inventory';
            }
            
            // Alt + R for Reports
            if (e.altKey && e.key === 'r') {
                e.preventDefault();
                window.location.href = '/reports';
            }
            
            // Escape to close modals
            if (e.key === 'Escape') {
                const modals = document.querySelectorAll('.modal:not(.hidden)');
                modals.forEach(modal => modal.classList.add('hidden'));
            }
        });
    </script>
    
    <!-- Core JavaScript files -->
    <script src="{{ url_for('static', filename='js/pos-utils.js') }}"></script>
    <script src="{{ url_for('static', filename='js/form-validation.js') }}"></script>
    <script src="{{ url_for('static', filename='js/real-time-updates.js') }}"></script>
    
    {% block scripts %}{% endblock %}
</body>
</html>