"""Unit tests for User and Tenant models."""

import pytest
from datetime import datetime, timedelta
from app import create_app, db
from app.models.user import User, Tenant
from config import TestingConfig


@pytest.fixture
def app():
    """Create test application."""
    app = create_app(TestingConfig)
    return app


@pytest.fixture
def client(app):
    """Create test client."""
    return app.test_client()


@pytest.fixture
def app_context(app):
    """Create application context."""
    with app.app_context():
        db.create_all()
        yield app
        db.session.remove()
        db.drop_all()


@pytest.fixture
def sample_tenant(app_context):
    """Create a sample tenant for testing."""
    tenant = Tenant.create(
        name='Test Business',
        business_type='restaurant',
        email='<EMAIL>',
        phone='555-0123',
        subscription_plan='premium'
    )
    return tenant


@pytest.fixture
def sample_user(app_context, sample_tenant):
    """Create a sample user for testing."""
    user = User.create_user(
        email='<EMAIL>',
        password='testpassword123',
        first_name='<PERSON>',
        last_name='Doe',
        tenant_id=sample_tenant.id,
        role='manager'
    )
    return user


class TestTenantModel:
    """Test cases for Tenant model."""
    
    def test_tenant_creation(self, app_context):
        """Test basic tenant creation."""
        tenant = Tenant.create(
            name='Test Restaurant',
            business_type='restaurant',
            email='<EMAIL>'
        )
        
        assert tenant.id is not None
        assert tenant.name == 'Test Restaurant'
        assert tenant.business_type == 'restaurant'
        assert tenant.email == '<EMAIL>'
        assert tenant.subscription_status == 'active'
        assert tenant.subscription_plan == 'basic'
        assert tenant.currency == 'USD'
        assert tenant.timezone == 'UTC'
        assert tenant.created_at is not None
    
    def test_tenant_is_active(self, app_context):
        """Test tenant active status check."""
        # Active tenant
        active_tenant = Tenant.create(name='Active Business', business_type='retail')
        assert active_tenant.is_active() is True
        
        # Inactive tenant
        inactive_tenant = Tenant.create(
            name='Inactive Business',
            business_type='retail',
            subscription_status='inactive'
        )
        assert inactive_tenant.is_active() is False
    
    def test_tenant_subscription_expiry(self, app_context):
        """Test tenant subscription expiry check."""
        # No expiry date
        tenant_no_expiry = Tenant.create(name='No Expiry', business_type='retail')
        assert tenant_no_expiry.is_subscription_expired() is False
        
        # Future expiry
        future_expiry = datetime.utcnow() + timedelta(days=30)
        tenant_future = Tenant.create(
            name='Future Expiry',
            business_type='retail',
            subscription_expires=future_expiry
        )
        assert tenant_future.is_subscription_expired() is False
        
        # Past expiry
        past_expiry = datetime.utcnow() - timedelta(days=1)
        tenant_expired = Tenant.create(
            name='Expired',
            business_type='retail',
            subscription_expires=past_expiry
        )
        assert tenant_expired.is_subscription_expired() is True
    
    def test_tenant_users_relationship(self, app_context, sample_tenant):
        """Test tenant-users relationship."""
        # Create users for the tenant
        user1 = User.create_user(
            email='<EMAIL>',
            password='password123',
            first_name='User',
            last_name='One',
            tenant_id=sample_tenant.id
        )
        
        user2 = User.create_user(
            email='<EMAIL>',
            password='password123',
            first_name='User',
            last_name='Two',
            tenant_id=sample_tenant.id
        )
        
        # Test relationship
        assert len(sample_tenant.users) == 2
        assert user1 in sample_tenant.users
        assert user2 in sample_tenant.users
    
    def test_tenant_active_users_count(self, app_context, sample_tenant):
        """Test getting active users count."""
        # Create active and inactive users
        User.create_user(
            email='<EMAIL>',
            password='password123',
            first_name='Active',
            last_name='One',
            tenant_id=sample_tenant.id
        )
        
        User.create_user(
            email='<EMAIL>',
            password='password123',
            first_name='Active',
            last_name='Two',
            tenant_id=sample_tenant.id
        )
        
        inactive_user = User.create_user(
            email='<EMAIL>',
            password='password123',
            first_name='Inactive',
            last_name='User',
            tenant_id=sample_tenant.id
        )
        inactive_user.is_active = False
        inactive_user.save()
        
        assert sample_tenant.get_active_users_count() == 2


class TestUserModel:
    """Test cases for User model."""
    
    def test_user_creation(self, app_context, sample_tenant):
        """Test basic user creation."""
        user = User.create_user(
            email='<EMAIL>',
            password='securepassword123',
            first_name='Jane',
            last_name='Smith',
            tenant_id=sample_tenant.id,
            role='cashier'
        )
        
        assert user.id is not None
        assert user.email == '<EMAIL>'
        assert user.first_name == 'Jane'
        assert user.last_name == 'Smith'
        assert user.tenant_id == sample_tenant.id
        assert user.role == 'cashier'
        assert user.is_active is True
        assert user.email_verified is False
        assert user.login_count == 0
        assert user.failed_login_attempts == 0
        assert user.password_hash is not None
        assert user.password_hash != 'securepassword123'  # Should be hashed
    
    def test_password_hashing(self, app_context, sample_tenant):
        """Test password hashing and verification."""
        user = User.create_user(
            email='<EMAIL>',
            password='mypassword123',
            first_name='Test',
            last_name='User',
            tenant_id=sample_tenant.id
        )
        
        # Password should be hashed
        assert user.password_hash != 'mypassword123'
        
        # Should be able to verify correct password
        assert user.check_password('mypassword123') is True
        
        # Should reject incorrect password
        assert user.check_password('wrongpassword') is False
    
    def test_user_full_name(self, app_context, sample_user):
        """Test getting user's full name."""
        assert sample_user.get_full_name() == 'John Doe'
    
    def test_user_role_checks(self, app_context, sample_tenant):
        """Test user role permission checks."""
        # Admin user
        admin = User.create_user(
            email='<EMAIL>',
            password='password123',
            first_name='Admin',
            last_name='User',
            tenant_id=sample_tenant.id,
            role='admin'
        )
        
        assert admin.is_admin() is True
        assert admin.is_manager() is True
        assert admin.can_access_reports() is True
        assert admin.can_manage_inventory() is True
        assert admin.can_process_transactions() is True
        
        # Manager user
        manager = User.create_user(
            email='<EMAIL>',
            password='password123',
            first_name='Manager',
            last_name='User',
            tenant_id=sample_tenant.id,
            role='manager'
        )
        
        assert manager.is_admin() is False
        assert manager.is_manager() is True
        assert manager.can_access_reports() is True
        assert manager.can_manage_inventory() is True
        assert manager.can_process_transactions() is True
        
        # Cashier user
        cashier = User.create_user(
            email='<EMAIL>',
            password='password123',
            first_name='Cashier',
            last_name='User',
            tenant_id=sample_tenant.id,
            role='cashier'
        )
        
        assert cashier.is_admin() is False
        assert cashier.is_manager() is False
        assert cashier.can_access_reports() is False
        assert cashier.can_manage_inventory() is False
        assert cashier.can_process_transactions() is True
        
        # Regular user
        regular = User.create_user(
            email='<EMAIL>',
            password='password123',
            first_name='Regular',
            last_name='User',
            tenant_id=sample_tenant.id,
            role='user'
        )
        
        assert regular.is_admin() is False
        assert regular.is_manager() is False
        assert regular.can_access_reports() is False
        assert regular.can_manage_inventory() is False
        assert regular.can_process_transactions() is False
    
    def test_user_authentication(self, app_context, sample_tenant):
        """Test user authentication."""
        # Create user
        user = User.create_user(
            email='<EMAIL>',
            password='correctpassword',
            first_name='Auth',
            last_name='Test',
            tenant_id=sample_tenant.id
        )
        
        # Successful authentication
        authenticated_user = User.authenticate('<EMAIL>', 'correctpassword', sample_tenant.id)
        assert authenticated_user is not None
        assert authenticated_user.id == user.id
        assert authenticated_user.login_count == 1
        assert authenticated_user.last_login is not None
        
        # Failed authentication - wrong password
        failed_auth = User.authenticate('<EMAIL>', 'wrongpassword', sample_tenant.id)
        assert failed_auth is None
        
        # Failed authentication - wrong tenant
        other_tenant = Tenant.create(name='Other Tenant', business_type='retail')
        failed_tenant_auth = User.authenticate('<EMAIL>', 'correctpassword', other_tenant.id)
        assert failed_tenant_auth is None
        
        # Failed authentication - non-existent user
        no_user_auth = User.authenticate('<EMAIL>', 'password', sample_tenant.id)
        assert no_user_auth is None
    
    def test_account_locking(self, app_context, sample_tenant):
        """Test account locking after failed login attempts."""
        user = User.create_user(
            email='<EMAIL>',
            password='password123',
            first_name='Lock',
            last_name='Test',
            tenant_id=sample_tenant.id
        )
        
        # Initially not locked
        assert user.is_account_locked() is False
        
        # Make 4 failed attempts
        for i in range(4):
            User.authenticate('<EMAIL>', 'wrongpassword', sample_tenant.id)
            user = User.query.get(user.id)  # Refresh from database
            assert user.is_account_locked() is False
        
        # 5th failed attempt should lock the account
        User.authenticate('<EMAIL>', 'wrongpassword', sample_tenant.id)
        user = User.query.get(user.id)  # Refresh from database
        assert user.is_account_locked() is True
        assert user.failed_login_attempts == 5
        assert user.locked_until is not None
        
        # Even correct password should fail when locked
        locked_auth = User.authenticate('<EMAIL>', 'password123', sample_tenant.id)
        assert locked_auth is None
        
        # Unlock account
        user.unlock_account()
        assert user.is_account_locked() is False
        assert user.failed_login_attempts == 0
        assert user.locked_until is None
        
        # Should be able to authenticate after unlock
        unlocked_auth = User.authenticate('<EMAIL>', 'password123', sample_tenant.id)
        assert unlocked_auth is not None
    
    def test_email_verification(self, app_context, sample_user):
        """Test email verification functionality."""
        # Initially not verified
        assert sample_user.email_verified is False
        
        # Verify email
        sample_user.verify_email()
        assert sample_user.email_verified is True
        assert sample_user.email_verification_token is None
    
    def test_password_reset(self, app_context, sample_user):
        """Test password reset functionality."""
        original_password_hash = sample_user.password_hash
        
        # Generate reset token
        token = sample_user.generate_password_reset_token()
        assert token is not None
        assert sample_user.password_reset_token == token
        assert sample_user.password_reset_expires is not None
        
        # Reset password with valid token
        result = sample_user.reset_password('newpassword123', token)
        assert result is True
        assert sample_user.password_hash != original_password_hash
        assert sample_user.check_password('newpassword123') is True
        assert sample_user.password_reset_token is None
        assert sample_user.password_reset_expires is None
        
        # Try to reset with invalid token
        sample_user.generate_password_reset_token()
        result = sample_user.reset_password('anotherpassword', 'invalidtoken')
        assert result is False
    
    def test_unique_email_per_tenant(self, app_context):
        """Test that email must be unique within a tenant."""
        tenant1 = Tenant.create(name='Tenant 1', business_type='retail')
        tenant2 = Tenant.create(name='Tenant 2', business_type='restaurant')
        
        # Create user in tenant 1
        user1 = User.create_user(
            email='<EMAIL>',
            password='password123',
            first_name='User',
            last_name='One',
            tenant_id=tenant1.id
        )
        
        # Should be able to create user with same email in different tenant
        user2 = User.create_user(
            email='<EMAIL>',
            password='password123',
            first_name='User',
            last_name='Two',
            tenant_id=tenant2.id
        )
        
        assert user1.email == user2.email
        assert user1.tenant_id != user2.tenant_id
        
        # Should not be able to create user with same email in same tenant
        with pytest.raises(Exception):  # Should raise IntegrityError
            User.create_user(
                email='<EMAIL>',
                password='password123',
                first_name='User',
                last_name='Three',
                tenant_id=tenant1.id
            )