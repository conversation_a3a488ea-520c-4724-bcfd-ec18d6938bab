"""Backup service for SaaS POS System."""

import os
import json
import gzip
import hashlib
import subprocess
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any
from flask import current_app
from sqlalchemy import text
from app import db
from app.models.audit import BackupLog, AuditLog, AuditAction, AuditSeverity


class BackupService:
    """Service for handling database backups and recovery."""
    
    def __init__(self):
        self.backup_dir = self._get_backup_directory()
        self._ensure_backup_directory()
    
    def _get_backup_directory(self) -> str:
        """Get the backup directory path."""
        backup_dir = os.environ.get('BACKUP_DIR', 'backups')
        if not os.path.isabs(backup_dir):
            backup_dir = os.path.join(os.getcwd(), backup_dir)
        return backup_dir
    
    def _ensure_backup_directory(self):
        """Ensure backup directory exists."""
        os.makedirs(self.backup_dir, exist_ok=True)
    
    def _generate_filename(self, backup_type: str, tenant_id: Optional[int] = None) -> str:
        """Generate backup filename."""
        timestamp = datetime.utcnow().strftime('%Y%m%d_%H%M%S')
        
        if tenant_id:
            return f"backup_{backup_type}_tenant_{tenant_id}_{timestamp}.sql.gz"
        else:
            return f"backup_{backup_type}_full_{timestamp}.sql.gz"
    
    def _calculate_checksum(self, filepath: str) -> str:
        """Calculate SHA-256 checksum of a file."""
        sha256_hash = hashlib.sha256()
        
        with open(filepath, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                sha256_hash.update(chunk)
        
        return sha256_hash.hexdigest()
    
    def _get_database_url(self) -> str:
        """Get database URL from configuration."""
        return current_app.config.get('SQLALCHEMY_DATABASE_URI', '')
    
    def _is_sqlite_database(self) -> bool:
        """Check if the database is SQLite."""
        return self._get_database_url().startswith('sqlite')
    
    def _is_postgresql_database(self) -> bool:
        """Check if the database is PostgreSQL."""
        db_url = self._get_database_url()
        return db_url.startswith('postgresql') or db_url.startswith('postgres')
    
    def create_full_backup(self, tenant_id: Optional[int] = None) -> BackupLog:
        """Create a full database backup."""
        
        backup_type = 'full'
        filename = self._generate_filename(backup_type, tenant_id)
        filepath = os.path.join(self.backup_dir, filename)
        
        # Create backup log entry
        backup_log = BackupLog(
            backup_type=backup_type,
            backup_filename=filename,
            backup_path=filepath,
            tenant_id=tenant_id,
            started_at=datetime.utcnow()
        )
        backup_log.save()
        
        try:
            if self._is_sqlite_database():
                self._backup_sqlite(filepath, tenant_id)
            elif self._is_postgresql_database():
                self._backup_postgresql(filepath, tenant_id)
            else:
                raise ValueError("Unsupported database type")
            
            # Calculate file size and checksum
            file_size = os.path.getsize(filepath)
            checksum = self._calculate_checksum(filepath)
            
            # Get record count
            records_count = self._count_records(tenant_id)
            
            # Mark backup as completed
            backup_log.mark_completed(
                backup_size=file_size,
                records_count=records_count,
                checksum=checksum
            )
            
            # Log the backup action
            AuditLog.log_action(
                action=AuditAction.BACKUP_CREATED,
                tenant_id=tenant_id or 0,  # Use 0 for system-wide backups
                description=f"Full backup created: {filename}",
                severity=AuditSeverity.MEDIUM,
                resource_type='backup',
                resource_id=str(backup_log.id),
                new_values={
                    'backup_type': backup_type,
                    'file_size': file_size,
                    'records_count': records_count
                }
            )
            
            return backup_log
            
        except Exception as e:
            backup_log.mark_failed(str(e), {'exception_type': type(e).__name__})
            
            # Log the backup failure
            AuditLog.log_action(
                action=AuditAction.BACKUP_CREATED,
                tenant_id=tenant_id or 0,
                description=f"Backup failed: {str(e)}",
                severity=AuditSeverity.HIGH,
                success=False,
                error_message=str(e),
                resource_type='backup',
                resource_id=str(backup_log.id)
            )
            
            raise
    
    def _backup_sqlite(self, filepath: str, tenant_id: Optional[int] = None):
        """Create SQLite backup."""
        
        # Get database path from URL
        db_url = self._get_database_url()
        db_path = db_url.replace('sqlite:///', '')
        
        if tenant_id:
            # Create tenant-specific backup using SQL dump
            self._backup_sqlite_tenant(filepath, tenant_id)
        else:
            # Full database backup
            with open(filepath.replace('.gz', ''), 'w') as f:
                # Use sqlite3 command to dump database
                result = subprocess.run([
                    'sqlite3', db_path, '.dump'
                ], capture_output=True, text=True, check=True)
                
                f.write(result.stdout)
            
            # Compress the backup
            with open(filepath.replace('.gz', ''), 'rb') as f_in:
                with gzip.open(filepath, 'wb') as f_out:
                    f_out.writelines(f_in)
            
            # Remove uncompressed file
            os.remove(filepath.replace('.gz', ''))
    
    def _backup_sqlite_tenant(self, filepath: str, tenant_id: int):
        """Create tenant-specific SQLite backup."""
        
        # Get all tables that have tenant_id column
        tenant_tables = self._get_tenant_tables()
        
        sql_dump = []
        
        # Add header
        sql_dump.append("-- Tenant-specific backup")
        sql_dump.append(f"-- Tenant ID: {tenant_id}")
        sql_dump.append(f"-- Created: {datetime.utcnow().isoformat()}")
        sql_dump.append("")
        
        for table_name in tenant_tables:
            # Get table schema
            schema_result = db.session.execute(text(f"SELECT sql FROM sqlite_master WHERE name = '{table_name}'"))
            schema = schema_result.fetchone()
            
            if schema:
                sql_dump.append(f"-- Table: {table_name}")
                sql_dump.append(schema[0] + ";")
                sql_dump.append("")
                
                # Get table data for this tenant
                data_result = db.session.execute(text(f"SELECT * FROM {table_name} WHERE tenant_id = :tenant_id"), 
                                                {'tenant_id': tenant_id})
                
                rows = data_result.fetchall()
                if rows:
                    # Get column names
                    columns = data_result.keys()
                    
                    for row in rows:
                        values = []
                        for value in row:
                            if value is None:
                                values.append('NULL')
                            elif isinstance(value, str):
                                escaped_value = value.replace("'", "''")
                                values.append(f"'{escaped_value}'")
                            else:
                                values.append(str(value))
                        
                        sql_dump.append(f"INSERT INTO {table_name} ({', '.join(columns)}) VALUES ({', '.join(values)});")
                
                sql_dump.append("")
        
        # Write and compress
        with gzip.open(filepath, 'wt') as f:
            f.write('\n'.join(sql_dump))
    
    def _backup_postgresql(self, filepath: str, tenant_id: Optional[int] = None):
        """Create PostgreSQL backup using pg_dump."""
        
        db_url = self._get_database_url()
        
        # Parse PostgreSQL URL
        # Format: postgresql://user:password@host:port/database
        import urllib.parse
        parsed = urllib.parse.urlparse(db_url)
        
        env = os.environ.copy()
        if parsed.password:
            env['PGPASSWORD'] = parsed.password
        
        cmd = [
            'pg_dump',
            '-h', parsed.hostname or 'localhost',
            '-p', str(parsed.port or 5432),
            '-U', parsed.username or 'postgres',
            '-d', parsed.path.lstrip('/'),
            '--no-password',
            '--verbose'
        ]
        
        if tenant_id:
            # Add tenant-specific filtering
            tenant_tables = self._get_tenant_tables()
            for table in tenant_tables:
                cmd.extend(['--table', table])
        
        # Run pg_dump and compress output
        with gzip.open(filepath, 'wb') as f:
            result = subprocess.run(cmd, stdout=subprocess.PIPE, env=env, check=True)
            f.write(result.stdout)
    
    def _get_tenant_tables(self) -> List[str]:
        """Get list of tables that have tenant_id column."""
        
        if self._is_sqlite_database():
            result = db.session.execute(text("""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name NOT LIKE 'sqlite_%'
            """))
            
            tables = []
            for row in result:
                table_name = row[0]
                # Check if table has tenant_id column
                try:
                    db.session.execute(text(f"SELECT tenant_id FROM {table_name} LIMIT 1"))
                    tables.append(table_name)
                except:
                    pass  # Table doesn't have tenant_id column
            
            return tables
        
        elif self._is_postgresql_database():
            result = db.session.execute(text("""
                SELECT table_name 
                FROM information_schema.columns 
                WHERE column_name = 'tenant_id' 
                AND table_schema = 'public'
            """))
            
            return [row[0] for row in result]
        
        return []
    
    def _count_records(self, tenant_id: Optional[int] = None) -> int:
        """Count total records in backup."""
        
        if tenant_id:
            # Count records for specific tenant
            tenant_tables = self._get_tenant_tables()
            total_count = 0
            
            for table in tenant_tables:
                try:
                    result = db.session.execute(text(f"SELECT COUNT(*) FROM {table} WHERE tenant_id = :tenant_id"), 
                                              {'tenant_id': tenant_id})
                    count = result.fetchone()[0]
                    total_count += count
                except:
                    pass  # Skip tables that might not exist or have issues
            
            return total_count
        
        else:
            # Count all records
            if self._is_sqlite_database():
                result = db.session.execute(text("""
                    SELECT SUM(count) FROM (
                        SELECT COUNT(*) as count FROM sqlite_master WHERE type='table'
                        UNION ALL
                        SELECT COUNT(*) FROM user
                        UNION ALL
                        SELECT COUNT(*) FROM tenant
                    )
                """))
            else:
                # For PostgreSQL, get approximate count
                result = db.session.execute(text("""
                    SELECT SUM(n_tup_ins + n_tup_upd) 
                    FROM pg_stat_user_tables
                """))
            
            count = result.fetchone()
            return count[0] if count and count[0] else 0
    
    def restore_backup(self, backup_log_id: int, target_tenant_id: Optional[int] = None) -> bool:
        """Restore from a backup."""
        
        backup_log = BackupLog.query.get(backup_log_id)
        if not backup_log:
            raise ValueError(f"Backup log {backup_log_id} not found")
        
        if backup_log.backup_status != 'completed':
            raise ValueError(f"Backup {backup_log_id} is not completed")
        
        if not os.path.exists(backup_log.backup_path):
            raise ValueError(f"Backup file {backup_log.backup_path} not found")
        
        try:
            # Verify backup integrity
            current_checksum = self._calculate_checksum(backup_log.backup_path)
            if backup_log.checksum and current_checksum != backup_log.checksum:
                raise ValueError("Backup file integrity check failed")
            
            # Perform restore
            if self._is_sqlite_database():
                self._restore_sqlite(backup_log, target_tenant_id)
            elif self._is_postgresql_database():
                self._restore_postgresql(backup_log, target_tenant_id)
            else:
                raise ValueError("Unsupported database type")
            
            # Log the restore action
            AuditLog.log_action(
                action=AuditAction.BACKUP_RESTORED,
                tenant_id=target_tenant_id or backup_log.tenant_id or 0,
                description=f"Backup restored from {backup_log.backup_filename}",
                severity=AuditSeverity.HIGH,
                resource_type='backup',
                resource_id=str(backup_log.id),
                new_values={
                    'source_backup_id': backup_log_id,
                    'target_tenant_id': target_tenant_id
                }
            )
            
            return True
            
        except Exception as e:
            # Log the restore failure
            AuditLog.log_action(
                action=AuditAction.BACKUP_RESTORED,
                tenant_id=target_tenant_id or backup_log.tenant_id or 0,
                description=f"Backup restore failed: {str(e)}",
                severity=AuditSeverity.CRITICAL,
                success=False,
                error_message=str(e),
                resource_type='backup',
                resource_id=str(backup_log.id)
            )
            
            raise
    
    def _restore_sqlite(self, backup_log: BackupLog, target_tenant_id: Optional[int] = None):
        """Restore SQLite backup."""
        
        # Read and decompress backup
        with gzip.open(backup_log.backup_path, 'rt') as f:
            sql_content = f.read()
        
        # Execute SQL statements
        statements = sql_content.split(';')
        
        for statement in statements:
            statement = statement.strip()
            if statement and not statement.startswith('--'):
                try:
                    db.session.execute(text(statement))
                except Exception as e:
                    # Log but continue with other statements
                    print(f"Warning: Failed to execute statement: {statement[:100]}... Error: {e}")
        
        db.session.commit()
    
    def _restore_postgresql(self, backup_log: BackupLog, target_tenant_id: Optional[int] = None):
        """Restore PostgreSQL backup using psql."""
        
        db_url = self._get_database_url()
        
        # Parse PostgreSQL URL
        import urllib.parse
        parsed = urllib.parse.urlparse(db_url)
        
        env = os.environ.copy()
        if parsed.password:
            env['PGPASSWORD'] = parsed.password
        
        # Decompress backup to temporary file
        temp_file = backup_log.backup_path.replace('.gz', '.temp')
        
        with gzip.open(backup_log.backup_path, 'rb') as f_in:
            with open(temp_file, 'wb') as f_out:
                f_out.write(f_in.read())
        
        try:
            cmd = [
                'psql',
                '-h', parsed.hostname or 'localhost',
                '-p', str(parsed.port or 5432),
                '-U', parsed.username or 'postgres',
                '-d', parsed.path.lstrip('/'),
                '-f', temp_file
            ]
            
            subprocess.run(cmd, env=env, check=True)
            
        finally:
            # Clean up temporary file
            if os.path.exists(temp_file):
                os.remove(temp_file)
    
    def schedule_automated_backup(self, backup_type: str = 'full', tenant_id: Optional[int] = None):
        """Schedule automated backup (to be called by cron job or scheduler)."""
        
        try:
            backup_log = self.create_full_backup(tenant_id)
            
            # Clean up old backups based on retention policy
            self.cleanup_old_backups()
            
            return backup_log
            
        except Exception as e:
            # Log the scheduling failure
            AuditLog.log_action(
                action=AuditAction.BACKUP_CREATED,
                tenant_id=tenant_id or 0,
                description=f"Scheduled backup failed: {str(e)}",
                severity=AuditSeverity.CRITICAL,
                success=False,
                error_message=str(e)
            )
            
            raise
    
    def cleanup_old_backups(self, retention_days: int = 30):
        """Clean up old backup files and logs."""
        
        deleted_count = BackupLog.cleanup_old_backups(retention_days)
        
        # Log cleanup action
        AuditLog.log_action(
            action=AuditAction.SETTINGS_CHANGED,
            tenant_id=0,  # System-wide action
            description=f"Cleaned up {deleted_count} old backup records",
            severity=AuditSeverity.LOW,
            resource_type='backup',
            new_values={
                'retention_days': retention_days,
                'deleted_count': deleted_count
            }
        )
        
        return deleted_count
    
    def verify_backup(self, backup_log_id: int) -> bool:
        """Verify backup integrity."""
        
        backup_log = BackupLog.query.get(backup_log_id)
        if not backup_log:
            raise ValueError(f"Backup log {backup_log_id} not found")
        
        if not os.path.exists(backup_log.backup_path):
            return False
        
        current_checksum = self._calculate_checksum(backup_log.backup_path)
        return backup_log.verify_backup(current_checksum)
    
    def get_backup_status(self) -> Dict[str, Any]:
        """Get overall backup system status."""
        
        recent_backups = BackupLog.get_recent_backups(limit=5)
        
        # Calculate success rate
        total_backups = BackupLog.query.count()
        successful_backups = BackupLog.query.filter_by(backup_status='completed').count()
        success_rate = (successful_backups / total_backups * 100) if total_backups > 0 else 0
        
        # Get last successful backup
        last_successful = BackupLog.query.filter_by(backup_status='completed').order_by(BackupLog.completed_at.desc()).first()
        
        # Calculate total backup size
        total_size = db.session.query(db.func.sum(BackupLog.backup_size)).filter_by(backup_status='completed').scalar() or 0
        
        return {
            'total_backups': total_backups,
            'successful_backups': successful_backups,
            'success_rate': round(success_rate, 2),
            'last_successful_backup': last_successful.completed_at.isoformat() if last_successful else None,
            'total_backup_size_bytes': total_size,
            'total_backup_size_mb': round(total_size / (1024 * 1024), 2),
            'recent_backups': [
                {
                    'id': backup.id,
                    'type': backup.backup_type,
                    'status': backup.backup_status,
                    'created_at': backup.created_at.isoformat(),
                    'completed_at': backup.completed_at.isoformat() if backup.completed_at else None,
                    'size_mb': round(backup.backup_size / (1024 * 1024), 2) if backup.backup_size else 0,
                    'tenant_id': backup.tenant_id
                }
                for backup in recent_backups
            ]
        }