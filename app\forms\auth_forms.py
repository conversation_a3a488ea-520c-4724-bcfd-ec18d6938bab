"""Authentication forms for SaaS POS System."""

from flask_wtf import FlaskForm
from wtforms import <PERSON><PERSON><PERSON>, PasswordField, IntegerField, BooleanField, SelectField
from wtforms.validators import DataRequired, Length, EqualTo, Optional
from app.utils.validators import (
    validate_email, validate_password_strength, validate_phone_number,
    validate_business_name, validate_name_field, validate_tenant_id
)


class LoginForm(FlaskForm):
    """User login form."""
    email = StringField('Email', validators=[
        DataRequired(message='Email is required'),
        validate_email
    ])
    password = PasswordField('Password', validators=[
        DataRequired(message='Password is required')
    ])
    business_name = StringField('Business Name', validators=[
        DataRequired(message='Business name is required'),
        Length(min=2, max=100, message='Business name must be between 2 and 100 characters')
    ])
    remember_me = BooleanField('Remember Me')


class RegisterForm(FlaskForm):
    """User registration form."""
    email = StringField('Email', validators=[
        DataRequired(message='Email is required'),
        validate_email
    ])
    password = PasswordField('Password', validators=[
        DataRequired(message='Password is required'),
        validate_password_strength
    ])
    confirm_password = PasswordField('Confirm Password', validators=[
        DataRequired(message='Please confirm your password'),
        EqualTo('password', message='Passwords must match')
    ])
    first_name = StringField('First Name', validators=[
        DataRequired(message='First name is required'),
        validate_name_field
    ])
    last_name = StringField('Last Name', validators=[
        DataRequired(message='Last name is required'),
        validate_name_field
    ])
    tenant_name = StringField('Business Name', validators=[
        DataRequired(message='Business name is required'),
        validate_business_name
    ])
    business_type = SelectField('Business Type', choices=[
        ('retail', 'Retail'),
        ('restaurant', 'Restaurant'),
        ('service', 'Service'),
        ('other', 'Other')
    ], default='retail')


class ForgotPasswordForm(FlaskForm):
    """Forgot password form."""
    email = StringField('Email', validators=[
        DataRequired(message='Email is required'),
        validate_email
    ])
    business_name = StringField('Business Name', validators=[
        DataRequired(message='Business name is required'),
        Length(min=2, max=100, message='Business name must be between 2 and 100 characters')
    ])


class ResetPasswordForm(FlaskForm):
    """Reset password form."""
    password = PasswordField('New Password', validators=[
        DataRequired(message='Password is required'),
        validate_password_strength
    ])
    confirm_password = PasswordField('Confirm Password', validators=[
        DataRequired(message='Please confirm your password'),
        EqualTo('password', message='Passwords must match')
    ])


class ChangePasswordForm(FlaskForm):
    """Change password form."""
    current_password = PasswordField('Current Password', validators=[
        DataRequired(message='Current password is required')
    ])
    new_password = PasswordField('New Password', validators=[
        DataRequired(message='New password is required'),
        validate_password_strength
    ])
    confirm_password = PasswordField('Confirm New Password', validators=[
        DataRequired(message='Please confirm your new password'),
        EqualTo('new_password', message='Passwords must match')
    ])


class ProfileForm(FlaskForm):
    """User profile form."""
    first_name = StringField('First Name', validators=[
        DataRequired(message='First name is required'),
        validate_name_field
    ])
    last_name = StringField('Last Name', validators=[
        DataRequired(message='Last name is required'),
        validate_name_field
    ])
    phone = StringField('Phone Number', validators=[
        Optional(),
        validate_phone_number
    ])