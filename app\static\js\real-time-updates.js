/**
 * Real-time updates for SaaS POS System
 * Handles stock level updates, loading indicators, and smooth transitions
 */

class RealTimeUpdater {
    constructor(options = {}) {
        this.options = {
            stockUpdateInterval: 30000, // 30 seconds
            enableStockUpdates: true,
            enableLoadingIndicators: true,
            apiBaseUrl: '/api',
            ...options
        };
        
        this.stockUpdateTimer = null;
        this.loadingElements = new Set();
        this.init();
    }
    
    init() {
        if (this.options.enableStockUpdates) {
            this.initStockUpdates();
        }
        
        if (this.options.enableLoadingIndicators) {
            this.initLoadingIndicators();
        }
        
        this.initTransitions();
    }
    
    // Stock Level Updates
    initStockUpdates() {
        // Start periodic stock updates
        this.startStockUpdates();
        
        // Update stock when page becomes visible
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden) {
                this.updateStockLevels();
            }
        });
        
        // Listen for stock update events from other parts of the app
        document.addEventListener('stockChanged', (e) => {
            this.updateProductStock(e.detail.productId, e.detail.newStock);
        });
    }
    
    startStockUpdates() {
        if (this.stockUpdateTimer) {
            clearInterval(this.stockUpdateTimer);
        }
        
        this.stockUpdateTimer = setInterval(() => {
            this.updateStockLevels();
        }, this.options.stockUpdateInterval);
        
        // Initial update
        this.updateStockLevels();
    }
    
    stopStockUpdates() {
        if (this.stockUpdateTimer) {
            clearInterval(this.stockUpdateTimer);
            this.stockUpdateTimer = null;
        }
    }
    
    async updateStockLevels() {
        try {
            const stockElements = document.querySelectorAll('[data-stock-id]');
            if (stockElements.length === 0) return;
            
            const productIds = Array.from(stockElements).map(el => el.dataset.stockId);
            
            const response = await fetch(`${this.options.apiBaseUrl}/stock/levels`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify({ product_ids: productIds })
            });
            
            if (response.ok) {
                const stockData = await response.json();
                this.updateStockDisplay(stockData);
            }
        } catch (error) {
            console.warn('Failed to update stock levels:', error);
        }
    }
    
    updateStockDisplay(stockData) {
        Object.entries(stockData).forEach(([productId, stockInfo]) => {
            this.updateProductStock(productId, stockInfo.current_stock, stockInfo);
        });
    }
    
    updateProductStock(productId, newStock, stockInfo = {}) {
        const stockElements = document.querySelectorAll(`[data-stock-id="${productId}"]`);
        
        stockElements.forEach(element => {
            const oldStock = parseInt(element.textContent) || 0;
            
            // Update stock number with animation
            this.animateNumberChange(element, oldStock, newStock);
            
            // Update stock status classes
            this.updateStockStatus(element, newStock, stockInfo);
            
            // Update related elements (like product cards)
            this.updateProductAvailability(productId, newStock, stockInfo);
        });
    }
    
    animateNumberChange(element, oldValue, newValue) {
        if (oldValue === newValue) return;
        
        element.classList.add('stock-updating');
        
        // Simple number animation
        const duration = 500;
        const startTime = performance.now();
        const difference = newValue - oldValue;
        
        const animate = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            const currentValue = Math.round(oldValue + (difference * progress));
            element.textContent = currentValue;
            
            if (progress < 1) {
                requestAnimationFrame(animate);
            } else {
                element.classList.remove('stock-updating');
                
                // Add flash effect to indicate change
                element.classList.add('stock-changed');
                setTimeout(() => {
                    element.classList.remove('stock-changed');
                }, 1000);
            }
        };
        
        requestAnimationFrame(animate);
    }
    
    updateStockStatus(element, stock, stockInfo) {
        const container = element.closest('.product-card, .stock-container') || element.parentElement;
        
        // Remove existing status classes
        container.classList.remove('stock-low', 'stock-out', 'stock-normal');
        
        // Add appropriate status class
        if (stock <= 0) {
            container.classList.add('stock-out');
        } else if (stock <= (stockInfo.low_stock_threshold || 10)) {
            container.classList.add('stock-low');
        } else {
            container.classList.add('stock-normal');
        }
    }
    
    updateProductAvailability(productId, stock, stockInfo) {
        const productCards = document.querySelectorAll(`[data-product-id="${productId}"]`);
        
        productCards.forEach(card => {
            const isAvailable = stock > 0 || !stockInfo.track_inventory;
            
            if (isAvailable) {
                card.classList.remove('product-unavailable');
                card.classList.add('product-available');
                
                // Re-enable buttons
                const buttons = card.querySelectorAll('button');
                buttons.forEach(btn => {
                    btn.disabled = false;
                    btn.classList.remove('opacity-50', 'cursor-not-allowed');
                });
            } else {
                card.classList.remove('product-available');
                card.classList.add('product-unavailable');
                
                // Disable buttons
                const buttons = card.querySelectorAll('button');
                buttons.forEach(btn => {
                    btn.disabled = true;
                    btn.classList.add('opacity-50', 'cursor-not-allowed');
                });
            }
        });
    }
    
    // Loading Indicators
    initLoadingIndicators() {
        // Intercept fetch requests to show loading indicators
        this.interceptFetch();
        
        // Handle form submissions
        document.addEventListener('submit', (e) => {
            if (e.target.matches('form[data-loading]')) {
                this.showFormLoading(e.target);
            }
        });
        
        // Handle AJAX button clicks
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-loading-target]')) {
                this.showButtonLoading(e.target);
            }
        });
    }
    
    interceptFetch() {
        const originalFetch = window.fetch;
        
        window.fetch = async (...args) => {
            const loadingId = this.generateLoadingId();
            
            try {
                this.showGlobalLoading(loadingId);
                const response = await originalFetch(...args);
                return response;
            } finally {
                this.hideGlobalLoading(loadingId);
            }
        };
    }
    
    showFormLoading(form) {
        const submitButton = form.querySelector('button[type="submit"], input[type="submit"]');
        
        if (submitButton) {
            this.showButtonLoading(submitButton);
        }
        
        // Disable all form inputs
        const inputs = form.querySelectorAll('input, select, textarea, button');
        inputs.forEach(input => {
            input.disabled = true;
            input.dataset.wasDisabled = input.disabled;
        });
    }
    
    showButtonLoading(button) {
        if (button.dataset.loading === 'true') return;
        
        button.dataset.loading = 'true';
        button.dataset.originalText = button.textContent;
        button.disabled = true;
        
        // Add loading spinner
        const spinner = document.createElement('span');
        spinner.className = 'loading-spinner inline-block w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2';
        
        button.innerHTML = '';
        button.appendChild(spinner);
        button.appendChild(document.createTextNode(button.dataset.loadingText || 'Loading...'));
    }
    
    hideButtonLoading(button) {
        if (button.dataset.loading !== 'true') return;
        
        button.dataset.loading = 'false';
        button.disabled = false;
        button.textContent = button.dataset.originalText || button.textContent;
        
        delete button.dataset.originalText;
    }
    
    showGlobalLoading(id) {
        this.loadingElements.add(id);
        
        let globalLoader = document.getElementById('global-loader');
        if (!globalLoader) {
            globalLoader = this.createGlobalLoader();
            document.body.appendChild(globalLoader);
        }
        
        globalLoader.classList.remove('hidden');
    }
    
    hideGlobalLoading(id) {
        this.loadingElements.delete(id);
        
        if (this.loadingElements.size === 0) {
            const globalLoader = document.getElementById('global-loader');
            if (globalLoader) {
                globalLoader.classList.add('hidden');
            }
        }
    }
    
    createGlobalLoader() {
        const loader = document.createElement('div');
        loader.id = 'global-loader';
        loader.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden';
        loader.innerHTML = `
            <div class="bg-white rounded-lg p-6 flex items-center space-x-3">
                <div class="loading-spinner w-6 h-6 border-3 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
                <span class="text-gray-700">Loading...</span>
            </div>
        `;
        return loader;
    }
    
    generateLoadingId() {
        return `loading-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    }
    
    // Smooth Transitions
    initTransitions() {
        // Add smooth transitions to dynamic content
        this.observeContentChanges();
        
        // Handle page transitions
        this.initPageTransitions();
    }
    
    observeContentChanges() {
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach((node) => {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            this.applyEntranceAnimation(node);
                        }
                    });
                }
            });
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }
    
    applyEntranceAnimation(element) {
        // Skip if element already has animation classes
        if (element.classList.contains('animate-in')) return;
        
        // Apply fade-in animation to new elements
        if (element.matches('.product-card, .cart-item, .flash-message, .modal')) {
            element.classList.add('animate-in', 'fade-in');
            
            // Remove animation class after animation completes
            setTimeout(() => {
                element.classList.remove('animate-in', 'fade-in');
            }, 300);
        }
    }
    
    initPageTransitions() {
        // Add loading state to navigation links
        document.addEventListener('click', (e) => {
            if (e.target.matches('a[href]:not([href^="#"]):not([target="_blank"])')) {
                this.showPageTransition();
            }
        });
        
        // Hide loading state when page loads
        window.addEventListener('load', () => {
            this.hidePageTransition();
        });
    }
    
    showPageTransition() {
        document.body.classList.add('page-transitioning');
    }
    
    hidePageTransition() {
        document.body.classList.remove('page-transitioning');
    }
    
    // Public API
    updateStock(productId, newStock, stockInfo = {}) {
        this.updateProductStock(productId, newStock, stockInfo);
    }
    
    showLoading(target) {
        if (typeof target === 'string') {
            this.showGlobalLoading(target);
        } else if (target.tagName === 'BUTTON') {
            this.showButtonLoading(target);
        } else if (target.tagName === 'FORM') {
            this.showFormLoading(target);
        }
    }
    
    hideLoading(target) {
        if (typeof target === 'string') {
            this.hideGlobalLoading(target);
        } else if (target.tagName === 'BUTTON') {
            this.hideButtonLoading(target);
        }
    }
    
    destroy() {
        this.stopStockUpdates();
        this.loadingElements.clear();
    }
}

// Auto-initialize real-time updater
document.addEventListener('DOMContentLoaded', function() {
    // Check if we're on a page that needs real-time updates
    const needsUpdates = document.querySelector('[data-stock-id], [data-real-time]');
    
    if (needsUpdates) {
        window.realTimeUpdater = new RealTimeUpdater();
    }
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = RealTimeUpdater;
}

// Global access
window.RealTimeUpdater = RealTimeUpdater;