"""Database optimization utilities for SaaS POS System."""

from sqlalchemy import text, inspect
from sqlalchemy.orm import sessionmaker
from app import db
from app.models import *
import logging

logger = logging.getLogger(__name__)


class DatabaseOptimizer:
    """Database optimization utilities."""
    
    @staticmethod
    def analyze_query_performance():
        """Analyze query performance and suggest optimizations."""
        optimizations = []
        
        # Check for missing indexes on frequently queried columns
        missing_indexes = DatabaseOptimizer._check_missing_indexes()
        if missing_indexes:
            optimizations.extend(missing_indexes)
        
        # Check for slow queries (if using PostgreSQL)
        slow_queries = DatabaseOptimizer._check_slow_queries()
        if slow_queries:
            optimizations.extend(slow_queries)
        
        return optimizations
    
    @staticmethod
    def _check_missing_indexes():
        """Check for potentially missing indexes."""
        suggestions = []
        
        # Common query patterns that should have indexes
        index_suggestions = [
            {
                'table': 'transaction',
                'columns': ['tenant_id', 'created_at'],
                'reason': 'Frequent date range queries by tenant'
            },
            {
                'table': 'product',
                'columns': ['tenant_id', 'is_active', 'current_stock'],
                'reason': 'Low stock alerts and active product queries'
            },
            {
                'table': 'inventory_movement',
                'columns': ['tenant_id', 'movement_type', 'created_at'],
                'reason': 'Inventory audit and reporting queries'
            }
        ]
        
        for suggestion in index_suggestions:
            suggestions.append(f"Consider index on {suggestion['table']}({', '.join(suggestion['columns'])}) - {suggestion['reason']}")
        
        return suggestions
    
    @staticmethod
    def _check_slow_queries():
        """Check for slow queries (PostgreSQL specific)."""
        if 'postgresql' not in db.engine.url.drivername:
            return []
        
        try:
            # Enable pg_stat_statements if available
            slow_queries = db.session.execute(text("""
                SELECT query, calls, total_time, mean_time
                FROM pg_stat_statements
                WHERE mean_time > 100
                ORDER BY mean_time DESC
                LIMIT 10
            """)).fetchall()
            
            return [f"Slow query detected: {query[:100]}... (avg: {mean_time:.2f}ms)" 
                   for query, calls, total_time, mean_time in slow_queries]
        except Exception as e:
            logger.warning(f"Could not check slow queries: {e}")
            return []
    
    @staticmethod
    def optimize_connection_pool():
        """Optimize database connection pool settings."""
        engine = db.engine
        
        # Get current pool settings
        pool_size = getattr(engine.pool, 'size', lambda: 5)()
        max_overflow = getattr(engine.pool, 'max_overflow', lambda: 10)()
        
        recommendations = []
        
        if pool_size < 10:
            recommendations.append("Consider increasing pool_size to 10-20 for better concurrency")
        
        if max_overflow < 20:
            recommendations.append("Consider increasing max_overflow to 20-30 for peak load handling")
        
        return recommendations
    
    @staticmethod
    def create_materialized_views():
        """Create materialized views for common reporting queries."""
        if 'postgresql' not in db.engine.url.drivername:
            logger.info("Materialized views only supported on PostgreSQL")
            return
        
        # Daily sales summary view
        daily_sales_view = """
        CREATE MATERIALIZED VIEW IF NOT EXISTS daily_sales_summary AS
        SELECT 
            t.tenant_id,
            DATE(t.completed_at) as sale_date,
            COUNT(*) as transaction_count,
            SUM(t.total_amount) as total_sales,
            SUM(t.tax_amount) as total_tax,
            SUM(t.discount_amount) as total_discount
        FROM transaction t
        WHERE t.status = 'completed'
        GROUP BY t.tenant_id, DATE(t.completed_at);
        
        CREATE UNIQUE INDEX IF NOT EXISTS idx_daily_sales_tenant_date 
        ON daily_sales_summary(tenant_id, sale_date);
        """
        
        # Product performance view
        product_performance_view = """
        CREATE MATERIALIZED VIEW IF NOT EXISTS product_performance AS
        SELECT 
            p.tenant_id,
            p.id as product_id,
            p.name as product_name,
            COUNT(ti.id) as times_sold,
            SUM(ti.quantity) as total_quantity_sold,
            SUM(ti.line_total) as total_revenue,
            AVG(ti.unit_price) as avg_selling_price
        FROM product p
        LEFT JOIN transaction_item ti ON p.id = ti.product_id
        LEFT JOIN transaction t ON ti.transaction_id = t.id
        WHERE t.status = 'completed' OR t.status IS NULL
        GROUP BY p.tenant_id, p.id, p.name;
        
        CREATE UNIQUE INDEX IF NOT EXISTS idx_product_performance_tenant_product
        ON product_performance(tenant_id, product_id);
        """
        
        try:
            db.session.execute(text(daily_sales_view))
            db.session.execute(text(product_performance_view))
            db.session.commit()
            logger.info("Materialized views created successfully")
        except Exception as e:
            db.session.rollback()
            logger.error(f"Error creating materialized views: {e}")
    
    @staticmethod
    def refresh_materialized_views():
        """Refresh materialized views with latest data."""
        if 'postgresql' not in db.engine.url.drivername:
            return
        
        try:
            db.session.execute(text("REFRESH MATERIALIZED VIEW CONCURRENTLY daily_sales_summary"))
            db.session.execute(text("REFRESH MATERIALIZED VIEW CONCURRENTLY product_performance"))
            db.session.commit()
            logger.info("Materialized views refreshed successfully")
        except Exception as e:
            db.session.rollback()
            logger.error(f"Error refreshing materialized views: {e}")
    
    @staticmethod
    def vacuum_analyze_tables():
        """Run VACUUM ANALYZE on all tables (PostgreSQL)."""
        if 'postgresql' not in db.engine.url.drivername:
            return
        
        tables = ['tenant', 'user', 'product', 'category', 'transaction', 
                 'transaction_item', 'inventory_movement', 'business_settings']
        
        try:
            for table in tables:
                db.session.execute(text(f"VACUUM ANALYZE {table}"))
            logger.info("VACUUM ANALYZE completed for all tables")
        except Exception as e:
            logger.error(f"Error running VACUUM ANALYZE: {e}")
    
    @staticmethod
    def get_table_statistics():
        """Get table size and row count statistics."""
        inspector = inspect(db.engine)
        tables = inspector.get_table_names()
        
        stats = {}
        for table in tables:
            try:
                # Get row count
                result = db.session.execute(text(f"SELECT COUNT(*) FROM {table}")).scalar()
                stats[table] = {'row_count': result}
                
                # Get table size (PostgreSQL specific)
                if 'postgresql' in db.engine.url.drivername:
                    size_result = db.session.execute(text(f"SELECT pg_size_pretty(pg_total_relation_size('{table}'))")).scalar()
                    stats[table]['size'] = size_result
                    
            except Exception as e:
                logger.warning(f"Could not get stats for table {table}: {e}")
                stats[table] = {'error': str(e)}
        
        return stats


class QueryOptimizer:
    """Query optimization helpers."""
    
    @staticmethod
    def get_optimized_product_query(tenant_id, filters=None):
        """Get optimized product query with proper indexing."""
        query = Product.query.filter_by(tenant_id=tenant_id, is_active=True)
        
        if filters:
            if filters.get('category_id'):
                query = query.filter_by(category_id=filters['category_id'])
            
            if filters.get('low_stock'):
                query = query.filter(
                    Product.track_inventory == True,
                    Product.current_stock <= Product.minimum_stock
                )
            
            if filters.get('search'):
                search_term = f"%{filters['search']}%"
                query = query.filter(
                    db.or_(
                        Product.name.ilike(search_term),
                        Product.sku.ilike(search_term),
                        Product.barcode.ilike(search_term)
                    )
                )
        
        return query.order_by(Product.sort_order, Product.name)
    
    @staticmethod
    def get_optimized_transaction_query(tenant_id, filters=None):
        """Get optimized transaction query with proper indexing."""
        query = Transaction.query.filter_by(tenant_id=tenant_id)
        
        if filters:
            if filters.get('status'):
                query = query.filter_by(status=filters['status'])
            
            if filters.get('date_from'):
                query = query.filter(Transaction.completed_at >= filters['date_from'])
            
            if filters.get('date_to'):
                query = query.filter(Transaction.completed_at <= filters['date_to'])
            
            if filters.get('user_id'):
                query = query.filter_by(user_id=filters['user_id'])
        
        return query.order_by(Transaction.completed_at.desc())
    
    @staticmethod
    def get_sales_summary_optimized(tenant_id, start_date, end_date):
        """Get optimized sales summary using raw SQL for better performance."""
        sql = text("""
            SELECT 
                COUNT(*) as transaction_count,
                COALESCE(SUM(total_amount), 0) as total_sales,
                COALESCE(SUM(tax_amount), 0) as total_tax,
                COALESCE(SUM(discount_amount), 0) as total_discount,
                COALESCE(AVG(total_amount), 0) as avg_transaction_value
            FROM transaction 
            WHERE tenant_id = :tenant_id 
                AND status = 'completed'
                AND completed_at BETWEEN :start_date AND :end_date
        """)
        
        result = db.session.execute(sql, {
            'tenant_id': tenant_id,
            'start_date': start_date,
            'end_date': end_date
        }).fetchone()
        
        return {
            'transaction_count': result.transaction_count,
            'total_sales': float(result.total_sales),
            'total_tax': float(result.total_tax),
            'total_discount': float(result.total_discount),
            'avg_transaction_value': float(result.avg_transaction_value)
        }
    
    @staticmethod
    def get_top_products_optimized(tenant_id, start_date, end_date, limit=10):
        """Get top selling products using optimized query."""
        sql = text("""
            SELECT 
                p.id,
                p.name,
                p.sku,
                SUM(ti.quantity) as total_quantity,
                SUM(ti.line_total) as total_revenue,
                COUNT(DISTINCT t.id) as transaction_count
            FROM product p
            JOIN transaction_item ti ON p.id = ti.product_id
            JOIN transaction t ON ti.transaction_id = t.id
            WHERE p.tenant_id = :tenant_id
                AND t.status = 'completed'
                AND t.completed_at BETWEEN :start_date AND :end_date
            GROUP BY p.id, p.name, p.sku
            ORDER BY total_revenue DESC
            LIMIT :limit
        """)
        
        results = db.session.execute(sql, {
            'tenant_id': tenant_id,
            'start_date': start_date,
            'end_date': end_date,
            'limit': limit
        }).fetchall()
        
        return [{
            'product_id': r.id,
            'name': r.name,
            'sku': r.sku,
            'total_quantity': r.total_quantity,
            'total_revenue': float(r.total_revenue),
            'transaction_count': r.transaction_count
        } for r in results]