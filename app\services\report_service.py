"""Reporting service for SaaS POS System."""

from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List, Tuple
from decimal import Decimal
from flask import current_app
from sqlalchemy import and_, or_, func, desc, asc
from sqlalchemy.orm import joinedload

from app import db
from app.models.base import get_current_tenant
from app.models.transaction import Transaction, TransactionItem, TransactionStatus
from app.models.product import Product, Category
from app.models.user import User


class ReportError(Exception):
    """Base exception for reporting errors."""
    pass


class InvalidDateRangeError(ReportError):
    """Raised when date range is invalid."""
    pass


class ReportService:
    """Service class for generating reports and analytics."""
    
    @staticmethod
    def get_sales_summary(start_date: Optional[datetime] = None,
                         end_date: Optional[datetime] = None) -> Dict[str, Any]:
        """
        Get sales summary for a date range.
        
        Args:
            start_date: Start date for the report
            end_date: End date for the report
            
        Returns:
            Dict containing sales summary data
        """
        try:
            tenant_id = get_current_tenant()
            if not tenant_id:
                return {'success': False, 'error': 'No tenant context available'}
            
            # Set default date range if not provided (last 30 days)
            if not end_date:
                end_date = datetime.utcnow()
            if not start_date:
                start_date = end_date - timedelta(days=30)
            
            # Validate date range
            if start_date > end_date:
                raise InvalidDateRangeError("Start date cannot be after end date")
            
            # Get completed transactions in date range
            transactions = Transaction.query.filter(
                and_(
                    Transaction.tenant_id == tenant_id,
                    Transaction.status == TransactionStatus.COMPLETED,
                    Transaction.completed_at >= start_date,
                    Transaction.completed_at <= end_date
                )
            ).all()
            
            # Calculate summary metrics
            total_transactions = len(transactions)
            total_revenue = sum(t.total_amount for t in transactions)
            total_tax = sum(t.tax_amount for t in transactions)
            total_discount = sum(t.discount_amount for t in transactions)
            total_profit = sum(t.get_profit_amount() for t in transactions)
            total_items_sold = sum(t.get_item_count() for t in transactions)
            
            # Calculate averages
            avg_transaction_value = total_revenue / total_transactions if total_transactions > 0 else 0
            avg_items_per_transaction = total_items_sold / total_transactions if total_transactions > 0 else 0
            profit_margin = (total_profit / total_revenue * 100) if total_revenue > 0 else 0
            
            # Payment method breakdown
            payment_methods = {}
            for transaction in transactions:
                method = transaction.payment_method.value if transaction.payment_method else 'unknown'
                if method not in payment_methods:
                    payment_methods[method] = {'count': 0, 'amount': 0}
                payment_methods[method]['count'] += 1
                payment_methods[method]['amount'] += float(transaction.total_amount)
            
            return {
                'success': True,
                'date_range': {
                    'start_date': start_date.isoformat(),
                    'end_date': end_date.isoformat(),
                    'days': (end_date - start_date).days + 1
                },
                'summary': {
                    'total_transactions': total_transactions,
                    'total_revenue': float(total_revenue),
                    'total_tax': float(total_tax),
                    'total_discount': float(total_discount),
                    'total_profit': float(total_profit),
                    'total_items_sold': total_items_sold,
                    'avg_transaction_value': round(float(avg_transaction_value), 2),
                    'avg_items_per_transaction': round(avg_items_per_transaction, 2),
                    'profit_margin': round(profit_margin, 2)
                },
                'payment_methods': payment_methods
            }
            
        except InvalidDateRangeError as e:
            return {'success': False, 'error': str(e)}
        except Exception as e:
            current_app.logger.error(f"Sales summary error: {str(e)}")
            return {'success': False, 'error': 'An error occurred while generating sales summary'}
    
    @staticmethod
    def get_daily_sales_trend(start_date: Optional[datetime] = None,
                            end_date: Optional[datetime] = None) -> Dict[str, Any]:
        """
        Get daily sales trend data for charting.
        
        Args:
            start_date: Start date for the report
            end_date: End date for the report
            
        Returns:
            Dict containing daily sales trend data
        """
        try:
            tenant_id = get_current_tenant()
            if not tenant_id:
                return {'success': False, 'error': 'No tenant context available'}
            
            # Set default date range if not provided (last 30 days)
            if not end_date:
                end_date = datetime.utcnow()
            if not start_date:
                start_date = end_date - timedelta(days=30)
            
            # Validate date range
            if start_date > end_date:
                raise InvalidDateRangeError("Start date cannot be after end date")
            
            # Query daily sales data
            daily_sales = db.session.query(
                func.date(Transaction.completed_at).label('date'),
                func.count(Transaction.id).label('transaction_count'),
                func.sum(Transaction.total_amount).label('total_revenue'),
                func.sum(Transaction.tax_amount).label('total_tax'),
                func.sum(Transaction.discount_amount).label('total_discount'),
                func.sum(Transaction.subtotal - Transaction.discount_amount).label('net_sales')
            ).filter(
                and_(
                    Transaction.tenant_id == tenant_id,
                    Transaction.status == TransactionStatus.COMPLETED,
                    Transaction.completed_at >= start_date,
                    Transaction.completed_at <= end_date
                )
            ).group_by(
                func.date(Transaction.completed_at)
            ).order_by(
                func.date(Transaction.completed_at)
            ).all()
            
            # Format data for charting
            trend_data = []
            for row in daily_sales:
                # Handle date conversion properly
                date_str = row.date.isoformat() if hasattr(row.date, 'isoformat') else str(row.date)
                trend_data.append({
                    'date': date_str,
                    'transaction_count': int(row.transaction_count or 0),
                    'total_revenue': float(row.total_revenue or 0),
                    'total_tax': float(row.total_tax or 0),
                    'total_discount': float(row.total_discount or 0),
                    'net_sales': float(row.net_sales or 0)
                })
            
            return {
                'success': True,
                'date_range': {
                    'start_date': start_date.isoformat(),
                    'end_date': end_date.isoformat(),
                    'days': (end_date - start_date).days + 1
                },
                'trend_data': trend_data
            }
            
        except InvalidDateRangeError as e:
            return {'success': False, 'error': str(e)}
        except Exception as e:
            current_app.logger.error(f"Daily sales trend error: {str(e)}")
            return {'success': False, 'error': 'An error occurred while generating daily sales trend'}
    
    @staticmethod
    def get_product_performance_report(start_date: Optional[datetime] = None,
                                     end_date: Optional[datetime] = None,
                                     limit: int = 50,
                                     sort_by: str = 'revenue') -> Dict[str, Any]:
        """
        Get product performance report with sales analytics.
        
        Args:
            start_date: Start date for the report
            end_date: End date for the report
            limit: Maximum number of products to include
            sort_by: Sort criteria ('revenue', 'quantity', 'profit', 'margin')
            
        Returns:
            Dict containing product performance data
        """
        try:
            tenant_id = get_current_tenant()
            if not tenant_id:
                return {'success': False, 'error': 'No tenant context available'}
            
            # Set default date range if not provided (last 30 days)
            if not end_date:
                end_date = datetime.utcnow()
            if not start_date:
                start_date = end_date - timedelta(days=30)
            
            # Validate date range
            if start_date > end_date:
                raise InvalidDateRangeError("Start date cannot be after end date")
            
            # Build query for product performance
            query = db.session.query(
                Product.id,
                Product.name,
                Product.sku,
                Product.selling_price,
                Product.cost_price,
                Product.current_stock,
                Category.name.label('category_name'),
                func.sum(TransactionItem.quantity).label('total_sold'),
                func.sum(TransactionItem.line_total).label('total_revenue'),
                func.sum(TransactionItem.tax_amount).label('total_tax'),
                func.sum(TransactionItem.discount_amount).label('total_discount'),
                func.count(TransactionItem.id).label('transaction_count'),
                func.avg(TransactionItem.quantity).label('avg_quantity_per_sale')
            ).join(
                TransactionItem, Product.id == TransactionItem.product_id
            ).join(
                Transaction, TransactionItem.transaction_id == Transaction.id
            ).outerjoin(
                Category, Product.category_id == Category.id
            ).filter(
                and_(
                    Product.tenant_id == tenant_id,
                    Transaction.status == TransactionStatus.COMPLETED,
                    Transaction.completed_at >= start_date,
                    Transaction.completed_at <= end_date
                )
            ).group_by(
                Product.id, Product.name, Product.sku, Product.selling_price,
                Product.cost_price, Product.current_stock, Category.name
            )
            
            # Apply sorting
            if sort_by == 'quantity':
                query = query.order_by(desc(func.sum(TransactionItem.quantity)))
            elif sort_by == 'profit':
                query = query.order_by(desc(func.sum(TransactionItem.line_total)))
            elif sort_by == 'margin':
                query = query.order_by(desc(Product.selling_price - Product.cost_price))
            else:  # default to revenue
                query = query.order_by(desc(func.sum(TransactionItem.line_total)))
            
            # Apply limit
            results = query.limit(limit).all()
            
            # Process results
            product_performance = []
            total_revenue = 0
            total_profit = 0
            total_quantity = 0
            
            for row in results:
                revenue = float(row.total_revenue or 0)
                cost = float(row.cost_price or 0) * int(row.total_sold or 0)
                profit = revenue - cost
                profit_margin = (profit / revenue * 100) if revenue > 0 else 0
                
                # Calculate inventory turnover
                turnover_rate = (int(row.total_sold or 0) / max(1, row.current_stock)) if row.current_stock > 0 else 0
                
                product_data = {
                    'product_id': row.id,
                    'product_name': row.name,
                    'sku': row.sku,
                    'category_name': row.category_name,
                    'selling_price': float(row.selling_price or 0),
                    'cost_price': float(row.cost_price or 0),
                    'current_stock': row.current_stock,
                    'total_sold': int(row.total_sold or 0),
                    'total_revenue': revenue,
                    'total_tax': float(row.total_tax or 0),
                    'total_discount': float(row.total_discount or 0),
                    'total_profit': profit,
                    'profit_margin': round(profit_margin, 2),
                    'transaction_count': int(row.transaction_count or 0),
                    'avg_quantity_per_sale': round(float(row.avg_quantity_per_sale or 0), 2),
                    'inventory_turnover': round(turnover_rate, 2)
                }
                
                product_performance.append(product_data)
                total_revenue += revenue
                total_profit += profit
                total_quantity += int(row.total_sold or 0)
            
            return {
                'success': True,
                'date_range': {
                    'start_date': start_date.isoformat(),
                    'end_date': end_date.isoformat(),
                    'days': (end_date - start_date).days + 1
                },
                'products': product_performance,
                'summary': {
                    'total_products': len(product_performance),
                    'total_revenue': round(total_revenue, 2),
                    'total_profit': round(total_profit, 2),
                    'total_quantity_sold': total_quantity,
                    'avg_profit_margin': round((total_profit / total_revenue * 100) if total_revenue > 0 else 0, 2)
                }
            }
            
        except InvalidDateRangeError as e:
            return {'success': False, 'error': str(e)}
        except Exception as e:
            current_app.logger.error(f"Product performance report error: {str(e)}")
            return {'success': False, 'error': 'An error occurred while generating product performance report'}
    
    @staticmethod
    def get_inventory_report(low_stock_threshold: int = 10,
                           include_out_of_stock: bool = True) -> Dict[str, Any]:
        """
        Get inventory status report.
        
        Args:
            low_stock_threshold: Threshold for low stock alerts
            include_out_of_stock: Whether to include out of stock items
            
        Returns:
            Dict containing inventory report data
        """
        try:
            tenant_id = get_current_tenant()
            if not tenant_id:
                return {'success': False, 'error': 'No tenant context available'}
            
            # Get all active products with category information
            products_query = db.session.query(
                Product.id,
                Product.name,
                Product.sku,
                Product.current_stock,
                Product.minimum_stock,
                Product.maximum_stock,
                Product.selling_price,
                Product.cost_price,
                Product.track_inventory,
                Category.name.label('category_name')
            ).outerjoin(
                Category, Product.category_id == Category.id
            ).filter(
                and_(
                    Product.tenant_id == tenant_id,
                    Product.is_active == True
                )
            ).order_by(Product.name)
            
            all_products = products_query.all()
            
            # Categorize products by stock status
            in_stock = []
            low_stock = []
            out_of_stock = []
            overstocked = []
            
            total_inventory_value = 0
            total_products = len(all_products)
            
            for product in all_products:
                stock_value = float(product.cost_price or 0) * product.current_stock
                total_inventory_value += stock_value
                
                product_data = {
                    'product_id': product.id,
                    'product_name': product.name,
                    'sku': product.sku,
                    'category_name': product.category_name,
                    'current_stock': product.current_stock,
                    'minimum_stock': product.minimum_stock,
                    'maximum_stock': product.maximum_stock,
                    'selling_price': float(product.selling_price or 0),
                    'cost_price': float(product.cost_price or 0),
                    'stock_value': round(stock_value, 2),
                    'track_inventory': product.track_inventory
                }
                
                if not product.track_inventory:
                    # Non-tracked items go to in_stock
                    in_stock.append(product_data)
                elif product.current_stock <= 0:
                    if include_out_of_stock:
                        out_of_stock.append(product_data)
                elif product.current_stock <= max(product.minimum_stock, low_stock_threshold):
                    low_stock.append(product_data)
                elif product.maximum_stock and product.current_stock > product.maximum_stock:
                    overstocked.append(product_data)
                else:
                    in_stock.append(product_data)
            
            # Calculate category breakdown
            category_breakdown = {}
            for product in all_products:
                category = product.category_name or 'Uncategorized'
                if category not in category_breakdown:
                    category_breakdown[category] = {
                        'product_count': 0,
                        'total_stock': 0,
                        'total_value': 0
                    }
                
                category_breakdown[category]['product_count'] += 1
                category_breakdown[category]['total_stock'] += product.current_stock
                category_breakdown[category]['total_value'] += float(product.cost_price or 0) * product.current_stock
            
            return {
                'success': True,
                'inventory_status': {
                    'in_stock': in_stock,
                    'low_stock': low_stock,
                    'out_of_stock': out_of_stock,
                    'overstocked': overstocked
                },
                'summary': {
                    'total_products': total_products,
                    'in_stock_count': len(in_stock),
                    'low_stock_count': len(low_stock),
                    'out_of_stock_count': len(out_of_stock),
                    'overstocked_count': len(overstocked),
                    'total_inventory_value': round(total_inventory_value, 2)
                },
                'category_breakdown': category_breakdown
            }
            
        except Exception as e:
            current_app.logger.error(f"Inventory report error: {str(e)}")
            return {'success': False, 'error': 'An error occurred while generating inventory report'}
    
    @staticmethod
    def get_top_customers_report(start_date: Optional[datetime] = None,
                               end_date: Optional[datetime] = None,
                               limit: int = 20) -> Dict[str, Any]:
        """
        Get top customers report based on transaction data.
        
        Args:
            start_date: Start date for the report
            end_date: End date for the report
            limit: Maximum number of customers to include
            
        Returns:
            Dict containing top customers data
        """
        try:
            tenant_id = get_current_tenant()
            if not tenant_id:
                return {'success': False, 'error': 'No tenant context available'}
            
            # Set default date range if not provided (last 30 days)
            if not end_date:
                end_date = datetime.utcnow()
            if not start_date:
                start_date = end_date - timedelta(days=30)
            
            # Validate date range
            if start_date > end_date:
                raise InvalidDateRangeError("Start date cannot be after end date")
            
            # Query customer data (only transactions with customer info)
            # First get basic customer data
            customer_data = db.session.query(
                Transaction.customer_name,
                Transaction.customer_email,
                Transaction.customer_phone,
                func.count(Transaction.id).label('transaction_count'),
                func.sum(Transaction.total_amount).label('total_spent'),
                func.avg(Transaction.total_amount).label('avg_transaction_value'),
                func.max(Transaction.completed_at).label('last_purchase_date')
            ).filter(
                and_(
                    Transaction.tenant_id == tenant_id,
                    Transaction.status == TransactionStatus.COMPLETED,
                    Transaction.completed_at >= start_date,
                    Transaction.completed_at <= end_date,
                    or_(
                        Transaction.customer_name.isnot(None),
                        Transaction.customer_email.isnot(None),
                        Transaction.customer_phone.isnot(None)
                    )
                )
            ).group_by(
                Transaction.customer_name,
                Transaction.customer_email,
                Transaction.customer_phone
            ).order_by(
                desc(func.sum(Transaction.total_amount))
            ).limit(limit).all()
            
            # Format customer data and calculate total items purchased separately
            top_customers = []
            for row in customer_data:
                # Calculate total items purchased for this customer
                total_items = db.session.query(
                    func.sum(TransactionItem.quantity)
                ).join(
                    Transaction, TransactionItem.transaction_id == Transaction.id
                ).filter(
                    and_(
                        Transaction.tenant_id == tenant_id,
                        Transaction.status == TransactionStatus.COMPLETED,
                        Transaction.completed_at >= start_date,
                        Transaction.completed_at <= end_date,
                        Transaction.customer_name == row.customer_name,
                        Transaction.customer_email == row.customer_email,
                        Transaction.customer_phone == row.customer_phone
                    )
                ).scalar() or 0
                
                customer_info = {
                    'customer_name': row.customer_name,
                    'customer_email': row.customer_email,
                    'customer_phone': row.customer_phone,
                    'transaction_count': int(row.transaction_count or 0),
                    'total_spent': float(row.total_spent or 0),
                    'avg_transaction_value': round(float(row.avg_transaction_value or 0), 2),
                    'last_purchase_date': row.last_purchase_date.isoformat() if row.last_purchase_date else None,
                    'total_items_purchased': int(total_items)
                }
                top_customers.append(customer_info)
            
            # Calculate summary
            total_customer_revenue = sum(c['total_spent'] for c in top_customers)
            total_customer_transactions = sum(c['transaction_count'] for c in top_customers)
            
            return {
                'success': True,
                'date_range': {
                    'start_date': start_date.isoformat(),
                    'end_date': end_date.isoformat(),
                    'days': (end_date - start_date).days + 1
                },
                'customers': top_customers,
                'summary': {
                    'total_customers': len(top_customers),
                    'total_customer_revenue': round(total_customer_revenue, 2),
                    'total_customer_transactions': total_customer_transactions,
                    'avg_customer_value': round(total_customer_revenue / len(top_customers), 2) if top_customers else 0
                }
            }
            
        except InvalidDateRangeError as e:
            return {'success': False, 'error': str(e)}
        except Exception as e:
            current_app.logger.error(f"Top customers report error: {str(e)}")
            return {'success': False, 'error': 'An error occurred while generating top customers report'}
    
    @staticmethod
    def get_hourly_sales_pattern(start_date: Optional[datetime] = None,
                               end_date: Optional[datetime] = None) -> Dict[str, Any]:
        """
        Get hourly sales pattern for understanding peak hours.
        
        Args:
            start_date: Start date for the report
            end_date: End date for the report
            
        Returns:
            Dict containing hourly sales pattern data
        """
        try:
            tenant_id = get_current_tenant()
            if not tenant_id:
                return {'success': False, 'error': 'No tenant context available'}
            
            # Set default date range if not provided (last 7 days)
            if not end_date:
                end_date = datetime.utcnow()
            if not start_date:
                start_date = end_date - timedelta(days=7)
            
            # Validate date range
            if start_date > end_date:
                raise InvalidDateRangeError("Start date cannot be after end date")
            
            # Query hourly sales data
            hourly_data = db.session.query(
                func.extract('hour', Transaction.completed_at).label('hour'),
                func.count(Transaction.id).label('transaction_count'),
                func.sum(Transaction.total_amount).label('total_revenue'),
                func.avg(Transaction.total_amount).label('avg_transaction_value')
            ).filter(
                and_(
                    Transaction.tenant_id == tenant_id,
                    Transaction.status == TransactionStatus.COMPLETED,
                    Transaction.completed_at >= start_date,
                    Transaction.completed_at <= end_date
                )
            ).group_by(
                func.extract('hour', Transaction.completed_at)
            ).order_by(
                func.extract('hour', Transaction.completed_at)
            ).all()
            
            # Format hourly pattern data
            hourly_pattern = []
            for row in hourly_data:
                hourly_pattern.append({
                    'hour': int(row.hour),
                    'hour_display': f"{int(row.hour):02d}:00",
                    'transaction_count': int(row.transaction_count or 0),
                    'total_revenue': float(row.total_revenue or 0),
                    'avg_transaction_value': round(float(row.avg_transaction_value or 0), 2)
                })
            
            # Find peak hours
            if hourly_pattern:
                peak_revenue_hour = max(hourly_pattern, key=lambda x: x['total_revenue'])
                peak_transaction_hour = max(hourly_pattern, key=lambda x: x['transaction_count'])
            else:
                peak_revenue_hour = None
                peak_transaction_hour = None
            
            return {
                'success': True,
                'date_range': {
                    'start_date': start_date.isoformat(),
                    'end_date': end_date.isoformat(),
                    'days': (end_date - start_date).days + 1
                },
                'hourly_pattern': hourly_pattern,
                'peak_hours': {
                    'peak_revenue_hour': peak_revenue_hour,
                    'peak_transaction_hour': peak_transaction_hour
                }
            }
            
        except InvalidDateRangeError as e:
            return {'success': False, 'error': str(e)}
        except Exception as e:
            current_app.logger.error(f"Hourly sales pattern error: {str(e)}")
            return {'success': False, 'error': 'An error occurred while generating hourly sales pattern'}