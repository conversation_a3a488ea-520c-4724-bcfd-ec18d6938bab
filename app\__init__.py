"""Flask application factory for SaaS POS System."""

from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate
from flask_login import LoginManager
from flask_caching import Cache
from flask_wtf.csrf import CSRFProtect
import redis
from config import Config

# Initialize extensions
db = SQLAlchemy()
migrate = Migrate()
login_manager = LoginManager()
cache = Cache()
csrf = CSRFProtect()
redis_client = None


def create_app(config_class=Config):
    """Create and configure Flask application."""
    app = Flask(__name__)
    app.config.from_object(config_class)
    
    # Initialize extensions with app
    db.init_app(app)
    migrate.init_app(app, db)
    login_manager.init_app(app)
    cache.init_app(app)
    csrf.init_app(app)
    
    # Configure login manager
    login_manager.login_view = 'auth.login'
    login_manager.login_message = 'Please log in to access this page.'
    login_manager.login_message_category = 'info'
    
    # Initialize Redis client
    global redis_client
    if app.config.get('REDIS_URL'):
        redis_client = redis.from_url(app.config['REDIS_URL'])
    
    # Register blueprints
    from app.routes.auth import bp as auth_bp
    from app.routes.pos import bp as pos_bp
    from app.routes.inventory import bp as inventory_bp
    from app.routes.reports import bp as reports_bp
    from app.routes.admin import bp as admin_bp
    
    app.register_blueprint(auth_bp, url_prefix='/auth')
    app.register_blueprint(pos_bp, url_prefix='/pos')
    app.register_blueprint(inventory_bp, url_prefix='/inventory')
    app.register_blueprint(reports_bp, url_prefix='/reports')
    app.register_blueprint(admin_bp, url_prefix='/admin')
    
    # Register main routes
    from app.routes import main
    app.register_blueprint(main.bp)
    
    # Register CLI commands
    from app.cli.backup_commands import register_commands
    register_commands(app)
    
    # Set tenant context before each request
    from flask import g
    from flask_login import current_user
    @app.before_request
    def set_tenant_context():
        if hasattr(current_user, 'is_authenticated') and current_user.is_authenticated:
            g.current_tenant_id = getattr(current_user, 'tenant_id', None)
    
    return app


@login_manager.user_loader
def load_user(user_id):
    """Load user for Flask-Login."""
    from app.models.user import User
    return User.query.get(int(user_id))