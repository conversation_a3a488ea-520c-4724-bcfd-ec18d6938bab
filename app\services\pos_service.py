"""POS service for SaaS POS System."""

from datetime import datetime
from typing import Optional, Dict, Any, List
from decimal import Decimal
from flask import current_app
from flask_login import current_user
from sqlalchemy import and_, or_, func
from sqlalchemy.exc import IntegrityError
from sqlalchemy.orm import joinedload

from app import db, cache
from app.models.transaction import Transaction, TransactionItem, TransactionStatus, PaymentMethod
from app.models.product import Product
from app.models.base import get_current_tenant


class POSError(Exception):
    """Base exception for POS errors."""
    pass


class InsufficientStockError(POSError):
    """Raised when there's insufficient stock for a transaction."""
    pass


class InvalidTransactionError(POSError):
    """Raised for invalid transaction operations."""
    pass


class ProductNotFoundError(POSError):
    """Raised when a product is not found."""
    pass


class TransactionNotFoundError(POSError):
    """Raised when a transaction is not found."""
    pass


class PaymentError(POSError):
    """Raised when payment processing fails."""
    pass


class POSService:
    """Service class for handling POS operations."""
    
    @staticmethod
    def create_transaction(user_id: int, customer_data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Create a new transaction.
        
        Args:
            user_id: ID of the user creating the transaction
            customer_data: Optional customer information
            
        Returns:
            Dict containing success status and transaction info
        """
        try:
            tenant_id = get_current_tenant()
            if not tenant_id:
                return {'success': False, 'error': 'No tenant context available'}
            
            # Generate transaction number
            transaction_number = Transaction.generate_transaction_number(tenant_id)
            
            # Create transaction
            transaction = Transaction(
                tenant_id=tenant_id,
                user_id=user_id,
                transaction_number=transaction_number,
                customer_name=customer_data.get('name') if customer_data else None,
                customer_email=customer_data.get('email') if customer_data else None,
                customer_phone=customer_data.get('phone') if customer_data else None,
                table_number=customer_data.get('table_number') if customer_data else None,
                order_type=customer_data.get('order_type', 'dine_in') if customer_data else 'dine_in',
                status=TransactionStatus.PENDING
            )
            
            transaction.save()
            
            return {
                'success': True,
                'transaction': POSService._transaction_to_dict(transaction)
            }
            
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Transaction creation error: {str(e)}")
            return {'success': False, 'error': 'An error occurred while creating the transaction'}
    
    @staticmethod
    def add_item_to_cart(transaction_id: int, product_id: int, quantity: int, 
                        unit_price: Optional[Decimal] = None) -> Dict[str, Any]:
        """
        Add an item to the transaction cart.
        
        Args:
            transaction_id: ID of the transaction
            product_id: ID of the product to add
            quantity: Quantity to add
            unit_price: Optional custom unit price
            
        Returns:
            Dict containing success status and updated transaction info
        """
        try:
            tenant_id = get_current_tenant()
            if not tenant_id:
                return {'success': False, 'error': 'No tenant context available'}
            
            # Get transaction
            transaction = Transaction.query.filter_by(
                id=transaction_id, 
                tenant_id=tenant_id
            ).first()
            if not transaction:
                raise TransactionNotFoundError("Transaction not found")
            
            # Check transaction status
            if transaction.status != TransactionStatus.PENDING:
                raise InvalidTransactionError("Cannot modify completed or cancelled transaction")
            
            # Get product
            product = Product.query.filter_by(
                id=product_id, 
                tenant_id=tenant_id,
                is_active=True
            ).first()
            if not product:
                raise ProductNotFoundError("Product not found or inactive")
            
            # Check stock availability
            if product.track_inventory and not product.allow_negative_stock:
                if not product.can_sell_quantity(quantity):
                    raise InsufficientStockError(
                        f"Insufficient stock for {product.name}. Available: {product.current_stock}, Requested: {quantity}"
                    )
            
            # Add item to transaction
            transaction.add_item(product, quantity, unit_price)
            
            return {
                'success': True,
                'transaction': POSService._transaction_to_dict(transaction)
            }
            
        except (TransactionNotFoundError, ProductNotFoundError, 
                InsufficientStockError, InvalidTransactionError) as e:
            return {'success': False, 'error': str(e)}
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Add item to cart error: {str(e)}")
            return {'success': False, 'error': 'An error occurred while adding item to cart'}
    
    @staticmethod
    def remove_item_from_cart(transaction_id: int, product_id: int) -> Dict[str, Any]:
        """
        Remove an item from the transaction cart.
        
        Args:
            transaction_id: ID of the transaction
            product_id: ID of the product to remove
            
        Returns:
            Dict containing success status and updated transaction info
        """
        try:
            tenant_id = get_current_tenant()
            if not tenant_id:
                return {'success': False, 'error': 'No tenant context available'}
            
            # Get transaction
            transaction = Transaction.query.filter_by(
                id=transaction_id, 
                tenant_id=tenant_id
            ).first()
            if not transaction:
                raise TransactionNotFoundError("Transaction not found")
            
            # Check transaction status
            if transaction.status != TransactionStatus.PENDING:
                raise InvalidTransactionError("Cannot modify completed or cancelled transaction")
            
            # Remove item
            transaction.remove_item(product_id)
            transaction.save()
            
            return {
                'success': True,
                'transaction': POSService._transaction_to_dict(transaction)
            }
            
        except (TransactionNotFoundError, InvalidTransactionError) as e:
            return {'success': False, 'error': str(e)}
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Remove item from cart error: {str(e)}")
            return {'success': False, 'error': 'An error occurred while removing item from cart'}
    
    @staticmethod
    def update_item_quantity(transaction_id: int, product_id: int, new_quantity: int) -> Dict[str, Any]:
        """
        Update the quantity of an item in the transaction cart.
        
        Args:
            transaction_id: ID of the transaction
            product_id: ID of the product
            new_quantity: New quantity (0 to remove item)
            
        Returns:
            Dict containing success status and updated transaction info
        """
        try:
            tenant_id = get_current_tenant()
            if not tenant_id:
                return {'success': False, 'error': 'No tenant context available'}
            
            # Get transaction
            transaction = Transaction.query.filter_by(
                id=transaction_id, 
                tenant_id=tenant_id
            ).first()
            if not transaction:
                raise TransactionNotFoundError("Transaction not found")
            
            # Check transaction status
            if transaction.status != TransactionStatus.PENDING:
                raise InvalidTransactionError("Cannot modify completed or cancelled transaction")
            
            # Get product for stock check
            if new_quantity > 0:
                product = Product.query.filter_by(
                    id=product_id, 
                    tenant_id=tenant_id,
                    is_active=True
                ).first()
                if not product:
                    raise ProductNotFoundError("Product not found or inactive")
                
                # Check stock availability
                if product.track_inventory and not product.allow_negative_stock:
                    if not product.can_sell_quantity(new_quantity):
                        raise InsufficientStockError(
                            f"Insufficient stock for {product.name}. Available: {product.current_stock}, Requested: {new_quantity}"
                        )
            
            # Update item quantity
            transaction.update_item_quantity(product_id, new_quantity)
            transaction.save()
            
            return {
                'success': True,
                'transaction': POSService._transaction_to_dict(transaction)
            }
            
        except (TransactionNotFoundError, ProductNotFoundError, 
                InsufficientStockError, InvalidTransactionError) as e:
            return {'success': False, 'error': str(e)}
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Update item quantity error: {str(e)}")
            return {'success': False, 'error': 'An error occurred while updating item quantity'}
    
    @staticmethod
    def apply_discount(transaction_id: int, discount_type: str, discount_value: Decimal, 
                      reason: Optional[str] = None) -> Dict[str, Any]:
        """
        Apply discount to a transaction.
        
        Args:
            transaction_id: ID of the transaction
            discount_type: Type of discount ('percentage' or 'fixed_amount')
            discount_value: Discount value (percentage or amount)
            reason: Optional reason for the discount
            
        Returns:
            Dict containing success status and updated transaction info
        """
        try:
            tenant_id = get_current_tenant()
            if not tenant_id:
                return {'success': False, 'error': 'No tenant context available'}
            
            # Get transaction
            transaction = Transaction.query.filter_by(
                id=transaction_id, 
                tenant_id=tenant_id
            ).first()
            if not transaction:
                raise TransactionNotFoundError("Transaction not found")
            
            # Check transaction status
            if transaction.status != TransactionStatus.PENDING:
                raise InvalidTransactionError("Cannot modify completed or cancelled transaction")
            
            # Validate discount type
            if discount_type not in ['percentage', 'fixed_amount']:
                raise InvalidTransactionError("Invalid discount type. Must be 'percentage' or 'fixed_amount'")
            
            # Validate discount value
            if discount_value < 0:
                raise InvalidTransactionError("Discount value cannot be negative")
            
            if discount_type == 'percentage' and discount_value > 100:
                raise InvalidTransactionError("Percentage discount cannot exceed 100%")
            
            # Apply discount
            transaction.apply_discount(discount_type, discount_value, reason)
            transaction.save()
            
            return {
                'success': True,
                'transaction': POSService._transaction_to_dict(transaction)
            }
            
        except (TransactionNotFoundError, InvalidTransactionError) as e:
            return {'success': False, 'error': str(e)}
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Apply discount error: {str(e)}")
            return {'success': False, 'error': 'An error occurred while applying discount'}
    
    @staticmethod
    def process_payment(transaction_id: int, payment_method: str, amount_paid: Decimal) -> Dict[str, Any]:
        """
        Process payment for a transaction.
        
        Args:
            transaction_id: ID of the transaction
            payment_method: Payment method used
            amount_paid: Amount paid by customer
            
        Returns:
            Dict containing success status and transaction info
        """
        try:
            tenant_id = get_current_tenant()
            if not tenant_id:
                return {'success': False, 'error': 'No tenant context available'}
            
            # Get transaction
            transaction = Transaction.query.filter_by(
                id=transaction_id, 
                tenant_id=tenant_id
            ).first()
            if not transaction:
                raise TransactionNotFoundError("Transaction not found")
            
            # Check transaction status
            if transaction.status != TransactionStatus.PENDING:
                raise InvalidTransactionError("Transaction is not in pending status")
            
            # Validate payment method
            try:
                payment_method_enum = PaymentMethod(payment_method)
            except ValueError:
                raise PaymentError(f"Invalid payment method: {payment_method}")
            
            # Validate amount
            if amount_paid < 0:
                raise PaymentError("Payment amount cannot be negative")
            
            if amount_paid < transaction.total_amount:
                raise PaymentError(f"Insufficient payment. Required: {transaction.total_amount}, Received: {amount_paid}")
            
            # Process payment
            transaction.process_payment(payment_method_enum, amount_paid)
            
            # Update inventory for completed transaction
            if transaction.status == TransactionStatus.COMPLETED:
                POSService._update_inventory_for_transaction(transaction)
            
            transaction.save()
            
            return {
                'success': True,
                'transaction': POSService._transaction_to_dict(transaction),
                'change_due': float(transaction.change_given)
            }
            
        except (TransactionNotFoundError, InvalidTransactionError, PaymentError) as e:
            return {'success': False, 'error': str(e)}
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Process payment error: {str(e)}")
            return {'success': False, 'error': 'An error occurred while processing payment'}
    
    @staticmethod
    def cancel_transaction(transaction_id: int, reason: Optional[str] = None) -> Dict[str, Any]:
        """
        Cancel a transaction.
        
        Args:
            transaction_id: ID of the transaction to cancel
            reason: Optional reason for cancellation
            
        Returns:
            Dict containing success status
        """
        try:
            tenant_id = get_current_tenant()
            if not tenant_id:
                return {'success': False, 'error': 'No tenant context available'}
            
            # Get transaction
            transaction = Transaction.query.filter_by(
                id=transaction_id, 
                tenant_id=tenant_id
            ).first()
            if not transaction:
                raise TransactionNotFoundError("Transaction not found")
            
            # Cancel transaction
            transaction.cancel_transaction(reason)
            transaction.save()
            
            return {
                'success': True,
                'transaction': POSService._transaction_to_dict(transaction)
            }
            
        except (TransactionNotFoundError, InvalidTransactionError) as e:
            return {'success': False, 'error': str(e)}
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Cancel transaction error: {str(e)}")
            return {'success': False, 'error': 'An error occurred while cancelling transaction'}
    
    @staticmethod
    def get_transaction(transaction_id: int) -> Optional[Dict[str, Any]]:
        """
        Get a transaction by ID.
        
        Args:
            transaction_id: ID of the transaction
            
        Returns:
            Transaction dictionary or None if not found
        """
        try:
            tenant_id = get_current_tenant()
            if not tenant_id:
                current_app.logger.error("No tenant context available")
                return None
            
            # Utiliser le modèle Transaction avec le filtre de tenant et charger les items
            transaction = Transaction.query.options(
                joinedload(Transaction.items)
            ).filter(
                and_(
                    Transaction.id == transaction_id,
                    Transaction.tenant_id == tenant_id
                )
            ).first()
            
            if not transaction:
                current_app.logger.error(f"Transaction {transaction_id} not found")
                return None
                
            # Convertir la transaction en dictionnaire
            try:
                return {
                    'id': transaction.id,
                    'transaction_number': transaction.transaction_number,
                    'receipt_number': transaction.receipt_number,
                    'user_id': transaction.user_id,
                    'customer_name': transaction.customer_name,
                    'customer_email': transaction.customer_email,
                    'customer_phone': transaction.customer_phone,
                    'table_number': transaction.table_number,
                    'status': transaction.status.value,
                    'transaction_date': transaction.transaction_date.isoformat() if transaction.transaction_date else None,
                    'completed_at': transaction.completed_at.isoformat() if transaction.completed_at else None,
                    'subtotal': float(transaction.subtotal) if transaction.subtotal else 0.0,
                    'tax_amount': float(transaction.tax_amount) if transaction.tax_amount else 0.0,
                    'discount_amount': float(transaction.discount_amount) if transaction.discount_amount else 0.0,
                    'total_amount': float(transaction.total_amount) if transaction.total_amount else 0.0,
                    'payment_method': transaction.payment_method.value if transaction.payment_method else None,
                    'amount_paid': float(transaction.amount_paid) if transaction.amount_paid else 0.0,
                    'items': [
                        {
                            'id': item.id,
                            'product_id': item.product_id,
                            'product_name': item.product_name,
                            'quantity': item.quantity,
                            'unit_price': float(item.unit_price) if item.unit_price else 0.0,
                            'line_total': float(item.line_total) if item.line_total else 0.0
                        }
                        for item in transaction.items
                    ] if transaction.items else []
                }
            except Exception as e:
                current_app.logger.error(f"Error converting transaction to dict: {str(e)}")
                return None
            
        except Exception as e:
            current_app.logger.error(f"Get transaction error: {str(e)}")
            return None
    
    @staticmethod
    def get_pending_transactions(user_id: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        Get all pending transactions, optionally filtered by user.
        
        Args:
            user_id: Optional user ID to filter by
            
        Returns:
            List of transaction dictionaries
        """
        try:
            tenant_id = get_current_tenant()
            if not tenant_id:
                return []
            
            query = Transaction.query.filter_by(
                tenant_id=tenant_id,
                status=TransactionStatus.PENDING
            )
            
            if user_id:
                query = query.filter_by(user_id=user_id)
            
            transactions = query.order_by(Transaction.created_at.desc()).all()
            
            return [POSService._transaction_to_dict(t) for t in transactions]
            
        except Exception as e:
            current_app.logger.error(f"Get pending transactions error: {str(e)}")
            return []
    
    @staticmethod
    def generate_receipt(transaction_id: int) -> Dict[str, Any]:
        """
        Generate receipt data for a transaction.
        
        Args:
            transaction_id: ID of the transaction
            
        Returns:
            Dict containing receipt data
        """
        try:
            tenant_id = get_current_tenant()
            if not tenant_id:
                return {'success': False, 'error': 'No tenant context available'}
            
            # Get transaction
            transaction = Transaction.query.filter_by(
                id=transaction_id, 
                tenant_id=tenant_id
            ).first()
            if not transaction:
                raise TransactionNotFoundError("Transaction not found")
            
            # Get tenant info
            from app.models.user import Tenant
            tenant = Tenant.query.get(tenant_id)
            
            # Generate receipt number if not exists
            if not transaction.receipt_number:
                transaction.receipt_number = f"RCP-{transaction.transaction_number}"
                transaction.save()
            
            receipt_data = {
                'success': True,
                'receipt': {
                    'transaction_number': transaction.transaction_number,
                    'receipt_number': transaction.receipt_number,
                    'date': transaction.transaction_date.strftime('%Y-%m-%d %H:%M:%S'),
                    'tenant': {
                        'name': tenant.name if tenant else 'Unknown Business',
                        'address': tenant.address if tenant else '',
                        'phone': tenant.phone if tenant else '',
                        'email': tenant.email if tenant else ''
                    },
                    'customer': {
                        'name': transaction.customer_name,
                        'email': transaction.customer_email,
                        'phone': transaction.customer_phone
                    },
                    'items': [
                        {
                            'name': item.product_name,
                            'sku': item.product_sku,
                            'quantity': item.quantity,
                            'unit_price': float(item.unit_price),
                            'line_total': float(item.line_total),
                            'discount': float(item.discount_amount) if item.discount_amount else 0
                        }
                        for item in transaction.items
                    ],
                    'totals': {
                        'subtotal': float(transaction.subtotal),
                        'tax_amount': float(transaction.tax_amount),
                        'discount_amount': float(transaction.discount_amount),
                        'total_amount': float(transaction.total_amount),
                        'amount_paid': float(transaction.amount_paid),
                        'change_given': float(transaction.change_given)
                    },
                    'payment': {
                        'method': transaction.payment_method.value if transaction.payment_method else None,
                        'status': transaction.status.value
                    },
                    'notes': transaction.notes
                }
            }
            
            return receipt_data
            
        except TransactionNotFoundError as e:
            return {'success': False, 'error': str(e)}
        except Exception as e:
            current_app.logger.error(f"Generate receipt error: {str(e)}")
            return {'success': False, 'error': 'An error occurred while generating receipt'}
    
    @staticmethod
    def _update_inventory_for_transaction(transaction: Transaction) -> None:
        """
        Update inventory levels for a completed transaction.
        
        Args:
            transaction: The completed transaction
        """
        try:
            for item in transaction.items:
                if item.product and item.product.track_inventory:
                    # Reduce stock
                    item.product.reduce_stock(
                        item.quantity, 
                        reason=f'sale_{transaction.transaction_number}'
                    )
                    
        except Exception as e:
            current_app.logger.error(f"Inventory update error for transaction {transaction.id}: {str(e)}")
            # Don't raise exception here to avoid breaking the transaction completion
    
    @staticmethod
    def _transaction_to_dict(transaction: Transaction) -> Dict[str, Any]:
        """
        Convert transaction object to dictionary.
        
        Args:
            transaction: Transaction object
            
        Returns:
            Transaction dictionary
        """
        return {
            'id': transaction.id,
            'transaction_number': transaction.transaction_number,
            'receipt_number': transaction.receipt_number,
            'user_id': transaction.user_id,
            'customer_name': transaction.customer_name,
            'customer_email': transaction.customer_email,
            'customer_phone': transaction.customer_phone,
            'table_number': transaction.table_number,
            'order_type': transaction.order_type,
            'status': transaction.status.value,
            'transaction_date': transaction.transaction_date.isoformat(),
            'completed_at': transaction.completed_at.isoformat() if transaction.completed_at else None,
            'subtotal': float(transaction.subtotal),
            'tax_amount': float(transaction.tax_amount),
            'discount_amount': float(transaction.discount_amount),
            'total_amount': float(transaction.total_amount),
            'payment_method': transaction.payment_method.value if transaction.payment_method else None,
            'amount_paid': float(transaction.amount_paid),
            'change_given': float(transaction.change_given),
            'discount_type': transaction.discount_type,
            'discount_value': float(transaction.discount_value) if transaction.discount_value else None,
            'discount_reason': transaction.discount_reason,
            'notes': transaction.notes,
            'items': [
                {
                    'id': item.id,
                    'product_id': item.product_id,
                    'product_name': item.product_name,
                    'product_sku': item.product_sku,
                    'quantity': item.quantity,
                    'unit_price': float(item.unit_price),
                    'line_total': float(item.line_total),
                    'tax_amount': float(item.tax_amount),
                    'discount_amount': float(item.discount_amount),
                    'cost_price': float(item.cost_price)
                }
                for item in transaction.items
            ],
            'item_count': transaction.get_item_count(),
            'profit_amount': float(transaction.get_profit_amount()),
            'is_paid': transaction.is_paid(),
            'balance_due': float(transaction.get_balance_due())
        }