# SaaS POS System

A comprehensive multi-tenant Point of Sale (POS) system built with Flask, designed for various business types including retail stores, restaurants, and service businesses.

## 🚀 Features

### Core Features
- **Multi-tenant Architecture** - Support for multiple businesses with data isolation
- **User Management** - Role-based access control (Admin, Manager, Cashier)
- **Product Management** - Inventory tracking with low stock alerts
- **Transaction Processing** - Complete POS functionality with receipt generation
- **Reporting & Analytics** - Sales reports, inventory analytics, and performance metrics
- **Business Type Customization** - Tailored features for different business types

### Advanced Features
- **Restaurant Mode** - Table management, order tracking, kitchen display
- **Audit Logging** - Comprehensive audit trails for compliance
- **Automated Backups** - Database backup and recovery system
- **Data Retention** - Automated cleanup with configurable retention policies
- **Security** - Input validation, CSRF protection, rate limiting
- **Responsive Design** - Mobile-friendly interface with Tailwind CSS
- **Caching** - Redis-based caching for improved performance

## 🛠️ Technology Stack

- **Backend**: Python 3.8+, Flask, SQLAlchemy
- **Database**: SQLite (development), PostgreSQL (production)
- **Cache**: Redis
- **Frontend**: HTML5, Tailwind CSS, JavaScript
- **Authentication**: Flask-Login
- **Forms**: WTForms with CSRF protection
- **CLI**: Click for management commands

## 📋 Prerequisites

- Python 3.8 or higher
- Redis server (for caching)
- SQLite (included with Python) or PostgreSQL (for production)

## 🚀 Quick Start

### 1. Clone the Repository
```bash
git clone <repository-url>
cd saas-pos-system
```

### 2. Create Virtual Environment
```bash
python -m venv venv

# On Windows
venv\Scripts\activate

# On macOS/Linux
source venv/bin/activate
```

### 3. Install Dependencies
```bash
pip install -r requirements.txt
```

### 4. Environment Configuration
Create a `.env` file in the root directory:
```bash
# Flask Configuration
FLASK_APP=run.py
FLASK_ENV=development
SECRET_KEY=your-secret-key-here

# Database Configuration
DATABASE_URL=sqlite:///saas_pos_dev.db
DEV_DATABASE_URL=sqlite:///saas_pos_dev.db

# Redis Configuration
REDIS_URL=redis://localhost:6379/1
DEV_REDIS_URL=redis://localhost:6379/1

# Backup Configuration
BACKUP_DIR=backups
AUTO_BACKUP_ENABLED=true
AUTO_BACKUP_SCHEDULE=daily

# Data Retention Configuration
AUDIT_LOG_RETENTION_DAYS=90
BACKUP_LOG_RETENTION_DAYS=30
BACKUP_FILE_RETENTION_DAYS=30
```

### 5. Initialize Database
```bash
# Initialize database and create tables
python init_db.py

# Run migrations (if any)
flask db upgrade

# Create admin account
python create_admin.py
```

### 6. Start Redis Server
```bash
# On Windows (if Redis is installed)
redis-server

# On macOS with Homebrew
brew services start redis

# On Ubuntu/Debian
sudo systemctl start redis-server
```

### 7. Run the Application
```bash
python run.py
```

The application will be available at `http://localhost:5000`

## 👤 Default Admin Account

After running `python create_admin.py`, you can log in with:

- **Email**: `<EMAIL>`
- **Password**: `admin123`
- **Business**: `Demo Business`

**⚠️ Important**: Change the admin password immediately after first login in production!

## 📚 Usage Guide

### User Roles

1. **Admin**
   - Full system access
   - User management
   - System configuration
   - Reports and analytics

2. **Manager**
   - Inventory management
   - Transaction processing
   - Reports access
   - Staff oversight

3. **Cashier**
   - Transaction processing
   - Basic inventory viewing
   - Customer interactions

### Key Workflows

#### Processing a Sale
1. Navigate to POS interface
2. Add products to cart
3. Apply discounts if needed
4. Process payment
5. Generate receipt

#### Managing Inventory
1. Go to Inventory section
2. Add/edit products
3. Adjust stock levels
4. Set low stock alerts
5. View inventory reports

#### Viewing Reports
1. Access Reports section
2. Select date range
3. Choose report type
4. Export data (CSV/PDF)

## 🔧 CLI Commands

### Backup Management
```bash
# Create backup
flask backup create
flask backup create --tenant-id 1

# List backups
flask backup list

# Restore backup
flask backup restore --backup-id 123

# Verify backup
flask backup verify --backup-id 123

# Backup status
flask backup status

# Cleanup old backups
flask backup cleanup --retention-days 30
```

### Audit Management
```bash
# View audit logs
flask audit logs --tenant-id 1 --limit 50

# Security events
flask audit security --tenant-id 1

# Audit summary
flask audit summary --tenant-id 1

# Export audit logs
flask audit export --tenant-id 1 --start-date 2024-01-01 --end-date 2024-01-31

# Cleanup old logs
flask audit cleanup --retention-days 90
```

### Data Retention
```bash
# Run cleanup (dry run)
flask retention cleanup --dry-run

# Run actual cleanup
flask retention cleanup

# Check retention status
flask retention status
```

## 🏗️ Architecture

### Directory Structure
```
saas-pos-system/
├── app/
│   ├── cli/                 # CLI commands
│   ├── forms/              # WTForms
│   ├── models/             # Database models
│   ├── routes/             # Flask blueprints
│   ├── services/           # Business logic
│   ├── static/             # CSS, JS, images
│   ├── templates/          # Jinja2 templates
│   └── utils/              # Utilities and helpers
├── docs/                   # Documentation
├── examples/               # Usage examples
├── migrations/             # Database migrations
├── tests/                  # Test suite
├── config.py              # Configuration
├── run.py                 # Application entry point
└── requirements.txt       # Dependencies
```

### Database Schema

#### Core Tables
- `tenant` - Business/organization data
- `user` - User accounts with roles
- `product` - Product catalog
- `category` - Product categories
- `transaction` - Sales transactions
- `transaction_item` - Transaction line items

#### Audit & Backup Tables
- `audit_log` - Comprehensive audit trail
- `backup_log` - Backup operation logs

#### Restaurant Tables (Optional)
- `table` - Restaurant table management
- `order` - Order tracking
- `order_item` - Order line items

## 🔒 Security Features

### Authentication & Authorization
- Secure password hashing (Werkzeug)
- Session management with Flask-Login
- Role-based access control
- Account lockout after failed attempts
- Password reset functionality

### Data Protection
- CSRF protection on all forms
- Input validation and sanitization
- SQL injection prevention
- XSS protection
- Rate limiting on sensitive endpoints

### Audit & Compliance
- Comprehensive audit logging
- Data retention policies
- Automated backup system
- Suspicious activity detection
- Export capabilities for compliance

## 🚀 Production Deployment

### Environment Setup
1. Use PostgreSQL instead of SQLite
2. Configure Redis for production
3. Set secure environment variables
4. Enable HTTPS
5. Configure reverse proxy (nginx)

### Production Configuration
```bash
# Production Environment Variables
FLASK_ENV=production
DATABASE_URL=postgresql://user:password@localhost/saas_pos
REDIS_URL=redis://localhost:6379/0
SECRET_KEY=your-very-secure-secret-key
SESSION_COOKIE_SECURE=True
WTF_CSRF_ENABLED=True
```

### Automated Backups
Set up cron jobs for automated backups:
```bash
# Daily backup at 2 AM
0 2 * * * cd /path/to/app && flask backup create

# Weekly cleanup on Sundays at 3 AM
0 3 * * 0 cd /path/to/app && flask retention cleanup
```

## 🧪 Testing

### Run Tests
```bash
# Run all tests
python -m pytest

# Run with coverage
python -m pytest --cov=app

# Run specific test file
python -m pytest tests/test_backup_service.py

# Run with verbose output
python -m pytest -v
```

### Test Categories
- **Unit Tests** - Individual component testing
- **Integration Tests** - Component interaction testing
- **Security Tests** - Vulnerability testing
- **Performance Tests** - Load and stress testing

## 🔧 Development

### Adding New Features
1. Create feature branch
2. Add models if needed
3. Implement business logic in services
4. Create routes and templates
5. Add tests
6. Update documentation

### Code Style
- Follow PEP 8 guidelines
- Use type hints where appropriate
- Write comprehensive docstrings
- Add unit tests for new functionality

### Database Migrations
```bash
# Create migration
flask db migrate -m "Description of changes"

# Apply migration
flask db upgrade

# Downgrade migration
flask db downgrade
```

## 📊 Monitoring & Maintenance

### Health Checks
- Database connectivity
- Redis availability
- Backup system status
- Audit log integrity

### Performance Monitoring
- Response times
- Database query performance
- Cache hit rates
- Error rates

### Regular Maintenance
- Database optimization
- Log rotation
- Backup verification
- Security updates

## 🐛 Troubleshooting

### Common Issues

#### Application Won't Start
```bash
# Check Python version
python --version

# Verify dependencies
pip list

# Check database connection
flask shell
>>> from app import db
>>> db.engine.execute('SELECT 1')
```

#### Database Issues
```bash
# Reset database
python init_db.py

# Check migrations
flask db current
flask db history
```

#### Redis Connection Issues
```bash
# Test Redis connection
redis-cli ping

# Check Redis status
redis-cli info
```

### Log Files
- Application logs: Check console output
- Audit logs: Available through CLI commands
- Backup logs: `flask backup list`

## 📖 Documentation

- [Backup and Audit System](docs/BACKUP_AND_AUDIT.md)
- [Input Validation Guide](docs/INPUT_VALIDATION.md)
- [API Reference](docs/API_REFERENCE.md)
- [Deployment Guide](docs/DEPLOYMENT.md)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Check the documentation
- Review troubleshooting guide
- Create an issue on GitHub
- Contact the development team

## 🔄 Version History

### v1.0.0 (Current)
- Initial release
- Multi-tenant POS system
- Audit logging and backup system
- Restaurant mode support
- Comprehensive security features

---

**Built with ❤️ for modern businesses**