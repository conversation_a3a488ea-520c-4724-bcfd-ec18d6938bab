"""Unit tests for Transaction and TransactionItem models."""

import pytest
from decimal import Decimal
from datetime import datetime, date
from app import create_app, db
from app.models.user import User, Tenant
from app.models.product import Product, Category
from app.models.transaction import Transaction, TransactionItem, TransactionStatus, PaymentMethod
from config import TestingConfig


@pytest.fixture
def app():
    """Create test application."""
    app = create_app(TestingConfig)
    return app


@pytest.fixture
def client(app):
    """Create test client."""
    return app.test_client()


@pytest.fixture
def app_context(app):
    """Create application context."""
    with app.app_context():
        db.create_all()
        yield app
        db.session.remove()
        db.drop_all()


@pytest.fixture
def sample_tenant(app_context):
    """Create a sample tenant for testing."""
    tenant = Tenant.create(
        name='Test Store',
        business_type='retail',
        email='<EMAIL>'
    )
    return tenant


@pytest.fixture
def sample_user(app_context, sample_tenant):
    """Create a sample user for testing."""
    user = User.create_user(
        email='<EMAIL>',
        password='password123',
        first_name='<PERSON>',
        last_name='Cashier',
        tenant_id=sample_tenant.id,
        role='cashier'
    )
    return user


@pytest.fixture
def sample_category(app_context, sample_tenant):
    """Create a sample category for testing."""
    category = Category.create(
        name='Electronics',
        tenant_id=sample_tenant.id
    )
    return category


@pytest.fixture
def sample_products(app_context, sample_tenant, sample_category):
    """Create sample products for testing."""
    product1 = Product.create(
        name='Smartphone',
        sku='PHONE-001',
        category_id=sample_category.id,
        tenant_id=sample_tenant.id,
        cost_price=Decimal('300.00'),
        selling_price=Decimal('500.00'),
        current_stock=50,
        tax_rate=Decimal('0.0825'),  # 8.25%
        tax_inclusive=False
    )
    
    product2 = Product.create(
        name='Headphones',
        sku='HEAD-001',
        category_id=sample_category.id,
        tenant_id=sample_tenant.id,
        cost_price=Decimal('50.00'),
        selling_price=Decimal('100.00'),
        current_stock=30,
        tax_rate=Decimal('0.0825'),  # 8.25%
        tax_inclusive=True
    )
    
    return [product1, product2]


@pytest.fixture
def sample_transaction(app_context, sample_tenant, sample_user):
    """Create a sample transaction for testing."""
    transaction = Transaction.create(
        tenant_id=sample_tenant.id,
        user_id=sample_user.id,
        customer_name='John Doe',
        customer_email='<EMAIL>'
    )
    return transaction


class TestTransactionModel:
    """Test cases for Transaction model."""
    
    def test_transaction_creation(self, app_context, sample_tenant, sample_user):
        """Test basic transaction creation."""
        transaction = Transaction.create(
            tenant_id=sample_tenant.id,
            user_id=sample_user.id,
            customer_name='Jane Smith',
            customer_email='<EMAIL>',
            customer_phone='555-0123',
            table_number='T5',
            order_type='dine_in'
        )
        
        assert transaction.id is not None
        assert transaction.tenant_id == sample_tenant.id
        assert transaction.user_id == sample_user.id
        assert transaction.customer_name == 'Jane Smith'
        assert transaction.customer_email == '<EMAIL>'
        assert transaction.customer_phone == '555-0123'
        assert transaction.table_number == 'T5'
        assert transaction.order_type == 'dine_in'
        assert transaction.status == TransactionStatus.PENDING
        assert transaction.subtotal == Decimal('0.00')
        assert transaction.tax_amount == Decimal('0.00')
        assert transaction.discount_amount == Decimal('0.00')
        assert transaction.total_amount == Decimal('0.00')
        assert transaction.transaction_number is not None
        assert transaction.transaction_number.startswith('TXN-')
    
    def test_transaction_number_generation(self, app_context, sample_tenant):
        """Test transaction number generation and uniqueness."""
        # Generate multiple transaction numbers
        numbers = set()
        for _ in range(10):
            number = Transaction.generate_transaction_number(sample_tenant.id)
            assert number.startswith('TXN-')
            assert len(number.split('-')) == 4  # TXN-YYYYMMDD-HHMMSS-XXXX
            numbers.add(number)
        
        # All numbers should be unique
        assert len(numbers) == 10
    
    def test_add_items_to_transaction(self, app_context, sample_transaction, sample_products):
        """Test adding items to a transaction."""
        product1, product2 = sample_products
        
        # Add first product
        sample_transaction.add_item(product1, quantity=2)
        
        # Add second product
        sample_transaction.add_item(product2, quantity=1, unit_price=Decimal('95.00'))
        
        assert len(sample_transaction.items) == 2
        
        # Check first item
        item1 = sample_transaction.items[0]
        assert item1.product_id == product1.id
        assert item1.quantity == 2
        assert item1.unit_price == product1.selling_price
        assert item1.product_name == product1.name
        
        # Check second item
        item2 = sample_transaction.items[1]
        assert item2.product_id == product2.id
        assert item2.quantity == 1
        assert item2.unit_price == Decimal('95.00')  # Custom price
    
    def test_transaction_totals_calculation(self, app_context, sample_transaction, sample_products):
        """Test transaction totals calculation."""
        product1, product2 = sample_products
        
        # Add items
        sample_transaction.add_item(product1, quantity=2)  # 2 x $500 = $1000 (tax exclusive)
        sample_transaction.add_item(product2, quantity=1)  # 1 x $100 = $100 (tax inclusive)
        
        sample_transaction.calculate_totals()
        
        # Product1: $1000 subtotal, tax = $1000 * 0.0825 = $82.50
        # Product2: $100 total (tax inclusive), tax = $100 - ($100 / 1.0825) = ~$7.62
        # Expected subtotal: $1000 + $100 = $1100
        # Expected tax: $82.50 + ~$7.62 = ~$90.12
        # Expected total: $1100 + ~$90.12 = ~$1190.12
        
        assert sample_transaction.subtotal == Decimal('1100.00')
        assert abs(sample_transaction.tax_amount - Decimal('90.12')) < Decimal('0.01')
        assert abs(sample_transaction.total_amount - Decimal('1190.12')) < Decimal('0.01')
    
    def test_apply_percentage_discount(self, app_context, sample_transaction, sample_products):
        """Test applying percentage discount."""
        product1, _ = sample_products
        
        # Add item: 2 x $500 = $1000
        sample_transaction.add_item(product1, quantity=2)
        sample_transaction.calculate_totals()
        
        # Apply 10% discount
        sample_transaction.apply_discount('percentage', Decimal('10.00'), 'Customer loyalty')
        
        assert sample_transaction.discount_type == 'percentage'
        assert sample_transaction.discount_value == Decimal('10.00')
        assert sample_transaction.discount_reason == 'Customer loyalty'
        assert sample_transaction.discount_amount == Decimal('100.00')  # 10% of $1000
        
        # Total should be: ($1000 - $100) + tax on $900
        expected_discounted_subtotal = Decimal('900.00')
        expected_tax = expected_discounted_subtotal * Decimal('0.0825')  # $74.25
        expected_total = expected_discounted_subtotal + expected_tax  # $974.25
        
        assert abs(sample_transaction.total_amount - expected_total) < Decimal('0.01')
    
    def test_apply_fixed_discount(self, app_context, sample_transaction, sample_products):
        """Test applying fixed amount discount."""
        product1, _ = sample_products
        
        # Add item: 2 x $500 = $1000
        sample_transaction.add_item(product1, quantity=2)
        sample_transaction.calculate_totals()
        
        # Apply $150 fixed discount
        sample_transaction.apply_discount('fixed_amount', Decimal('150.00'), 'Manager override')
        
        assert sample_transaction.discount_type == 'fixed_amount'
        assert sample_transaction.discount_value == Decimal('150.00')
        assert sample_transaction.discount_amount == Decimal('150.00')
        
        # Total should be: ($1000 - $150) + tax on $850
        expected_discounted_subtotal = Decimal('850.00')
        expected_tax = expected_discounted_subtotal * Decimal('0.0825')  # $70.125
        expected_total = expected_discounted_subtotal + expected_tax
        
        assert abs(sample_transaction.total_amount - expected_total) < Decimal('0.01')
    
    def test_remove_item_from_transaction(self, app_context, sample_transaction, sample_products):
        """Test removing items from transaction."""
        product1, product2 = sample_products
        
        # Add items
        sample_transaction.add_item(product1, quantity=2)
        sample_transaction.add_item(product2, quantity=1)
        
        assert len(sample_transaction.items) == 2
        
        # Remove first product
        sample_transaction.remove_item(product1.id)
        
        assert len(sample_transaction.items) == 1
        assert sample_transaction.items[0].product_id == product2.id
    
    def test_update_item_quantity(self, app_context, sample_transaction, sample_products):
        """Test updating item quantity."""
        product1, _ = sample_products
        
        # Add item
        sample_transaction.add_item(product1, quantity=2)
        
        # Update quantity
        sample_transaction.update_item_quantity(product1.id, 5)
        
        item = sample_transaction.items[0]
        assert item.quantity == 5
        
        # Update to zero (should remove item)
        sample_transaction.update_item_quantity(product1.id, 0)
        
        assert len(sample_transaction.items) == 0
    
    def test_process_payment(self, app_context, sample_transaction, sample_products):
        """Test payment processing."""
        product1, _ = sample_products
        
        # Add item and calculate totals
        sample_transaction.add_item(product1, quantity=1)  # $500 + tax
        sample_transaction.calculate_totals()
        
        total_amount = sample_transaction.total_amount
        
        # Process exact payment
        sample_transaction.process_payment(PaymentMethod.CASH, total_amount)
        
        assert sample_transaction.payment_method == PaymentMethod.CASH
        assert sample_transaction.amount_paid == total_amount
        assert sample_transaction.change_given == Decimal('0.00')
        assert sample_transaction.status == TransactionStatus.COMPLETED
        assert sample_transaction.completed_at is not None
        
        # Test overpayment
        sample_transaction2 = Transaction.create(
            tenant_id=sample_transaction.tenant_id,
            user_id=sample_transaction.user_id
        )
        sample_transaction2.add_item(product1, quantity=1)
        sample_transaction2.calculate_totals()
        
        overpayment = sample_transaction2.total_amount + Decimal('20.00')
        sample_transaction2.process_payment(PaymentMethod.CASH, overpayment)
        
        assert sample_transaction2.change_given == Decimal('20.00')
        assert sample_transaction2.status == TransactionStatus.COMPLETED
    
    def test_cancel_transaction(self, app_context, sample_transaction):
        """Test transaction cancellation."""
        # Cancel pending transaction
        sample_transaction.cancel_transaction('Customer changed mind')
        
        assert sample_transaction.status == TransactionStatus.CANCELLED
        assert 'Cancelled: Customer changed mind' in sample_transaction.notes
        
        # Try to cancel completed transaction (should fail)
        sample_transaction.status = TransactionStatus.COMPLETED
        
        with pytest.raises(ValueError, match="Cannot cancel a completed transaction"):
            sample_transaction.cancel_transaction()
    
    def test_refund_transaction(self, app_context, sample_transaction, sample_products):
        """Test transaction refund."""
        product1, _ = sample_products
        
        # Complete a transaction first
        sample_transaction.add_item(product1, quantity=1)
        sample_transaction.calculate_totals()
        sample_transaction.process_payment(PaymentMethod.CARD, sample_transaction.total_amount)
        
        # Full refund
        sample_transaction.refund_transaction(reason='Defective product')
        
        assert sample_transaction.status == TransactionStatus.REFUNDED
        assert 'Refund:' in sample_transaction.notes
        assert 'Defective product' in sample_transaction.notes
        
        # Test partial refund
        sample_transaction2 = Transaction.create(
            tenant_id=sample_transaction.tenant_id,
            user_id=sample_transaction.user_id
        )
        sample_transaction2.add_item(product1, quantity=1)
        sample_transaction2.calculate_totals()
        sample_transaction2.process_payment(PaymentMethod.CARD, sample_transaction2.total_amount)
        
        partial_amount = sample_transaction2.total_amount / 2
        sample_transaction2.refund_transaction(partial_amount, 'Partial return')
        
        assert sample_transaction2.status == TransactionStatus.PARTIALLY_REFUNDED
    
    def test_transaction_profit_calculation(self, app_context, sample_transaction, sample_products):
        """Test profit calculation."""
        product1, product2 = sample_products
        
        # Add items
        sample_transaction.add_item(product1, quantity=2)  # Cost: $300, Sell: $500, Profit: $200 each
        sample_transaction.add_item(product2, quantity=1)  # Cost: $50, Sell: $100, Profit: $50
        
        expected_profit = (Decimal('200.00') * 2) + Decimal('50.00')  # $450
        actual_profit = sample_transaction.get_profit_amount()
        
        assert actual_profit == expected_profit
    
    def test_get_daily_sales(self, app_context, sample_tenant, sample_user, sample_products):
        """Test daily sales summary."""
        product1, _ = sample_products
        
        # Create and complete multiple transactions
        for i in range(3):
            transaction = Transaction.create(
                tenant_id=sample_tenant.id,
                user_id=sample_user.id
            )
            transaction.add_item(product1, quantity=1)
            transaction.calculate_totals()
            transaction.process_payment(PaymentMethod.CASH, transaction.total_amount)
        
        # Get today's sales
        daily_sales = Transaction.get_daily_sales(sample_tenant.id)
        
        assert daily_sales['transaction_count'] == 3
        assert daily_sales['total_sales'] > 0
        assert daily_sales['total_tax'] > 0
        assert daily_sales['total_profit'] > 0


class TestTransactionItemModel:
    """Test cases for TransactionItem model."""
    
    def test_transaction_item_creation(self, app_context, sample_transaction, sample_products):
        """Test transaction item creation."""
        product1, _ = sample_products
        
        item = TransactionItem.create(
            transaction_id=sample_transaction.id,
            product_id=product1.id,
            tenant_id=sample_transaction.tenant_id,
            quantity=3,
            unit_price=Decimal('450.00'),
            discount_amount=Decimal('50.00'),
            product_name=product1.name,
            product_sku=product1.sku,
            cost_price=product1.cost_price,
            tax_rate=product1.tax_rate
        )
        
        assert item.id is not None
        assert item.transaction_id == sample_transaction.id
        assert item.product_id == product1.id
        assert item.quantity == 3
        assert item.unit_price == Decimal('450.00')
        assert item.discount_amount == Decimal('50.00')
        assert item.product_name == product1.name
        assert item.product_sku == product1.sku
        assert item.cost_price == product1.cost_price
    
    def test_item_totals_calculation(self, app_context, sample_transaction, sample_products):
        """Test item totals calculation."""
        product1, _ = sample_products  # Tax exclusive product
        
        item = TransactionItem.create(
            transaction_id=sample_transaction.id,
            product_id=product1.id,
            tenant_id=sample_transaction.tenant_id,
            quantity=2,
            unit_price=Decimal('500.00'),
            discount_amount=Decimal('100.00')
        )
        
        item.calculate_totals()
        
        # Gross total: 2 * $500 = $1000
        # After discount: $1000 - $100 = $900
        # Tax (8.25%): $900 * 0.0825 = $74.25
        # Line total: $900 (tax exclusive)
        
        assert item.line_total == Decimal('900.00')
        assert abs(item.tax_amount - Decimal('74.25')) < Decimal('0.01')
    
    def test_item_profit_calculations(self, app_context, sample_transaction, sample_products):
        """Test item profit calculations."""
        product1, _ = sample_products
        
        item = TransactionItem.create(
            transaction_id=sample_transaction.id,
            product_id=product1.id,
            tenant_id=sample_transaction.tenant_id,
            quantity=2,
            unit_price=Decimal('500.00'),  # Cost: $300, Profit: $200 per unit
            discount_amount=Decimal('50.00')
        )
        
        # Profit: (($500 - $300) * 2) - $50 = $400 - $50 = $350
        expected_profit = Decimal('350.00')
        actual_profit = item.get_profit_amount()
        
        assert actual_profit == expected_profit
        
        # Profit margin: ($500 - $300) / $300 * 100 = 66.67%
        expected_margin = Decimal('66.67')
        actual_margin = item.get_profit_margin()
        
        assert abs(actual_margin - expected_margin) < Decimal('0.01')
    
    def test_item_validation(self, app_context, sample_transaction, sample_products):
        """Test transaction item validation."""
        product1, _ = sample_products
        
        # Test negative quantity
        with pytest.raises(ValueError, match="Quantity must be greater than 0"):
            TransactionItem.create(
                transaction_id=sample_transaction.id,
                product_id=product1.id,
                tenant_id=sample_transaction.tenant_id,
                quantity=0,
                unit_price=Decimal('500.00')
            )
        
        # Test negative unit price
        with pytest.raises(ValueError, match="Unit price cannot be negative"):
            TransactionItem.create(
                transaction_id=sample_transaction.id,
                product_id=product1.id,
                tenant_id=sample_transaction.tenant_id,
                quantity=1,
                unit_price=Decimal('-10.00')
            )
        
        # Test negative discount
        with pytest.raises(ValueError, match="Discount amount cannot be negative"):
            TransactionItem.create(
                transaction_id=sample_transaction.id,
                product_id=product1.id,
                tenant_id=sample_transaction.tenant_id,
                quantity=1,
                unit_price=Decimal('500.00'),
                discount_amount=Decimal('-50.00')
            )
        
        # Test discount exceeding line total
        with pytest.raises(ValueError, match="Discount amount cannot exceed line total"):
            TransactionItem.create(
                transaction_id=sample_transaction.id,
                product_id=product1.id,
                tenant_id=sample_transaction.tenant_id,
                quantity=1,
                unit_price=Decimal('100.00'),
                discount_amount=Decimal('150.00')  # Discount > line total
            )
    
    def test_get_discount_percentage(self, app_context, sample_transaction, sample_products):
        """Test discount percentage calculation."""
        product1, _ = sample_products
        
        item = TransactionItem.create(
            transaction_id=sample_transaction.id,
            product_id=product1.id,
            tenant_id=sample_transaction.tenant_id,
            quantity=2,
            unit_price=Decimal('500.00'),  # Gross: $1000
            discount_amount=Decimal('200.00')  # 20% discount
        )
        
        discount_percentage = item.get_discount_percentage()
        assert discount_percentage == Decimal('20.00')
    
    def test_get_total_cost(self, app_context, sample_transaction, sample_products):
        """Test total cost calculation."""
        product1, _ = sample_products
        
        item = TransactionItem.create(
            transaction_id=sample_transaction.id,
            product_id=product1.id,
            tenant_id=sample_transaction.tenant_id,
            quantity=3,
            unit_price=Decimal('500.00')
        )
        
        # Total cost: 3 * $300 = $900
        total_cost = item.get_total_cost()
        assert total_cost == Decimal('900.00')


class TestTransactionIntegration:
    """Integration tests for transaction workflows."""
    
    def test_complete_transaction_workflow(self, app_context, sample_tenant, sample_user, sample_products):
        """Test complete transaction workflow from creation to completion."""
        product1, product2 = sample_products
        
        # 1. Create transaction
        transaction = Transaction.create(
            tenant_id=sample_tenant.id,
            user_id=sample_user.id,
            customer_name='Integration Test Customer'
        )
        
        assert transaction.status == TransactionStatus.PENDING
        assert transaction.total_amount == Decimal('0.00')
        
        # 2. Add items
        transaction.add_item(product1, quantity=2)
        transaction.add_item(product2, quantity=1, unit_price=Decimal('95.00'))
        
        assert len(transaction.items) == 2
        assert transaction.get_item_count() == 3
        
        # 3. Apply discount
        transaction.apply_discount('percentage', Decimal('10.00'), 'Customer loyalty')
        
        assert transaction.discount_amount > 0
        
        # 4. Process payment
        total_due = transaction.total_amount
        payment_amount = total_due + Decimal('5.00')  # Overpayment
        
        transaction.process_payment(PaymentMethod.CARD, payment_amount)
        
        assert transaction.status == TransactionStatus.COMPLETED
        assert transaction.is_paid() is True
        assert transaction.change_given == Decimal('5.00')
        assert transaction.get_balance_due() == Decimal('0.00')
        
        # 5. Verify profit calculation
        profit = transaction.get_profit_amount()
        assert profit > 0
        
        # 6. Test transaction is in daily sales
        daily_sales = Transaction.get_daily_sales(sample_tenant.id)
        assert daily_sales['transaction_count'] >= 1
        assert daily_sales['total_sales'] >= transaction.total_amount