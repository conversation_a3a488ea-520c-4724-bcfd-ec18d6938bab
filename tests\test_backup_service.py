"""Tests for backup service functionality."""

import os
import tempfile
import shutil
from datetime import datetime, timedelta
import pytest
from unittest.mock import patch, MagicMock
from app import create_app, db
from app.services.backup_service import BackupService
from app.models.audit import BackupLog, AuditLog, AuditAction
from app.models.user import User, Tenant
from config import TestingConfig


@pytest.fixture
def app():
    """Create test application."""
    app = create_app(TestingConfig)
    with app.app_context():
        db.create_all()
        yield app
        db.drop_all()


@pytest.fixture
def client(app):
    """Create test client."""
    return app.test_client()


@pytest.fixture
def temp_backup_dir():
    """Create temporary backup directory."""
    temp_dir = tempfile.mkdtemp()
    yield temp_dir
    shutil.rmtree(temp_dir)


@pytest.fixture
def backup_service(app, temp_backup_dir):
    """Create backup service with temporary directory."""
    with app.app_context():
        service = BackupService()
        service.backup_dir = temp_backup_dir
        return service


@pytest.fixture
def sample_tenant(app):
    """Create sample tenant for testing."""
    with app.app_context():
        tenant = Tenant(
            name='Test Business',
            business_type='retail',
            email='<EMAIL>'
        )
        tenant.save()
        return tenant


@pytest.fixture
def sample_user(app, sample_tenant):
    """Create sample user for testing."""
    with app.app_context():
        user = User.create_user(
            email='<EMAIL>',
            password='password123',
            first_name='Test',
            last_name='User',
            tenant_id=sample_tenant.id
        )
        return user


class TestBackupService:
    """Test cases for BackupService."""
    
    def test_backup_service_initialization(self, backup_service):
        """Test backup service initialization."""
        assert backup_service is not None
        assert os.path.exists(backup_service.backup_dir)
    
    def test_generate_filename(self, backup_service):
        """Test backup filename generation."""
        filename = backup_service._generate_filename('full')
        assert 'backup_full_full_' in filename
        assert filename.endswith('.sql.gz')
        
        filename_with_tenant = backup_service._generate_filename('full', tenant_id=123)
        assert 'backup_full_tenant_123_' in filename_with_tenant
        assert filename_with_tenant.endswith('.sql.gz')
    
    def test_calculate_checksum(self, backup_service, temp_backup_dir):
        """Test checksum calculation."""
        # Create a test file
        test_file = os.path.join(temp_backup_dir, 'test.txt')
        with open(test_file, 'w') as f:
            f.write('test content')
        
        checksum1 = backup_service._calculate_checksum(test_file)
        checksum2 = backup_service._calculate_checksum(test_file)
        
        assert checksum1 == checksum2
        assert len(checksum1) == 64  # SHA-256 hex length
    
    def test_is_sqlite_database(self, backup_service, app):
        """Test SQLite database detection."""
        with app.app_context():
            assert backup_service._is_sqlite_database() is True
            assert backup_service._is_postgresql_database() is False
    
    @patch('subprocess.run')
    def test_create_full_backup_sqlite(self, mock_subprocess, backup_service, sample_tenant):
        """Test creating full SQLite backup."""
        # Mock subprocess.run for sqlite3 command
        mock_subprocess.return_value = MagicMock(stdout='-- SQLite dump\nCREATE TABLE test;')
        
        with patch.object(backup_service, '_count_records', return_value=100):
            backup_log = backup_service.create_full_backup()
            
            assert backup_log is not None
            assert backup_log.backup_type == 'full'
            assert backup_log.backup_status == 'completed'
            assert backup_log.records_count == 100
            assert os.path.exists(backup_log.backup_path)
    
    def test_create_tenant_backup(self, backup_service, sample_tenant, sample_user):
        """Test creating tenant-specific backup."""
        with patch.object(backup_service, '_backup_sqlite_tenant') as mock_backup:
            backup_log = backup_service.create_full_backup(tenant_id=sample_tenant.id)
            
            assert backup_log.tenant_id == sample_tenant.id
            mock_backup.assert_called_once()
    
    def test_backup_failure_handling(self, backup_service):
        """Test backup failure handling."""
        with patch.object(backup_service, '_backup_sqlite', side_effect=Exception('Test error')):
            with pytest.raises(Exception, match='Test error'):
                backup_service.create_full_backup()
            
            # Check that failed backup is logged
            failed_backup = BackupLog.query.filter_by(backup_status='failed').first()
            assert failed_backup is not None
            assert failed_backup.error_message == 'Test error'
    
    def test_get_tenant_tables(self, backup_service, sample_tenant):
        """Test getting tenant tables."""
        tables = backup_service._get_tenant_tables()
        
        # Should include tables with tenant_id column
        expected_tables = ['user', 'product', 'transaction', 'audit_log']  # etc.
        
        # At least some tenant tables should be found
        assert len(tables) > 0
    
    def test_count_records(self, backup_service, sample_tenant, sample_user):
        """Test record counting."""
        # Test full count
        total_count = backup_service._count_records()
        assert total_count >= 0
        
        # Test tenant-specific count
        tenant_count = backup_service._count_records(tenant_id=sample_tenant.id)
        assert tenant_count >= 0
    
    def test_verify_backup(self, backup_service, temp_backup_dir):
        """Test backup verification."""
        # Create a mock backup log
        backup_log = BackupLog(
            backup_type='full',
            backup_filename='test_backup.sql.gz',
            backup_path=os.path.join(temp_backup_dir, 'test_backup.sql.gz'),
            backup_status='completed'
        )
        backup_log.save()
        
        # Create the backup file
        with open(backup_log.backup_path, 'w') as f:
            f.write('test backup content')
        
        # Calculate and set checksum
        backup_log.checksum = backup_service._calculate_checksum(backup_log.backup_path)
        backup_log.save()
        
        # Verify backup
        is_valid = backup_service.verify_backup(backup_log.id)
        assert is_valid is True
    
    def test_verify_backup_integrity_failure(self, backup_service, temp_backup_dir):
        """Test backup verification with integrity failure."""
        # Create a mock backup log with wrong checksum
        backup_log = BackupLog(
            backup_type='full',
            backup_filename='test_backup.sql.gz',
            backup_path=os.path.join(temp_backup_dir, 'test_backup.sql.gz'),
            backup_status='completed',
            checksum='wrong_checksum'
        )
        backup_log.save()
        
        # Create the backup file
        with open(backup_log.backup_path, 'w') as f:
            f.write('test backup content')
        
        # Verify backup should fail
        is_valid = backup_service.verify_backup(backup_log.id)
        assert is_valid is False
    
    def test_cleanup_old_backups(self, backup_service):
        """Test cleanup of old backups."""
        # Create old backup logs
        old_date = datetime.utcnow() - timedelta(days=35)
        
        old_backup = BackupLog(
            backup_type='full',
            backup_filename='old_backup.sql.gz',
            backup_path='/tmp/old_backup.sql.gz',
            backup_status='completed',
            created_at=old_date
        )
        old_backup.save()
        
        recent_backup = BackupLog(
            backup_type='full',
            backup_filename='recent_backup.sql.gz',
            backup_path='/tmp/recent_backup.sql.gz',
            backup_status='completed'
        )
        recent_backup.save()
        
        # Run cleanup
        deleted_count = backup_service.cleanup_old_backups(retention_days=30)
        
        assert deleted_count >= 1
        
        # Check that old backup is deleted but recent one remains
        assert BackupLog.query.get(old_backup.id) is None
        assert BackupLog.query.get(recent_backup.id) is not None
    
    def test_get_backup_status(self, backup_service):
        """Test getting backup system status."""
        # Create some test backup logs
        BackupLog(
            backup_type='full',
            backup_filename='backup1.sql.gz',
            backup_path='/tmp/backup1.sql.gz',
            backup_status='completed',
            backup_size=1024*1024  # 1MB
        ).save()
        
        BackupLog(
            backup_type='full',
            backup_filename='backup2.sql.gz',
            backup_path='/tmp/backup2.sql.gz',
            backup_status='failed'
        ).save()
        
        status = backup_service.get_backup_status()
        
        assert 'total_backups' in status
        assert 'successful_backups' in status
        assert 'success_rate' in status
        assert 'total_backup_size_mb' in status
        assert 'recent_backups' in status
        
        assert status['total_backups'] >= 2
        assert status['successful_backups'] >= 1
        assert status['success_rate'] <= 100
    
    def test_schedule_automated_backup(self, backup_service):
        """Test scheduled automated backup."""
        with patch.object(backup_service, 'create_full_backup') as mock_create:
            with patch.object(backup_service, 'cleanup_old_backups') as mock_cleanup:
                mock_backup_log = MagicMock()
                mock_create.return_value = mock_backup_log
                mock_cleanup.return_value = 5
                
                result = backup_service.schedule_automated_backup()
                
                assert result == mock_backup_log
                mock_create.assert_called_once_with(None)
                mock_cleanup.assert_called_once()
    
    def test_restore_backup_validation(self, backup_service):
        """Test backup restore validation."""
        # Test with non-existent backup
        with pytest.raises(ValueError, match='Backup log .* not found'):
            backup_service.restore_backup(999)
        
        # Test with incomplete backup
        incomplete_backup = BackupLog(
            backup_type='full',
            backup_filename='incomplete.sql.gz',
            backup_path='/tmp/incomplete.sql.gz',
            backup_status='in_progress'
        )
        incomplete_backup.save()
        
        with pytest.raises(ValueError, match='Backup .* is not completed'):
            backup_service.restore_backup(incomplete_backup.id)
        
        # Test with missing file
        completed_backup = BackupLog(
            backup_type='full',
            backup_filename='missing.sql.gz',
            backup_path='/tmp/missing.sql.gz',
            backup_status='completed'
        )
        completed_backup.save()
        
        with pytest.raises(ValueError, match='Backup file .* not found'):
            backup_service.restore_backup(completed_backup.id)


class TestBackupLog:
    """Test cases for BackupLog model."""
    
    def test_backup_log_creation(self, app):
        """Test backup log creation."""
        with app.app_context():
            backup_log = BackupLog(
                backup_type='full',
                backup_filename='test.sql.gz',
                backup_path='/tmp/test.sql.gz'
            )
            backup_log.save()
            
            assert backup_log.id is not None
            assert backup_log.backup_status == 'in_progress'
            assert backup_log.started_at is not None
    
    def test_mark_completed(self, app):
        """Test marking backup as completed."""
        with app.app_context():
            backup_log = BackupLog(
                backup_type='full',
                backup_filename='test.sql.gz',
                backup_path='/tmp/test.sql.gz'
            )
            backup_log.save()
            
            backup_log.mark_completed(
                backup_size=1024,
                records_count=100,
                checksum='abc123'
            )
            
            assert backup_log.backup_status == 'completed'
            assert backup_log.completed_at is not None
            assert backup_log.backup_size == 1024
            assert backup_log.records_count == 100
            assert backup_log.checksum == 'abc123'
            assert backup_log.duration_seconds is not None
    
    def test_mark_failed(self, app):
        """Test marking backup as failed."""
        with app.app_context():
            backup_log = BackupLog(
                backup_type='full',
                backup_filename='test.sql.gz',
                backup_path='/tmp/test.sql.gz'
            )
            backup_log.save()
            
            backup_log.mark_failed('Test error', {'detail': 'error detail'})
            
            assert backup_log.backup_status == 'failed'
            assert backup_log.completed_at is not None
            assert backup_log.error_message == 'Test error'
            assert backup_log.error_details == {'detail': 'error detail'}
    
    def test_verify_backup(self, app):
        """Test backup verification."""
        with app.app_context():
            backup_log = BackupLog(
                backup_type='full',
                backup_filename='test.sql.gz',
                backup_path='/tmp/test.sql.gz',
                checksum='correct_checksum'
            )
            backup_log.save()
            
            # Test successful verification
            result = backup_log.verify_backup('correct_checksum')
            assert result is True
            assert backup_log.verified is True
            assert backup_log.verification_date is not None
            
            # Test failed verification
            result = backup_log.verify_backup('wrong_checksum')
            assert result is False
            assert backup_log.verified is False
    
    def test_get_recent_backups(self, app):
        """Test getting recent backups."""
        with app.app_context():
            # Create test backups
            for i in range(5):
                BackupLog(
                    backup_type='full',
                    backup_filename=f'backup_{i}.sql.gz',
                    backup_path=f'/tmp/backup_{i}.sql.gz'
                ).save()
            
            recent = BackupLog.get_recent_backups(limit=3)
            assert len(recent) == 3
            
            # Should be ordered by created_at desc
            assert recent[0].created_at >= recent[1].created_at
    
    def test_cleanup_old_backups(self, app, temp_backup_dir):
        """Test cleanup of old backup logs."""
        with app.app_context():
            # Create old backup
            old_backup_path = os.path.join(temp_backup_dir, 'old_backup.sql.gz')
            with open(old_backup_path, 'w') as f:
                f.write('old backup')
            
            old_backup = BackupLog(
                backup_type='full',
                backup_filename='old_backup.sql.gz',
                backup_path=old_backup_path,
                created_at=datetime.utcnow() - timedelta(days=35)
            )
            old_backup.save()
            
            # Create recent backup
            recent_backup = BackupLog(
                backup_type='full',
                backup_filename='recent_backup.sql.gz',
                backup_path='/tmp/recent_backup.sql.gz'
            )
            recent_backup.save()
            
            deleted_count = BackupLog.cleanup_old_backups(retention_days=30)
            
            assert deleted_count >= 1
            assert BackupLog.query.get(old_backup.id) is None
            assert BackupLog.query.get(recent_backup.id) is not None
            assert not os.path.exists(old_backup_path)  # File should be deleted