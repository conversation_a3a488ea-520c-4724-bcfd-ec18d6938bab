"""Test configuration and core functionality."""

from config import Config, DevelopmentConfig, ProductionConfig, TestingConfig
from app import create_app
from app.utils.cache import cache_key, cached
from app.utils.validators import sanitize_input, validate_email
from wtforms import Form, StringField


def test_configurations():
    """Test different configuration classes."""
    print("Testing configuration classes...")
    
    # Test base config
    assert Config.SECRET_KEY is not None
    assert Config.SQLALCHEMY_TRACK_MODIFICATIONS is False
    print("✓ Base configuration working")
    
    # Test development config
    dev_config = DevelopmentConfig()
    assert dev_config.DEBUG is True
    assert 'sqlite' in dev_config.SQLALCHEMY_DATABASE_URI
    print("✓ Development configuration working")
    
    # Test production config
    prod_config = ProductionConfig()
    assert prod_config.DEBUG is False
    assert prod_config.SESSION_COOKIE_SECURE is True
    print("✓ Production configuration working")
    
    # Test testing config
    test_config = TestingConfig()
    assert test_config.TESTING is True
    assert test_config.SQLALCHEMY_DATABASE_URI == 'sqlite:///:memory:'
    print("✓ Testing configuration working")


def test_app_creation():
    """Test Flask app creation with different configs."""
    print("\nTesting Flask app creation...")
    
    # Test with development config
    app = create_app(DevelopmentConfig)
    assert app.config['DEBUG'] is True
    print("✓ Development app creation working")
    
    # Test with testing config
    app = create_app(TestingConfig)
    assert app.config['TESTING'] is True
    print("✓ Testing app creation working")


def test_cache_utilities():
    """Test cache utility functions."""
    print("\nTesting cache utilities...")
    
    # Test cache key generation
    key = cache_key("test", "arg1", "arg2", param1="value1", param2="value2")
    expected = "test:arg1:arg2:param1:value1:param2:value2"
    assert key == expected
    print("✓ Cache key generation working")


def test_validators():
    """Test validation utilities."""
    print("\nTesting validation utilities...")
    
    # Test input sanitization
    dirty_input = "<script>alert('xss')</script>Hello World"
    clean_input = sanitize_input(dirty_input)
    assert '<' not in clean_input
    assert '>' not in clean_input
    assert 'Hello World' in clean_input
    print("✓ Input sanitization working")
    
    # Test email validation
    class TestForm(Form):
        email = StringField('Email')
    
    form = TestForm()
    form.email.data = "<EMAIL>"
    
    try:
        validate_email(form, form.email)
        print("✓ Email validation working")
    except Exception as e:
        print(f"✗ Email validation failed: {e}")


def test_database_configuration():
    """Test database configuration."""
    print("\nTesting database configuration...")
    
    app = create_app(TestingConfig)
    with app.app_context():
        from app import db
        
        # Test database creation
        db.create_all()
        
        # Test basic query
        result = db.session.execute(db.text("SELECT 1 as test"))
        row = result.fetchone()
        assert row[0] == 1
        print("✓ Database configuration working")


if __name__ == '__main__':
    print("Running configuration and core functionality tests...\n")
    
    test_configurations()
    test_app_creation()
    test_cache_utilities()
    test_validators()
    test_database_configuration()
    
    print("\n" + "="*50)
    print("✅ All core configuration tests passed!")
    print("✅ Project structure and configuration are working correctly!")
    print("="*50)