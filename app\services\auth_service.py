"""Authentication service for SaaS POS System."""

from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from flask import session, current_app
from flask_login import login_user, logout_user, current_user
from werkzeug.security import generate_password_hash
import secrets

from app import db, cache
from app.models.user import User, Tenant


class AuthenticationError(Exception):
    """Base exception for authentication errors."""
    pass


class InvalidCredentialsError(AuthenticationError):
    """Raised when login credentials are invalid."""
    pass


class AccountLockedError(AuthenticationError):
    """Raised when user account is locked."""
    pass


class TenantInactiveError(AuthenticationError):
    """Raised when tenant account is inactive."""
    pass


class AuthService:
    """Service class for handling authentication operations."""
    
    @staticmethod
    def login(email: str, password: str, business_name: str, remember_me: bool = False) -> Dict[str, Any]:
        """
        Authenticate user and create session.
        
        Args:
            email: User's email address
            password: User's password
            business_name: Business name for multi-tenant authentication
            remember_me: Whether to remember the user session
            
        Returns:
            Dict containing success status and user info or error details
            
        Raises:
            InvalidCredentialsError: When credentials are invalid
            AccountLockedError: When account is locked
            TenantInactiveError: When tenant is inactive
        """
        try:
            # Find tenant by business name
            tenant = Tenant.query.filter_by(name=business_name).first()
            if not tenant or not tenant.is_active():
                raise TenantInactiveError("Business account is inactive or does not exist")
            
            # Check if tenant subscription has expired
            if tenant.is_subscription_expired():
                raise TenantInactiveError("Business subscription has expired")
            
            # Authenticate user
            user = User.authenticate(email, password, tenant.id)
            
            if not user:
                # Check if user exists to provide specific error
                existing_user = User.query.filter_by(email=email, tenant_id=tenant.id).first()
                if existing_user and existing_user.is_account_locked():
                    raise AccountLockedError("Account is temporarily locked due to failed login attempts")
                else:
                    raise InvalidCredentialsError("Invalid email or password")
            
            # Check if user account is active
            if not user.is_active:
                raise InvalidCredentialsError("User account is inactive")
            
            # Log user in with Flask-Login
            login_user(user, remember=remember_me)
            
            # Store tenant info in session for easy access
            session['tenant_id'] = tenant.id
            session['tenant_name'] = tenant.name
            session['business_type'] = tenant.business_type
            
            # Cache user permissions for performance
            AuthService._cache_user_permissions(user)
            
            return {
                'success': True,
                'user': {
                    'id': user.id,
                    'email': user.email,
                    'full_name': user.get_full_name(),
                    'role': user.role,
                    'tenant_id': user.tenant_id,
                    'last_login': user.last_login.isoformat() if user.last_login else None
                },
                'tenant': {
                    'id': tenant.id,
                    'name': tenant.name,
                    'business_type': tenant.business_type
                }
            }
            
        except (InvalidCredentialsError, AccountLockedError, TenantInactiveError):
            raise
        except Exception as e:
            current_app.logger.error(f"Login error: {str(e)}")
            raise AuthenticationError("An error occurred during login")
    
    @staticmethod
    def logout() -> Dict[str, Any]:
        """
        Log out current user and clear session.
        
        Returns:
            Dict containing success status
        """
        try:
            if current_user.is_authenticated:
                user_id = current_user.id
                # Clear cached permissions
                AuthService._clear_user_cache(user_id)
                
            # Log out user with Flask-Login
            logout_user()
            
            # Clear session data
            session.clear()
            
            return {'success': True, 'message': 'Successfully logged out'}
            
        except Exception as e:
            current_app.logger.error(f"Logout error: {str(e)}")
            return {'success': False, 'message': 'An error occurred during logout'}
    
    @staticmethod
    def register(email: str, password: str, first_name: str, last_name: str, 
                tenant_name: str, business_type: str = 'retail') -> Dict[str, Any]:
        """
        Register new user and tenant.
        
        Args:
            email: User's email address
            password: User's password
            first_name: User's first name
            last_name: User's last name
            tenant_name: Name of the business/tenant
            business_type: Type of business (retail, restaurant, service)
            
        Returns:
            Dict containing success status and user/tenant info
        """
        try:
            # Check if email already exists across all tenants
            existing_user = User.query.filter_by(email=email).first()
            if existing_user:
                return {
                    'success': False,
                    'error': 'Email address is already registered'
                }
            
            # Create new tenant
            tenant = Tenant(
                name=tenant_name,
                business_type=business_type,
                subscription_status='active',
                email=email
            )
            tenant.save()
            
            # Create admin user for the tenant
            user = User.create_user(
                email=email,
                password=password,
                first_name=first_name,
                last_name=last_name,
                tenant_id=tenant.id,
                role='admin'  # First user is always admin
            )
            
            # Generate email verification token
            user.email_verification_token = secrets.token_urlsafe(32)
            user.save()
            
            return {
                'success': True,
                'user': {
                    'id': user.id,
                    'email': user.email,
                    'full_name': user.get_full_name(),
                    'role': user.role,
                    'tenant_id': user.tenant_id
                },
                'tenant': {
                    'id': tenant.id,
                    'name': tenant.name,
                    'business_type': tenant.business_type
                },
                'verification_token': user.email_verification_token
            }
            
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Registration error: {str(e)}")
            return {
                'success': False,
                'error': 'An error occurred during registration'
            }
    
    @staticmethod
    def verify_email(token: str) -> Dict[str, Any]:
        """
        Verify user email with token.
        
        Args:
            token: Email verification token
            
        Returns:
            Dict containing success status
        """
        try:
            user = User.query.filter_by(email_verification_token=token).first()
            if not user:
                return {'success': False, 'error': 'Invalid verification token'}
            
            user.verify_email()
            
            return {'success': True, 'message': 'Email verified successfully'}
            
        except Exception as e:
            current_app.logger.error(f"Email verification error: {str(e)}")
            return {'success': False, 'error': 'An error occurred during email verification'}
    
    @staticmethod
    def request_password_reset(email: str, business_name: str) -> Dict[str, Any]:
        """
        Request password reset for user.
        
        Args:
            email: User's email address
            business_name: Business name to identify tenant
            
        Returns:
            Dict containing success status and reset token
        """
        try:
            # Find tenant by business name
            tenant = Tenant.query.filter_by(name=business_name).first()
            if not tenant:
                # Don't reveal if business exists for security
                return {'success': True, 'message': 'If the email exists, a reset link has been sent'}
            
            user = User.query.filter_by(email=email, tenant_id=tenant.id).first()
            if not user:
                # Don't reveal if email exists for security
                return {'success': True, 'message': 'If the email exists, a reset link has been sent'}
            
            reset_token = user.generate_password_reset_token()
            
            return {
                'success': True,
                'message': 'Password reset token generated',
                'reset_token': reset_token
            }
            
        except Exception as e:
            current_app.logger.error(f"Password reset request error: {str(e)}")
            return {'success': False, 'error': 'An error occurred while requesting password reset'}
    
    @staticmethod
    def reset_password(token: str, new_password: str) -> Dict[str, Any]:
        """
        Reset user password with token.
        
        Args:
            token: Password reset token
            new_password: New password
            
        Returns:
            Dict containing success status
        """
        try:
            user = User.query.filter_by(password_reset_token=token).first()
            if not user:
                return {'success': False, 'error': 'Invalid or expired reset token'}
            
            if user.reset_password(new_password, token):
                return {'success': True, 'message': 'Password reset successfully'}
            else:
                return {'success': False, 'error': 'Invalid or expired reset token'}
                
        except Exception as e:
            current_app.logger.error(f"Password reset error: {str(e)}")
            return {'success': False, 'error': 'An error occurred while resetting password'}
    
    @staticmethod
    def change_password(current_password: str, new_password: str) -> Dict[str, Any]:
        """
        Change password for current user.
        
        Args:
            current_password: Current password
            new_password: New password
            
        Returns:
            Dict containing success status
        """
        try:
            if not current_user.is_authenticated:
                return {'success': False, 'error': 'User not authenticated'}
            
            if not current_user.check_password(current_password):
                return {'success': False, 'error': 'Current password is incorrect'}
            
            current_user.set_password(new_password)
            current_user.save()
            
            return {'success': True, 'message': 'Password changed successfully'}
            
        except Exception as e:
            current_app.logger.error(f"Password change error: {str(e)}")
            return {'success': False, 'error': 'An error occurred while changing password'}
    
    @staticmethod
    def unlock_account(user_id: int) -> Dict[str, Any]:
        """
        Unlock user account (admin function).
        
        Args:
            user_id: ID of user to unlock
            
        Returns:
            Dict containing success status
        """
        try:
            if not current_user.is_authenticated or not current_user.is_admin():
                return {'success': False, 'error': 'Insufficient permissions'}
            
            user = User.query.filter_by(id=user_id, tenant_id=current_user.tenant_id).first()
            if not user:
                return {'success': False, 'error': 'User not found'}
            
            user.unlock_account()
            
            return {'success': True, 'message': 'Account unlocked successfully'}
            
        except Exception as e:
            current_app.logger.error(f"Account unlock error: {str(e)}")
            return {'success': False, 'error': 'An error occurred while unlocking account'}
    
    @staticmethod
    def get_current_user_info() -> Optional[Dict[str, Any]]:
        """
        Get current authenticated user information.
        
        Returns:
            Dict containing user info or None if not authenticated
        """
        if not current_user.is_authenticated:
            return None
        
        return {
            'id': current_user.id,
            'email': current_user.email,
            'full_name': current_user.get_full_name(),
            'role': current_user.role,
            'tenant_id': current_user.tenant_id,
            'permissions': {
                'can_access_reports': current_user.can_access_reports(),
                'can_manage_inventory': current_user.can_manage_inventory(),
                'can_process_transactions': current_user.can_process_transactions(),
                'is_admin': current_user.is_admin(),
                'is_manager': current_user.is_manager()
            }
        }
    
    @staticmethod
    def _cache_user_permissions(user: User) -> None:
        """Cache user permissions for performance."""
        try:
            cache_key = f"user_permissions_{user.id}"
            permissions = {
                'can_access_reports': user.can_access_reports(),
                'can_manage_inventory': user.can_manage_inventory(),
                'can_process_transactions': user.can_process_transactions(),
                'is_admin': user.is_admin(),
                'is_manager': user.is_manager()
            }
            cache.set(cache_key, permissions, timeout=3600)  # Cache for 1 hour
        except Exception as e:
            current_app.logger.warning(f"Failed to cache user permissions: {str(e)}")
    
    @staticmethod
    def _clear_user_cache(user_id: int) -> None:
        """Clear cached user data."""
        try:
            cache_key = f"user_permissions_{user_id}"
            cache.delete(cache_key)
        except Exception as e:
            current_app.logger.warning(f"Failed to clear user cache: {str(e)}")
    
    @staticmethod
    def get_cached_permissions(user_id: int) -> Optional[Dict[str, bool]]:
        """Get cached user permissions."""
        try:
            cache_key = f"user_permissions_{user_id}"
            return cache.get(cache_key)
        except Exception as e:
            current_app.logger.warning(f"Failed to get cached permissions: {str(e)}")
            return None