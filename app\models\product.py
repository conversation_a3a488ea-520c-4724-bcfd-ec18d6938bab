"""Product and Category models for SaaS POS System."""

from decimal import Decimal
from datetime import datetime
from sqlalchemy import event
from app import db
from app.models.base import TenantAwareModel


class Category(TenantAwareModel):
    """Category model for organizing products."""
    
    __tablename__ = 'category'
    
    # Basic category information
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text, nullable=True)
    
    # Category hierarchy (optional parent category)
    parent_id = db.Column(db.Integer, db.ForeignKey('category.id'), nullable=True)
    parent = db.relationship('Category', remote_side='Category.id', backref='subcategories')
    
    # Display settings
    color = db.Column(db.String(7), nullable=True)  # Hex color code
    icon = db.Column(db.String(50), nullable=True)  # Icon name/class
    sort_order = db.Column(db.Integer, nullable=False, default=0)
    
    # Status
    is_active = db.Column(db.<PERSON>, nullable=False, default=True)
    
    # Relationships
    products = db.relationship('Product', backref='category_ref', lazy=True)
    
    # Indexes for optimization
    __table_args__ = (
        db.Index('idx_category_tenant_active', 'tenant_id', 'is_active'),
        db.Index('idx_category_tenant_parent', 'tenant_id', 'parent_id'),
        db.Index('idx_category_sort_order', 'tenant_id', 'sort_order'),
        db.Index('idx_category_name_search', 'name'),
    )
    
    def __repr__(self):
        return f'<Category {self.name}>'
    
    def get_full_path(self):
        """Get full category path (e.g., 'Food > Beverages > Hot Drinks')."""
        if self.parent:
            return f"{self.parent.get_full_path()} > {self.name}"
        return self.name
    
    def get_product_count(self):
        """Get count of active products in this category."""
        return Product.query.filter_by(category_id=self.id, is_active=True).count()
    
    def get_subcategory_count(self):
        """Get count of active subcategories."""
        return Category.query.filter_by(parent_id=self.id, is_active=True).count()
    
    def has_products(self):
        """Check if category has any products."""
        return self.get_product_count() > 0
    
    def can_be_deleted(self):
        """Check if category can be safely deleted."""
        return not self.has_products() and self.get_subcategory_count() == 0
    
    @classmethod
    def get_root_categories(cls, tenant_id):
        """Get all root categories (no parent) for a tenant."""
        return cls.query.filter_by(tenant_id=tenant_id, parent_id=None, is_active=True).order_by(cls.sort_order, cls.name).all()
    
    @classmethod
    def get_category_tree(cls, tenant_id):
        """Get hierarchical category tree for a tenant."""
        root_categories = cls.get_root_categories(tenant_id)
        return [cls._build_category_tree(cat) for cat in root_categories]
    
    @classmethod
    def _build_category_tree(cls, category):
        """Build category tree recursively."""
        return {
            'id': category.id,
            'name': category.name,
            'description': category.description,
            'color': category.color,
            'icon': category.icon,
            'product_count': category.get_product_count(),
            'subcategories': [cls._build_category_tree(sub) for sub in category.subcategories if sub.is_active]
        }


class Product(TenantAwareModel):
    """Product model for inventory management."""
    
    __tablename__ = 'product'
    
    # Basic product information
    name = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text, nullable=True)
    sku = db.Column(db.String(100), nullable=True, index=True)  # Stock Keeping Unit
    barcode = db.Column(db.String(100), nullable=True, index=True)
    
    # Category relationship
    category_id = db.Column(db.Integer, db.ForeignKey('category.id'), nullable=True)
    
    # Pricing
    cost_price = db.Column(db.Numeric(10, 2), nullable=False, default=0.00)  # What we pay
    selling_price = db.Column(db.Numeric(10, 2), nullable=False, default=0.00)  # What customer pays
    
    # Inventory tracking
    current_stock = db.Column(db.Integer, nullable=False, default=0)
    minimum_stock = db.Column(db.Integer, nullable=False, default=0)  # Low stock threshold
    maximum_stock = db.Column(db.Integer, nullable=True)  # Optional max stock
    
    # Stock management settings
    track_inventory = db.Column(db.Boolean, nullable=False, default=True)
    allow_negative_stock = db.Column(db.Boolean, nullable=False, default=False)
    
    # Product attributes
    unit_of_measure = db.Column(db.String(20), nullable=False, default='each')  # each, kg, liter, etc.
    weight = db.Column(db.Numeric(8, 3), nullable=True)  # Product weight
    
    # Tax settings
    tax_rate = db.Column(db.Numeric(5, 4), nullable=False, default=0.0000)  # Tax rate as decimal (0.0825 = 8.25%)
    tax_inclusive = db.Column(db.Boolean, nullable=False, default=False)  # Is selling price tax-inclusive?
    
    # Status and visibility
    is_active = db.Column(db.Boolean, nullable=False, default=True)
    is_featured = db.Column(db.Boolean, nullable=False, default=False)
    
    # Display settings
    image_url = db.Column(db.String(500), nullable=True)
    color = db.Column(db.String(7), nullable=True)  # Hex color code for POS display
    sort_order = db.Column(db.Integer, nullable=False, default=0)
    
    # Comprehensive indexing for optimization
    __table_args__ = (
        db.Index('idx_product_tenant_active', 'tenant_id', 'is_active'),
        db.Index('idx_product_tenant_category', 'tenant_id', 'category_id'),
        db.Index('idx_product_sku_tenant', 'sku', 'tenant_id'),
        db.Index('idx_product_barcode_tenant', 'barcode', 'tenant_id'),
        db.Index('idx_product_name_search', 'name'),
        db.Index('idx_product_low_stock', 'tenant_id', 'track_inventory', 'current_stock', 'minimum_stock'),
        db.Index('idx_product_featured', 'tenant_id', 'is_featured', 'is_active'),
        db.Index('idx_product_sort_order', 'tenant_id', 'sort_order'),
    )
    
    def __repr__(self):
        return f'<Product {self.name}>'
    
    def get_profit_margin(self):
        """Calculate profit margin percentage."""
        if self.cost_price == 0:
            return Decimal('0.00')
        profit = self.selling_price - self.cost_price
        return (profit / self.cost_price) * 100
    
    def get_profit_amount(self):
        """Calculate profit amount per unit."""
        return self.selling_price - self.cost_price
    
    def get_tax_amount(self):
        """Calculate tax amount based on selling price."""
        if self.tax_inclusive:
            # Tax is included in selling price
            return self.selling_price - (self.selling_price / (1 + self.tax_rate))
        else:
            # Tax is added to selling price
            return self.selling_price * self.tax_rate
    
    def get_price_with_tax(self):
        """Get selling price including tax."""
        if self.tax_inclusive:
            return self.selling_price
        else:
            return self.selling_price + self.get_tax_amount()
    
    def get_price_without_tax(self):
        """Get selling price excluding tax."""
        if self.tax_inclusive:
            return self.selling_price - self.get_tax_amount()
        else:
            return self.selling_price
    
    def is_low_stock(self):
        """Check if product is below minimum stock level."""
        if not self.track_inventory:
            return False
        return self.current_stock <= self.minimum_stock
    
    def is_out_of_stock(self):
        """Check if product is out of stock."""
        if not self.track_inventory:
            return False
        return self.current_stock <= 0
    
    def can_sell_quantity(self, quantity):
        """Check if we can sell the requested quantity."""
        if not self.track_inventory:
            return True
        if self.allow_negative_stock:
            return True
        return self.current_stock >= quantity
    
    def adjust_stock(self, quantity, reason='manual_adjustment'):
        """Adjust stock level and create inventory movement record."""
        old_stock = self.current_stock
        self.current_stock += quantity
        
        # Create inventory movement record
        movement = InventoryMovement(
            product_id=self.id,
            tenant_id=self.tenant_id,
            movement_type='adjustment',
            quantity=quantity,
            old_stock=old_stock,
            new_stock=self.current_stock,
            reason=reason
        )
        
        db.session.add(movement)
        self.save()
        return movement
    
    def reduce_stock(self, quantity, reason='sale'):
        """Reduce stock level (for sales)."""
        return self.adjust_stock(-quantity, reason)
    
    def increase_stock(self, quantity, reason='restock'):
        """Increase stock level (for restocking)."""
        return self.adjust_stock(quantity, reason)
    
    def get_stock_value(self):
        """Get total value of current stock at cost price."""
        return self.current_stock * self.cost_price
    
    def get_retail_stock_value(self):
        """Get total value of current stock at selling price."""
        return self.current_stock * self.selling_price
    
    @classmethod
    def get_low_stock_products(cls, tenant_id, limit=None):
        """Get products that are low on stock."""
        query = cls.query.filter(
            cls.tenant_id == tenant_id,
            cls.is_active == True,
            cls.track_inventory == True,
            cls.current_stock <= cls.minimum_stock
        ).order_by(cls.current_stock.asc())
        
        if limit:
            query = query.limit(limit)
        
        return query.all()
    
    @classmethod
    def get_out_of_stock_products(cls, tenant_id):
        """Get products that are out of stock."""
        return cls.query.filter(
            cls.tenant_id == tenant_id,
            cls.is_active == True,
            cls.track_inventory == True,
            cls.current_stock <= 0
        ).all()
    
    @classmethod
    def search_products(cls, tenant_id, search_term):
        """Search products by name, SKU, or barcode."""
        search_pattern = f"%{search_term}%"
        return cls.query.filter(
            cls.tenant_id == tenant_id,
            cls.is_active == True,
            db.or_(
                cls.name.ilike(search_pattern),
                cls.sku.ilike(search_pattern),
                cls.barcode.ilike(search_pattern)
            )
        ).all()


class InventoryMovement(TenantAwareModel):
    """Track all inventory movements for audit trail."""
    
    __tablename__ = 'inventory_movement'
    
    # Product reference
    product_id = db.Column(db.Integer, db.ForeignKey('product.id'), nullable=False)
    product = db.relationship('Product', backref='inventory_movements')
    
    # Movement details
    movement_type = db.Column(db.String(50), nullable=False)  # sale, restock, adjustment, return, etc.
    quantity = db.Column(db.Integer, nullable=False)  # Positive for increase, negative for decrease
    
    # Stock levels
    old_stock = db.Column(db.Integer, nullable=False)
    new_stock = db.Column(db.Integer, nullable=False)
    
    # Additional information
    reason = db.Column(db.String(200), nullable=True)
    reference_id = db.Column(db.String(100), nullable=True)  # Transaction ID, PO number, etc.
    notes = db.Column(db.Text, nullable=True)
    
    # User who made the change
    user_id = db.Column(db.Integer, nullable=True)  # Optional user tracking
    
    # Indexes for optimization
    __table_args__ = (
        db.Index('idx_inventory_tenant_product', 'tenant_id', 'product_id'),
        db.Index('idx_inventory_tenant_type', 'tenant_id', 'movement_type'),
        db.Index('idx_inventory_tenant_date', 'tenant_id', 'created_at'),
        db.Index('idx_inventory_product_date', 'product_id', 'created_at'),
        db.Index('idx_inventory_reference', 'reference_id'),
    )
    
    def __repr__(self):
        return f'<InventoryMovement {self.product.name}: {self.quantity}>'
    
    @classmethod
    def get_product_history(cls, product_id, limit=50):
        """Get inventory movement history for a product."""
        return cls.query.filter_by(product_id=product_id).order_by(cls.created_at.desc()).limit(limit).all()
    
    @classmethod
    def get_movements_by_type(cls, tenant_id, movement_type, start_date=None, end_date=None):
        """Get inventory movements by type within date range."""
        query = cls.query.filter_by(tenant_id=tenant_id, movement_type=movement_type)
        
        if start_date:
            query = query.filter(cls.created_at >= start_date)
        if end_date:
            query = query.filter(cls.created_at <= end_date)
        
        return query.order_by(cls.created_at.desc()).all()


# Event listeners for automatic stock validation
@event.listens_for(Product.current_stock, 'set')
def validate_stock_level(target, value, oldvalue, initiator):
    """Validate stock level changes."""
    if target.track_inventory and not target.allow_negative_stock and value < 0:
        raise ValueError(f"Stock cannot be negative for product {target.name}. Current attempt: {value}")


@event.listens_for(Product, 'before_insert')
@event.listens_for(Product, 'before_update')
def validate_product_data(mapper, connection, target):
    """Validate product data before saving."""
    # Ensure prices are not negative
    if target.cost_price is not None and target.cost_price < 0:
        raise ValueError("Cost price cannot be negative")
    if target.selling_price is not None and target.selling_price < 0:
        raise ValueError("Selling price cannot be negative")
    
    # Ensure tax rate is valid
    if target.tax_rate is not None and (target.tax_rate < 0 or target.tax_rate > 1):
        raise ValueError("Tax rate must be between 0 and 1 (0% to 100%)")
    
    # Ensure minimum stock is not negative
    if target.minimum_stock is not None and target.minimum_stock < 0:
        raise ValueError("Minimum stock cannot be negative")