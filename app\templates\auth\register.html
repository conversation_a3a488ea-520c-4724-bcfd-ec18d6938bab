{% extends "base.html" %}

{% block title %}Register - SaaS POS System{% endblock %}

{% block content %}
<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
        <div>
            <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
                Create your business account
            </h2>
            <p class="mt-2 text-center text-sm text-gray-600">
                Or
                <a href="{{ url_for('auth.login') }}" class="font-medium text-indigo-600 hover:text-indigo-500">
                    sign in to your existing account
                </a>
            </p>
        </div>
        
        <!-- Flash messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                <div class="space-y-2">
                    {% for category, message in messages %}
                        <div class="rounded-md p-4 {% if category == 'error' %}bg-red-50 text-red-800 border border-red-200{% elif category == 'success' %}bg-green-50 text-green-800 border border-green-200{% else %}bg-blue-50 text-blue-800 border border-blue-200{% endif %}">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    {% if category == 'error' %}
                                        <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                        </svg>
                                    {% elif category == 'success' %}
                                        <svg class="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                        </svg>
                                    {% else %}
                                        <svg class="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                                        </svg>
                                    {% endif %}
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm font-medium">{{ message }}</p>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            {% endif %}
        {% endwith %}
        
        <form class="mt-8 space-y-6" method="POST">
            {{ form.csrf_token }}
            
            <!-- Business Information -->
            <div class="space-y-4">
                <h3 class="text-lg font-medium text-gray-900">Business Information</h3>
                
                <div>
                    <label for="tenant_name" class="block text-sm font-medium text-gray-700">Business Name</label>
                    <input id="tenant_name" name="tenant_name" type="text" required 
                           class="mt-1 appearance-none relative block w-full px-3 py-2 border {% if form.tenant_name.errors %}border-red-500{% else %}border-gray-300{% endif %} placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" 
                           placeholder="Your Business Name" value="{{ form.tenant_name.data or '' }}">
                    {% if form.tenant_name.errors %}
                        <div class="text-red-500 text-xs mt-1">{{ form.tenant_name.errors[0] }}</div>
                    {% endif %}
                </div>
                
                <div>
                    <label for="business_type" class="block text-sm font-medium text-gray-700">Business Type</label>
                    <select id="business_type" name="business_type" required 
                            class="mt-1 block w-full px-3 py-2 border {% if form.business_type.errors %}border-red-500{% else %}border-gray-300{% endif %} bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                        <option value="retail" {% if form.business_type.data == 'retail' %}selected{% endif %}>Retail Store</option>
                        <option value="restaurant" {% if form.business_type.data == 'restaurant' %}selected{% endif %}>Restaurant</option>
                        <option value="service" {% if form.business_type.data == 'service' %}selected{% endif %}>Service Business</option>
                        <option value="other" {% if form.business_type.data == 'other' %}selected{% endif %}>Other</option>
                    </select>
                    {% if form.business_type.errors %}
                        <div class="text-red-500 text-xs mt-1">{{ form.business_type.errors[0] }}</div>
                    {% endif %}
                </div>
            </div>
            
            <!-- Personal Information -->
            <div class="space-y-4">
                <h3 class="text-lg font-medium text-gray-900">Account Owner Information</h3>
                
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label for="first_name" class="block text-sm font-medium text-gray-700">First Name</label>
                        <input id="first_name" name="first_name" type="text" required 
                               class="mt-1 appearance-none relative block w-full px-3 py-2 border {% if form.first_name.errors %}border-red-500{% else %}border-gray-300{% endif %} placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" 
                               placeholder="First Name" value="{{ form.first_name.data or '' }}">
                        {% if form.first_name.errors %}
                            <div class="text-red-500 text-xs mt-1">{{ form.first_name.errors[0] }}</div>
                        {% endif %}
                    </div>
                    
                    <div>
                        <label for="last_name" class="block text-sm font-medium text-gray-700">Last Name</label>
                        <input id="last_name" name="last_name" type="text" required 
                               class="mt-1 appearance-none relative block w-full px-3 py-2 border {% if form.last_name.errors %}border-red-500{% else %}border-gray-300{% endif %} placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" 
                               placeholder="Last Name" value="{{ form.last_name.data or '' }}">
                        {% if form.last_name.errors %}
                            <div class="text-red-500 text-xs mt-1">{{ form.last_name.errors[0] }}</div>
                        {% endif %}
                    </div>
                </div>
                
                <div>
                    <label for="email" class="block text-sm font-medium text-gray-700">Email Address</label>
                    <input id="email" name="email" type="email" autocomplete="email" required 
                           class="mt-1 appearance-none relative block w-full px-3 py-2 border {% if form.email.errors %}border-red-500{% else %}border-gray-300{% endif %} placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" 
                           placeholder="Email address" value="{{ form.email.data or '' }}">
                    {% if form.email.errors %}
                        <div class="text-red-500 text-xs mt-1">{{ form.email.errors[0] }}</div>
                    {% endif %}
                </div>
                
                <div>
                    <label for="password" class="block text-sm font-medium text-gray-700">Password</label>
                    <input id="password" name="password" type="password" autocomplete="new-password" required 
                           class="mt-1 appearance-none relative block w-full px-3 py-2 border {% if form.password.errors %}border-red-500{% else %}border-gray-300{% endif %} placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" 
                           placeholder="Password (min. 8 characters)">
                    {% if form.password.errors %}
                        <div class="text-red-500 text-xs mt-1">{{ form.password.errors[0] }}</div>
                    {% else %}
                        <p class="mt-1 text-xs text-gray-500">Password must be at least 8 characters long</p>
                    {% endif %}
                </div>
                
                <div>
                    <label for="confirm_password" class="block text-sm font-medium text-gray-700">Confirm Password</label>
                    <input id="confirm_password" name="confirm_password" type="password" autocomplete="new-password" required 
                           class="mt-1 appearance-none relative block w-full px-3 py-2 border {% if form.confirm_password.errors %}border-red-500{% else %}border-gray-300{% endif %} placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" 
                           placeholder="Confirm Password">
                    {% if form.confirm_password.errors %}
                        <div class="text-red-500 text-xs mt-1">{{ form.confirm_password.errors[0] }}</div>
                    {% endif %}
                </div>
            </div>

            <div>
                <button type="submit" 
                        class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                        <svg class="h-5 w-5 text-indigo-500 group-hover:text-indigo-400" viewBox="0 0 20 20" fill="currentColor">
                            <path d="M8 9a3 3 0 100-6 3 3 0 000 6zM8 11a6 6 0 016 6H2a6 6 0 016-6zM16 7a1 1 0 10-2 0v1h-1a1 1 0 100 2h1v1a1 1 0 102 0v-1h1a1 1 0 100-2h-1V7z" />
                        </svg>
                    </span>
                    Create Account
                </button>
            </div>
        </form>
        
        <div class="text-center">
            <p class="text-xs text-gray-500">
                By creating an account, you agree to our Terms of Service and Privacy Policy.
            </p>
        </div>
    </div>
</div>
{% endblock %}