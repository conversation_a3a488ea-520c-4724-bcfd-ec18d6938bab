"""Unit tests for ReportService."""

import pytest
from datetime import datetime, timedelta
from decimal import Decimal
from unittest.mock import patch, MagicMock

from app import create_app, db
from config import config
from app.models.base import set_current_tenant, clear_current_tenant
from app.models.user import User, Tenant
from app.models.product import Product, Category
from app.models.transaction import Transaction, TransactionItem, TransactionStatus, PaymentMethod
from app.services.report_service import ReportService, InvalidDateRangeError


@pytest.fixture
def app():
    """Create application for testing."""
    app = create_app(config['testing'])
    with app.app_context():
        db.create_all()
        yield app
        db.drop_all()


@pytest.fixture
def client(app):
    """Create test client."""
    return app.test_client()


@pytest.fixture
def tenant(app):
    """Create test tenant."""
    tenant = Tenant(
        name='Test Business',
        business_type='retail',
        subscription_status='active'
    )
    tenant.save()
    return tenant


@pytest.fixture
def user(app, tenant):
    """Create test user."""
    user = User.create_user(
        email='<EMAIL>',
        password='password123',
        first_name='Test',
        last_name='User',
        tenant_id=tenant.id,
        role='admin'
    )
    return user


@pytest.fixture
def category(app, tenant):
    """Create test category."""
    set_current_tenant(tenant.id)
    category = Category(
        tenant_id=tenant.id,
        name='Test Category',
        description='Test category description'
    )
    category.save()
    clear_current_tenant()
    return category


@pytest.fixture
def products(app, tenant, category):
    """Create test products."""
    set_current_tenant(tenant.id)
    
    products = []
    for i in range(3):
        product = Product(
            tenant_id=tenant.id,
            name=f'Test Product {i+1}',
            sku=f'TEST-{i+1:03d}',
            category_id=category.id,
            cost_price=Decimal('10.00'),
            selling_price=Decimal('20.00'),
            current_stock=100,
            minimum_stock=10,
            track_inventory=True
        )
        product.save()
        products.append(product)
    
    clear_current_tenant()
    return products


@pytest.fixture
def transactions(app, tenant, user, products):
    """Create test transactions."""
    set_current_tenant(tenant.id)
    
    transactions = []
    base_date = datetime.utcnow() - timedelta(days=5)
    
    for i in range(5):
        transaction = Transaction(
            tenant_id=tenant.id,
            user_id=user.id,
            transaction_number=f'TXN-{i+1:05d}',
            status=TransactionStatus.COMPLETED,
            transaction_date=base_date + timedelta(days=i),
            completed_at=base_date + timedelta(days=i),
            payment_method=PaymentMethod.CASH,
            customer_name=f'Customer {i+1}',
            customer_email=f'customer{i+1}@example.com'
        )
        transaction.save()
        
        # Add items to transaction
        for j, product in enumerate(products[:2]):  # Use first 2 products
            item = TransactionItem(
                transaction_id=transaction.id,
                product_id=product.id,
                tenant_id=tenant.id,
                quantity=j + 1,
                unit_price=product.selling_price,
                product_name=product.name,
                product_sku=product.sku,
                cost_price=product.cost_price,
                tax_rate=Decimal('0.1000')
            )
            item.calculate_totals()
            item.save()
        
        # Recalculate transaction totals
        transaction.calculate_totals()
        transaction.save()
        transactions.append(transaction)
    
    clear_current_tenant()
    return transactions


class TestReportService:
    """Test cases for ReportService."""
    
    def test_get_sales_summary_success(self, app, tenant, transactions):
        """Test successful sales summary generation."""
        with app.app_context():
            set_current_tenant(tenant.id)
            
            end_date = datetime.utcnow()
            start_date = end_date - timedelta(days=30)
            
            result = ReportService.get_sales_summary(start_date, end_date)
            
            assert result['success'] is True
            assert 'summary' in result
            assert 'payment_methods' in result
            assert 'date_range' in result
            
            summary = result['summary']
            assert summary['total_transactions'] == 5
            assert summary['total_revenue'] > 0
            assert summary['avg_transaction_value'] > 0
            assert summary['profit_margin'] > 0
            
            clear_current_tenant()
    
    def test_get_sales_summary_no_tenant(self, app):
        """Test sales summary without tenant context."""
        with app.app_context():
            result = ReportService.get_sales_summary()
            
            assert result['success'] is False
            assert 'No tenant context available' in result['error']
    
    def test_get_sales_summary_invalid_date_range(self, app, tenant):
        """Test sales summary with invalid date range."""
        with app.app_context():
            set_current_tenant(tenant.id)
            
            start_date = datetime.utcnow()
            end_date = start_date - timedelta(days=1)
            
            result = ReportService.get_sales_summary(start_date, end_date)
            
            assert result['success'] is False
            assert 'Start date cannot be after end date' in result['error']
            
            clear_current_tenant()
    
    def test_get_daily_sales_trend_success(self, app, tenant, transactions):
        """Test successful daily sales trend generation."""
        with app.app_context():
            set_current_tenant(tenant.id)
            
            end_date = datetime.utcnow()
            start_date = end_date - timedelta(days=30)
            
            result = ReportService.get_daily_sales_trend(start_date, end_date)
            
            assert result['success'] is True
            assert 'trend_data' in result
            assert 'date_range' in result
            assert isinstance(result['trend_data'], list)
            
            clear_current_tenant()
    
    def test_get_product_performance_report_success(self, app, tenant, transactions):
        """Test successful product performance report generation."""
        with app.app_context():
            set_current_tenant(tenant.id)
            
            end_date = datetime.utcnow()
            start_date = end_date - timedelta(days=30)
            
            result = ReportService.get_product_performance_report(
                start_date, end_date, limit=10, sort_by='revenue'
            )
            
            assert result['success'] is True
            assert 'products' in result
            assert 'summary' in result
            assert 'date_range' in result
            
            products = result['products']
            assert len(products) > 0
            
            # Check product data structure
            product = products[0]
            assert 'product_id' in product
            assert 'product_name' in product
            assert 'total_sold' in product
            assert 'total_revenue' in product
            assert 'total_profit' in product
            assert 'profit_margin' in product
            
            clear_current_tenant()
    
    def test_get_product_performance_report_different_sort(self, app, tenant, transactions):
        """Test product performance report with different sort options."""
        with app.app_context():
            set_current_tenant(tenant.id)
            
            end_date = datetime.utcnow()
            start_date = end_date - timedelta(days=30)
            
            # Test different sort options
            for sort_by in ['quantity', 'profit', 'margin']:
                result = ReportService.get_product_performance_report(
                    start_date, end_date, sort_by=sort_by
                )
                
                assert result['success'] is True
                assert 'products' in result
            
            clear_current_tenant()
    
    def test_get_inventory_report_success(self, app, tenant, products):
        """Test successful inventory report generation."""
        with app.app_context():
            set_current_tenant(tenant.id)
            
            result = ReportService.get_inventory_report(
                low_stock_threshold=20,
                include_out_of_stock=True
            )
            
            assert result['success'] is True
            assert 'inventory_status' in result
            assert 'summary' in result
            assert 'category_breakdown' in result
            
            inventory_status = result['inventory_status']
            assert 'in_stock' in inventory_status
            assert 'low_stock' in inventory_status
            assert 'out_of_stock' in inventory_status
            assert 'overstocked' in inventory_status
            
            summary = result['summary']
            assert summary['total_products'] == 3
            assert summary['total_inventory_value'] > 0
            
            clear_current_tenant()
    
    def test_get_top_customers_report_success(self, app, tenant, transactions):
        """Test successful top customers report generation."""
        with app.app_context():
            set_current_tenant(tenant.id)
            
            end_date = datetime.utcnow()
            start_date = end_date - timedelta(days=30)
            
            result = ReportService.get_top_customers_report(
                start_date, end_date, limit=10
            )
            
            assert result['success'] is True
            assert 'customers' in result
            assert 'summary' in result
            assert 'date_range' in result
            
            customers = result['customers']
            assert len(customers) > 0
            
            # Check customer data structure
            customer = customers[0]
            assert 'customer_name' in customer
            assert 'transaction_count' in customer
            assert 'total_spent' in customer
            assert 'avg_transaction_value' in customer
            
            clear_current_tenant()
    
    def test_get_hourly_sales_pattern_success(self, app, tenant, transactions):
        """Test successful hourly sales pattern generation."""
        with app.app_context():
            set_current_tenant(tenant.id)
            
            end_date = datetime.utcnow()
            start_date = end_date - timedelta(days=7)
            
            result = ReportService.get_hourly_sales_pattern(start_date, end_date)
            
            assert result['success'] is True
            assert 'hourly_pattern' in result
            assert 'peak_hours' in result
            assert 'date_range' in result
            
            hourly_pattern = result['hourly_pattern']
            if hourly_pattern:  # May be empty if no transactions in time range
                hour_data = hourly_pattern[0]
                assert 'hour' in hour_data
                assert 'hour_display' in hour_data
                assert 'transaction_count' in hour_data
                assert 'total_revenue' in hour_data
            
            clear_current_tenant()
    
    def test_get_sales_summary_default_dates(self, app, tenant, transactions):
        """Test sales summary with default date range."""
        with app.app_context():
            set_current_tenant(tenant.id)
            
            result = ReportService.get_sales_summary()
            
            assert result['success'] is True
            assert result['date_range']['days'] == 31  # Default 30 days + 1
            
            clear_current_tenant()
    
    def test_get_inventory_report_low_stock_filtering(self, app, tenant):
        """Test inventory report with low stock products."""
        with app.app_context():
            set_current_tenant(tenant.id)
            
            # Create products with different stock levels
            low_stock_product = Product(
                tenant_id=tenant.id,
                name='Low Stock Product',
                sku='LOW-001',
                cost_price=Decimal('5.00'),
                selling_price=Decimal('10.00'),
                current_stock=2,
                minimum_stock=10,
                track_inventory=True
            )
            low_stock_product.save()
            
            out_of_stock_product = Product(
                tenant_id=tenant.id,
                name='Out of Stock Product',
                sku='OUT-001',
                cost_price=Decimal('5.00'),
                selling_price=Decimal('10.00'),
                current_stock=0,
                minimum_stock=5,
                track_inventory=True
            )
            out_of_stock_product.save()
            
            result = ReportService.get_inventory_report(
                low_stock_threshold=15,
                include_out_of_stock=True
            )
            
            assert result['success'] is True
            
            # Check that products are categorized correctly
            inventory_status = result['inventory_status']
            assert len(inventory_status['low_stock']) >= 1
            assert len(inventory_status['out_of_stock']) >= 1
            
            clear_current_tenant()
    
    def test_error_handling(self, app, tenant):
        """Test error handling in report service."""
        with app.app_context():
            set_current_tenant(tenant.id)
            
            # Test with database error simulation by patching Transaction.query
            with patch('app.services.report_service.Transaction.query') as mock_query:
                mock_query.filter.side_effect = Exception("Database error")
                
                result = ReportService.get_sales_summary()
                
                assert result['success'] is False
                assert 'An error occurred' in result['error']
            
            clear_current_tenant()
    
    def test_empty_data_handling(self, app, tenant):
        """Test report generation with no data."""
        with app.app_context():
            set_current_tenant(tenant.id)
            
            # Test sales summary with no transactions
            result = ReportService.get_sales_summary()
            
            assert result['success'] is True
            assert result['summary']['total_transactions'] == 0
            assert result['summary']['total_revenue'] == 0
            
            # Test product performance with no sales
            result = ReportService.get_product_performance_report()
            
            assert result['success'] is True
            assert len(result['products']) == 0
            
            clear_current_tenant()
    
    def test_date_range_validation(self, app, tenant):
        """Test date range validation across different methods."""
        with app.app_context():
            set_current_tenant(tenant.id)
            
            start_date = datetime.utcnow()
            end_date = start_date - timedelta(days=1)
            
            # Test all methods that accept date ranges
            methods_to_test = [
                ReportService.get_sales_summary,
                ReportService.get_daily_sales_trend,
                ReportService.get_product_performance_report,
                ReportService.get_top_customers_report,
                ReportService.get_hourly_sales_pattern
            ]
            
            for method in methods_to_test:
                result = method(start_date, end_date)
                assert result['success'] is False
                assert 'Start date cannot be after end date' in result['error']
            
            clear_current_tenant()