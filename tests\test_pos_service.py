"""Unit tests for POS service."""

import pytest
from decimal import Decimal
from datetime import datetime
from unittest.mock import patch, MagicMock

from app import create_app, db
from app.models.user import User, Tenant
from app.models.product import Product, Category
from app.models.transaction import Transaction, TransactionItem, TransactionStatus, PaymentMethod
from app.models.base import set_current_tenant, clear_current_tenant
from app.services.pos_service import (
    POSService, POSError, InsufficientStockError, InvalidTransactionError,
    ProductNotFoundError, TransactionNotFoundError, PaymentError
)


@pytest.fixture
def app():
    """Create application for testing."""
    from config import TestingConfig
    app = create_app()
    app.config.from_object(TestingConfig)
    with app.app_context():
        db.create_all()
        yield app
        db.drop_all()


@pytest.fixture
def client(app):
    """Create test client."""
    return app.test_client()


@pytest.fixture
def tenant(app):
    """Create test tenant."""
    tenant = Tenant(
        name='Test Business',
        business_type='retail',
        email='<EMAIL>'
    )
    tenant.save()
    return tenant


@pytest.fixture
def user(app, tenant):
    """Create test user."""
    user = User(
        email='<EMAIL>',
        first_name='Test',
        last_name='User',
        tenant_id=tenant.id,
        role='cashier'
    )
    user.set_password('password123')
    user.save()
    return user


@pytest.fixture
def category(app, tenant):
    """Create test category."""
    set_current_tenant(tenant.id)
    category = Category(
        tenant_id=tenant.id,
        name='Test Category'
    )
    category.save()
    clear_current_tenant()
    return category


@pytest.fixture
def product(app, tenant, category):
    """Create test product."""
    set_current_tenant(tenant.id)
    product = Product(
        tenant_id=tenant.id,
        name='Test Product',
        sku='TEST001',
        category_id=category.id,
        cost_price=Decimal('5.00'),
        selling_price=Decimal('10.00'),
        current_stock=100,
        minimum_stock=10,
        track_inventory=True,
        tax_rate=Decimal('0.0825')
    )
    product.save()
    clear_current_tenant()
    return product


@pytest.fixture
def product_no_stock(app, tenant, category):
    """Create test product with no stock tracking."""
    set_current_tenant(tenant.id)
    product = Product(
        tenant_id=tenant.id,
        name='Service Product',
        sku='SRV001',
        category_id=category.id,
        cost_price=Decimal('0.00'),
        selling_price=Decimal('25.00'),
        current_stock=0,
        minimum_stock=0,
        track_inventory=False,
        tax_rate=Decimal('0.0825')
    )
    product.save()
    clear_current_tenant()
    return product


class TestPOSService:
    """Test cases for POS service."""
    
    def test_create_transaction_success(self, app, tenant, user):
        """Test successful transaction creation."""
        with app.app_context():
            set_current_tenant(tenant.id)
            
            customer_data = {
                'name': 'John Doe',
                'email': '<EMAIL>',
                'phone': '555-1234',
                'table_number': 'T1',
                'order_type': 'dine_in'
            }
            
            result = POSService.create_transaction(user.id, customer_data)
            
            assert result['success'] is True
            assert 'transaction' in result
            
            transaction_data = result['transaction']
            assert transaction_data['user_id'] == user.id
            assert transaction_data['customer_name'] == 'John Doe'
            assert transaction_data['customer_email'] == '<EMAIL>'
            assert transaction_data['customer_phone'] == '555-1234'
            assert transaction_data['table_number'] == 'T1'
            assert transaction_data['order_type'] == 'dine_in'
            assert transaction_data['status'] == 'pending'
            assert transaction_data['total_amount'] == 0.0
            
            clear_current_tenant()
    
    def test_create_transaction_without_customer_data(self, app, tenant, user):
        """Test transaction creation without customer data."""
        with app.app_context():
            set_current_tenant(tenant.id)
            
            result = POSService.create_transaction(user.id)
            
            assert result['success'] is True
            assert result['transaction']['customer_name'] is None
            assert result['transaction']['order_type'] == 'dine_in'
            
            clear_current_tenant()
    
    def test_create_transaction_no_tenant_context(self, app, user):
        """Test transaction creation without tenant context."""
        with app.app_context():
            result = POSService.create_transaction(user.id)
            
            assert result['success'] is False
            assert 'No tenant context available' in result['error']
    
    def test_add_item_to_cart_success(self, app, tenant, user, product):
        """Test successfully adding item to cart."""
        with app.app_context():
            set_current_tenant(tenant.id)
            
            # Create transaction
            transaction_result = POSService.create_transaction(user.id)
            transaction_id = transaction_result['transaction']['id']
            
            # Add item to cart
            result = POSService.add_item_to_cart(transaction_id, product.id, 2)
            
            assert result['success'] is True
            
            transaction_data = result['transaction']
            assert len(transaction_data['items']) == 1
            assert transaction_data['items'][0]['product_id'] == product.id
            assert transaction_data['items'][0]['quantity'] == 2
            assert transaction_data['items'][0]['unit_price'] == 10.0
            assert transaction_data['subtotal'] == 20.0
            assert transaction_data['total_amount'] > 0  # Should include tax
            
            clear_current_tenant()
    
    def test_add_item_to_cart_custom_price(self, app, tenant, user, product):
        """Test adding item with custom price."""
        with app.app_context():
            set_current_tenant(tenant.id)
            
            # Create transaction
            transaction_result = POSService.create_transaction(user.id)
            transaction_id = transaction_result['transaction']['id']
            
            # Add item with custom price
            custom_price = Decimal('15.00')
            result = POSService.add_item_to_cart(transaction_id, product.id, 1, custom_price)
            
            assert result['success'] is True
            assert result['transaction']['items'][0]['unit_price'] == 15.0
            
            clear_current_tenant()
    
    def test_add_item_insufficient_stock(self, app, tenant, user, product):
        """Test adding item with insufficient stock."""
        with app.app_context():
            set_current_tenant(tenant.id)
            
            # Create transaction
            transaction_result = POSService.create_transaction(user.id)
            transaction_id = transaction_result['transaction']['id']
            
            # Try to add more than available stock
            result = POSService.add_item_to_cart(transaction_id, product.id, 150)
            
            assert result['success'] is False
            assert 'Insufficient stock' in result['error']
            
            clear_current_tenant()
    
    def test_add_item_no_stock_tracking(self, app, tenant, user, product_no_stock):
        """Test adding item that doesn't track stock."""
        with app.app_context():
            set_current_tenant(tenant.id)
            
            # Create transaction
            transaction_result = POSService.create_transaction(user.id)
            transaction_id = transaction_result['transaction']['id']
            
            # Add service item (no stock tracking)
            result = POSService.add_item_to_cart(transaction_id, product_no_stock.id, 1)
            
            assert result['success'] is True
            assert result['transaction']['items'][0]['product_id'] == product_no_stock.id
            
            clear_current_tenant()
    
    def test_add_item_transaction_not_found(self, app, tenant, product):
        """Test adding item to non-existent transaction."""
        with app.app_context():
            set_current_tenant(tenant.id)
            
            result = POSService.add_item_to_cart(99999, product.id, 1)
            
            assert result['success'] is False
            assert 'Transaction not found' in result['error']
            
            clear_current_tenant()
    
    def test_add_item_product_not_found(self, app, tenant, user):
        """Test adding non-existent product to cart."""
        with app.app_context():
            set_current_tenant(tenant.id)
            
            # Create transaction
            transaction_result = POSService.create_transaction(user.id)
            transaction_id = transaction_result['transaction']['id']
            
            # Try to add non-existent product
            result = POSService.add_item_to_cart(transaction_id, 99999, 1)
            
            assert result['success'] is False
            assert 'Product not found' in result['error']
            
            clear_current_tenant()
    
    def test_remove_item_from_cart(self, app, tenant, user, product):
        """Test removing item from cart."""
        with app.app_context():
            set_current_tenant(tenant.id)
            
            # Create transaction and add item
            transaction_result = POSService.create_transaction(user.id)
            transaction_id = transaction_result['transaction']['id']
            POSService.add_item_to_cart(transaction_id, product.id, 2)
            
            # Remove item
            result = POSService.remove_item_from_cart(transaction_id, product.id)
            
            assert result['success'] is True
            assert len(result['transaction']['items']) == 0
            assert result['transaction']['total_amount'] == 0.0
            
            clear_current_tenant()
    
    def test_update_item_quantity(self, app, tenant, user, product):
        """Test updating item quantity."""
        with app.app_context():
            set_current_tenant(tenant.id)
            
            # Create transaction and add item
            transaction_result = POSService.create_transaction(user.id)
            transaction_id = transaction_result['transaction']['id']
            POSService.add_item_to_cart(transaction_id, product.id, 2)
            
            # Update quantity
            result = POSService.update_item_quantity(transaction_id, product.id, 5)
            
            assert result['success'] is True
            assert result['transaction']['items'][0]['quantity'] == 5
            
            clear_current_tenant()
    
    def test_update_item_quantity_to_zero(self, app, tenant, user, product):
        """Test updating item quantity to zero (removes item)."""
        with app.app_context():
            set_current_tenant(tenant.id)
            
            # Create transaction and add item
            transaction_result = POSService.create_transaction(user.id)
            transaction_id = transaction_result['transaction']['id']
            POSService.add_item_to_cart(transaction_id, product.id, 2)
            
            # Update quantity to zero
            result = POSService.update_item_quantity(transaction_id, product.id, 0)
            
            assert result['success'] is True
            assert len(result['transaction']['items']) == 0
            
            clear_current_tenant()
    
    def test_apply_percentage_discount(self, app, tenant, user, product):
        """Test applying percentage discount."""
        with app.app_context():
            set_current_tenant(tenant.id)
            
            # Create transaction and add item
            transaction_result = POSService.create_transaction(user.id)
            transaction_id = transaction_result['transaction']['id']
            POSService.add_item_to_cart(transaction_id, product.id, 2)  # $20 subtotal
            
            # Apply 10% discount
            result = POSService.apply_discount(transaction_id, 'percentage', Decimal('10'), 'Customer discount')
            
            assert result['success'] is True
            assert result['transaction']['discount_type'] == 'percentage'
            assert result['transaction']['discount_value'] == 10.0
            assert result['transaction']['discount_amount'] == 2.0  # 10% of $20
            assert result['transaction']['discount_reason'] == 'Customer discount'
            
            clear_current_tenant()
    
    def test_apply_fixed_amount_discount(self, app, tenant, user, product):
        """Test applying fixed amount discount."""
        with app.app_context():
            set_current_tenant(tenant.id)
            
            # Create transaction and add item
            transaction_result = POSService.create_transaction(user.id)
            transaction_id = transaction_result['transaction']['id']
            POSService.add_item_to_cart(transaction_id, product.id, 2)  # $20 subtotal
            
            # Apply $5 discount
            result = POSService.apply_discount(transaction_id, 'fixed_amount', Decimal('5'))
            
            assert result['success'] is True
            assert result['transaction']['discount_type'] == 'fixed_amount'
            assert result['transaction']['discount_value'] == 5.0
            assert result['transaction']['discount_amount'] == 5.0
            
            clear_current_tenant()
    
    def test_apply_invalid_discount_type(self, app, tenant, user, product):
        """Test applying invalid discount type."""
        with app.app_context():
            set_current_tenant(tenant.id)
            
            # Create transaction and add item
            transaction_result = POSService.create_transaction(user.id)
            transaction_id = transaction_result['transaction']['id']
            POSService.add_item_to_cart(transaction_id, product.id, 1)
            
            # Apply invalid discount type
            result = POSService.apply_discount(transaction_id, 'invalid_type', Decimal('10'))
            
            assert result['success'] is False
            assert 'Invalid discount type' in result['error']
            
            clear_current_tenant()
    
    def test_process_payment_success(self, app, tenant, user, product):
        """Test successful payment processing."""
        with app.app_context():
            set_current_tenant(tenant.id)
            
            # Create transaction and add item
            transaction_result = POSService.create_transaction(user.id)
            transaction_id = transaction_result['transaction']['id']
            POSService.add_item_to_cart(transaction_id, product.id, 1)
            
            # Get total amount
            transaction = Transaction.query.get(transaction_id)
            total_amount = transaction.total_amount
            
            # Process payment
            result = POSService.process_payment(transaction_id, 'cash', total_amount + Decimal('5.00'))
            
            assert result['success'] is True
            assert result['transaction']['status'] == 'completed'
            assert result['transaction']['payment_method'] == 'cash'
            assert result['change_due'] == 5.0
            
            clear_current_tenant()
    
    def test_process_payment_insufficient_amount(self, app, tenant, user, product):
        """Test payment with insufficient amount."""
        with app.app_context():
            set_current_tenant(tenant.id)
            
            # Create transaction and add item
            transaction_result = POSService.create_transaction(user.id)
            transaction_id = transaction_result['transaction']['id']
            POSService.add_item_to_cart(transaction_id, product.id, 1)
            
            # Try to pay less than total
            result = POSService.process_payment(transaction_id, 'cash', Decimal('5.00'))
            
            assert result['success'] is False
            assert 'Insufficient payment' in result['error']
            
            clear_current_tenant()
    
    def test_process_payment_invalid_method(self, app, tenant, user, product):
        """Test payment with invalid payment method."""
        with app.app_context():
            set_current_tenant(tenant.id)
            
            # Create transaction and add item
            transaction_result = POSService.create_transaction(user.id)
            transaction_id = transaction_result['transaction']['id']
            POSService.add_item_to_cart(transaction_id, product.id, 1)
            
            # Try invalid payment method
            result = POSService.process_payment(transaction_id, 'invalid_method', Decimal('20.00'))
            
            assert result['success'] is False
            assert 'Invalid payment method' in result['error']
            
            clear_current_tenant()
    
    def test_cancel_transaction(self, app, tenant, user, product):
        """Test transaction cancellation."""
        with app.app_context():
            set_current_tenant(tenant.id)
            
            # Create transaction and add item
            transaction_result = POSService.create_transaction(user.id)
            transaction_id = transaction_result['transaction']['id']
            POSService.add_item_to_cart(transaction_id, product.id, 1)
            
            # Cancel transaction
            result = POSService.cancel_transaction(transaction_id, 'Customer changed mind')
            
            assert result['success'] is True
            assert result['transaction']['status'] == 'cancelled'
            
            clear_current_tenant()
    
    def test_get_transaction(self, app, tenant, user, product):
        """Test getting transaction by ID."""
        with app.app_context():
            set_current_tenant(tenant.id)
            
            # Create transaction
            transaction_result = POSService.create_transaction(user.id)
            transaction_id = transaction_result['transaction']['id']
            
            # Get transaction
            result = POSService.get_transaction(transaction_id)
            
            assert result is not None
            assert result['id'] == transaction_id
            assert result['user_id'] == user.id
            
            clear_current_tenant()
    
    def test_get_pending_transactions(self, app, tenant, user):
        """Test getting pending transactions."""
        with app.app_context():
            set_current_tenant(tenant.id)
            
            # Create multiple transactions
            POSService.create_transaction(user.id)
            POSService.create_transaction(user.id)
            
            # Get pending transactions
            result = POSService.get_pending_transactions()
            
            assert len(result) == 2
            assert all(t['status'] == 'pending' for t in result)
            
            clear_current_tenant()
    
    def test_generate_receipt(self, app, tenant, user, product):
        """Test receipt generation."""
        with app.app_context():
            set_current_tenant(tenant.id)
            
            # Create and complete transaction
            transaction_result = POSService.create_transaction(user.id, {'name': 'John Doe'})
            transaction_id = transaction_result['transaction']['id']
            POSService.add_item_to_cart(transaction_id, product.id, 2)
            
            # Complete transaction
            transaction = Transaction.query.get(transaction_id)
            POSService.process_payment(transaction_id, 'cash', transaction.total_amount)
            
            # Generate receipt
            result = POSService.generate_receipt(transaction_id)
            
            assert result['success'] is True
            assert 'receipt' in result
            
            receipt = result['receipt']
            assert receipt['transaction_number'] is not None
            assert receipt['receipt_number'] is not None
            assert receipt['customer']['name'] == 'John Doe'
            assert len(receipt['items']) == 1
            assert receipt['items'][0]['name'] == 'Test Product'
            assert receipt['items'][0]['quantity'] == 2
            assert receipt['totals']['total_amount'] > 0
            assert receipt['payment']['method'] == 'cash'
            assert receipt['payment']['status'] == 'completed'
            
            clear_current_tenant()