"""Integration tests for POS routes."""

import pytest
import json
from decimal import Decimal
from flask import url_for

from app import create_app, db
from app.models.user import User, Tenant
from app.models.product import Product, Category
from app.models.transaction import Transaction, TransactionStatus
from app.models.base import set_current_tenant, clear_current_tenant
from config import TestingConfig


@pytest.fixture
def app():
    """Create application for testing."""
    app = create_app()
    app.config.from_object(TestingConfig)
    with app.app_context():
        db.create_all()
        yield app
        db.drop_all()


@pytest.fixture
def client(app):
    """Create test client."""
    return app.test_client()


@pytest.fixture
def tenant(app):
    """Create test tenant."""
    tenant = Tenant(
        name='Test Business',
        business_type='retail',
        email='<EMAIL>'
    )
    tenant.save()
    return tenant


@pytest.fixture
def user(app, tenant):
    """Create test user with cashier role."""
    user = User(
        email='<EMAIL>',
        first_name='Test',
        last_name='Cashier',
        tenant_id=tenant.id,
        role='cashier'
    )
    user.set_password('password123')
    user.save()
    return user


@pytest.fixture
def admin_user(app, tenant):
    """Create test admin user."""
    user = User(
        email='<EMAIL>',
        first_name='Test',
        last_name='Admin',
        tenant_id=tenant.id,
        role='admin'
    )
    user.set_password('password123')
    user.save()
    return user


@pytest.fixture
def category(app, tenant):
    """Create test category."""
    set_current_tenant(tenant.id)
    category = Category(
        tenant_id=tenant.id,
        name='Test Category'
    )
    category.save()
    clear_current_tenant()
    return category


@pytest.fixture
def product(app, tenant, category):
    """Create test product."""
    set_current_tenant(tenant.id)
    product = Product(
        tenant_id=tenant.id,
        name='Test Product',
        sku='TEST001',
        category_id=category.id,
        cost_price=Decimal('5.00'),
        selling_price=Decimal('10.00'),
        current_stock=100,
        minimum_stock=10,
        track_inventory=True,
        tax_rate=Decimal('0.0825'),
        is_featured=True
    )
    product.save()
    clear_current_tenant()
    return product


@pytest.fixture
def authenticated_client(client, user):
    """Create authenticated client."""
    with client.session_transaction() as sess:
        sess['_user_id'] = str(user.id)
        sess['_fresh'] = True
    return client


class TestPOSRoutes:
    """Test cases for POS routes."""
    
    def test_dashboard_requires_login(self, client):
        """Test that dashboard requires login."""
        response = client.get('/pos/dashboard')
        assert response.status_code == 302  # Redirect to login
    
    def test_dashboard_requires_pos_permission(self, client, app, tenant):
        """Test that dashboard requires POS permission."""
        # Create user without POS permission
        with app.app_context():
            user = User(
                email='<EMAIL>',
                first_name='No',
                last_name='Permission',
                tenant_id=tenant.id,
                role='viewer'  # No POS permission
            )
            user.set_password('password123')
            user.save()
            
            with client.session_transaction() as sess:
                sess['_user_id'] = str(user.id)
                sess['_fresh'] = True
            
            response = client.get('/pos/dashboard')
            assert response.status_code == 302  # Redirect due to no permission
    
    def test_dashboard_loads_successfully(self, authenticated_client, product):
        """Test that dashboard loads successfully for authorized user."""
        response = authenticated_client.get('/pos/dashboard')
        assert response.status_code == 200
        assert b'POS System' in response.data
        assert b'Test Product' in response.data  # Featured product should appear
    
    def test_get_products_ajax(self, authenticated_client, product):
        """Test getting products via AJAX."""
        response = authenticated_client.get('/pos/products')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert 'products' in data
        assert len(data['products']) > 0
        assert data['products'][0]['name'] == 'Test Product'
        assert data['products'][0]['selling_price'] == 10.0
    
    def test_get_products_with_search(self, authenticated_client, product):
        """Test getting products with search filter."""
        response = authenticated_client.get('/pos/products?search=Test')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert len(data['products']) > 0
        assert 'Test' in data['products'][0]['name']
    
    def test_get_products_with_category_filter(self, authenticated_client, product, category):
        """Test getting products with category filter."""
        response = authenticated_client.get(f'/pos/products?category_id={category.id}')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert len(data['products']) > 0
    
    def test_create_transaction(self, authenticated_client):
        """Test creating a new transaction."""
        response = authenticated_client.post('/pos/transaction/create',
                                           json={})
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert data['success'] is True
        assert 'transaction' in data
        assert data['transaction']['status'] == 'pending'
    
    def test_create_transaction_with_customer_data(self, authenticated_client):
        """Test creating transaction with customer data."""
        customer_data = {
            'customer_name': 'John Doe',
            'customer_email': '<EMAIL>',
            'customer_phone': '555-1234',
            'table_number': 'T1'
        }
        
        response = authenticated_client.post('/pos/transaction/create',
                                           json=customer_data)
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert data['success'] is True
        assert data['transaction']['customer_name'] == 'John Doe'
        assert data['transaction']['customer_email'] == '<EMAIL>'
    
    def test_get_transaction(self, authenticated_client, app, user):
        """Test getting transaction details."""
        with app.app_context():
            set_current_tenant(user.tenant_id)
            
            # Create transaction
            transaction = Transaction(
                tenant_id=user.tenant_id,
                user_id=user.id,
                transaction_number='TEST-001'
            )
            transaction.save()
            
            response = authenticated_client.get(f'/pos/transaction/{transaction.id}')
            assert response.status_code == 200
            
            data = json.loads(response.data)
            assert 'transaction' in data
            assert data['transaction']['transaction_number'] == 'TEST-001'
            
            clear_current_tenant()
    
    def test_add_item_to_cart(self, authenticated_client, app, user, product):
        """Test adding item to cart."""
        with app.app_context():
            set_current_tenant(user.tenant_id)
            
            # Create transaction
            transaction = Transaction(
                tenant_id=user.tenant_id,
                user_id=user.id,
                transaction_number='TEST-001'
            )
            transaction.save()
            
            # Add item to cart
            response = authenticated_client.post(f'/pos/transaction/{transaction.id}/add_item',
                                               json={
                                                   'product_id': product.id,
                                                   'quantity': 2
                                               })
            assert response.status_code == 200
            
            data = json.loads(response.data)
            assert data['success'] is True
            assert len(data['transaction']['items']) == 1
            assert data['transaction']['items'][0]['quantity'] == 2
            
            clear_current_tenant()
    
    def test_add_item_insufficient_stock(self, authenticated_client, app, user, product):
        """Test adding item with insufficient stock."""
        with app.app_context():
            set_current_tenant(user.tenant_id)
            
            # Create transaction
            transaction = Transaction(
                tenant_id=user.tenant_id,
                user_id=user.id,
                transaction_number='TEST-001'
            )
            transaction.save()
            
            # Try to add more than available stock
            response = authenticated_client.post(f'/pos/transaction/{transaction.id}/add_item',
                                               json={
                                                   'product_id': product.id,
                                                   'quantity': 150  # More than available
                                               })
            assert response.status_code == 400
            
            data = json.loads(response.data)
            assert 'Insufficient stock' in data['error']
            
            clear_current_tenant()
    
    def test_remove_item_from_cart(self, authenticated_client, app, user, product):
        """Test removing item from cart."""
        with app.app_context():
            set_current_tenant(user.tenant_id)
            
            # Create transaction with item
            transaction = Transaction(
                tenant_id=user.tenant_id,
                user_id=user.id,
                transaction_number='TEST-001'
            )
            transaction.save()
            transaction.add_item(product, 2)
            
            # Remove item
            response = authenticated_client.post(f'/pos/transaction/{transaction.id}/remove_item',
                                               json={'product_id': product.id})
            assert response.status_code == 200
            
            data = json.loads(response.data)
            assert data['success'] is True
            assert len(data['transaction']['items']) == 0
            
            clear_current_tenant()
    
    def test_update_item_quantity(self, authenticated_client, app, user, product):
        """Test updating item quantity."""
        with app.app_context():
            set_current_tenant(user.tenant_id)
            
            # Create transaction with item
            transaction = Transaction(
                tenant_id=user.tenant_id,
                user_id=user.id,
                transaction_number='TEST-001'
            )
            transaction.save()
            transaction.add_item(product, 2)
            
            # Update quantity
            response = authenticated_client.post(f'/pos/transaction/{transaction.id}/update_quantity',
                                               json={
                                                   'product_id': product.id,
                                                   'quantity': 5
                                               })
            assert response.status_code == 200
            
            data = json.loads(response.data)
            assert data['success'] is True
            assert data['transaction']['items'][0]['quantity'] == 5
            
            clear_current_tenant()
    
    def test_apply_discount(self, authenticated_client, app, user, product):
        """Test applying discount to transaction."""
        with app.app_context():
            set_current_tenant(user.tenant_id)
            
            # Create transaction with item
            transaction = Transaction(
                tenant_id=user.tenant_id,
                user_id=user.id,
                transaction_number='TEST-001'
            )
            transaction.save()
            transaction.add_item(product, 2)  # $20 subtotal
            
            # Apply 10% discount
            response = authenticated_client.post(f'/pos/transaction/{transaction.id}/apply_discount',
                                               json={
                                                   'discount_type': 'percentage',
                                                   'discount_value': 10,
                                                   'reason': 'Test discount'
                                               })
            assert response.status_code == 200
            
            data = json.loads(response.data)
            assert data['success'] is True
            assert data['transaction']['discount_type'] == 'percentage'
            assert data['transaction']['discount_amount'] == 2.0
            
            clear_current_tenant()
    
    def test_process_payment(self, authenticated_client, app, user, product):
        """Test processing payment."""
        with app.app_context():
            set_current_tenant(user.tenant_id)
            
            # Create transaction with item
            transaction = Transaction(
                tenant_id=user.tenant_id,
                user_id=user.id,
                transaction_number='TEST-001'
            )
            transaction.save()
            transaction.add_item(product, 1)
            
            # Get total amount
            db.session.refresh(transaction)
            total_amount = float(transaction.total_amount)
            
            # Process payment
            response = authenticated_client.post(f'/pos/transaction/{transaction.id}/process_payment',
                                               json={
                                                   'payment_method': 'cash',
                                                   'amount_paid': total_amount + 5.0
                                               })
            assert response.status_code == 200
            
            data = json.loads(response.data)
            assert data['success'] is True
            assert data['transaction']['status'] == 'completed'
            assert data['change_due'] == 5.0
            
            clear_current_tenant()
    
    def test_process_payment_insufficient_amount(self, authenticated_client, app, user, product):
        """Test processing payment with insufficient amount."""
        with app.app_context():
            set_current_tenant(user.tenant_id)
            
            # Create transaction with item
            transaction = Transaction(
                tenant_id=user.tenant_id,
                user_id=user.id,
                transaction_number='TEST-001'
            )
            transaction.save()
            transaction.add_item(product, 1)
            
            # Try to pay less than total
            response = authenticated_client.post(f'/pos/transaction/{transaction.id}/process_payment',
                                               json={
                                                   'payment_method': 'cash',
                                                   'amount_paid': 5.0  # Less than total
                                               })
            assert response.status_code == 400
            
            data = json.loads(response.data)
            assert 'Insufficient payment' in data['error']
            
            clear_current_tenant()
    
    def test_cancel_transaction(self, authenticated_client, app, user, product):
        """Test cancelling transaction."""
        with app.app_context():
            set_current_tenant(user.tenant_id)
            
            # Create transaction with item
            transaction = Transaction(
                tenant_id=user.tenant_id,
                user_id=user.id,
                transaction_number='TEST-001'
            )
            transaction.save()
            transaction.add_item(product, 1)
            
            # Cancel transaction
            response = authenticated_client.post(f'/pos/transaction/{transaction.id}/cancel',
                                               json={'reason': 'Test cancellation'})
            assert response.status_code == 200
            
            data = json.loads(response.data)
            assert data['success'] is True
            assert data['transaction']['status'] == 'cancelled'
            
            clear_current_tenant()
    
    def test_checkout_page(self, authenticated_client, app, user, product):
        """Test checkout page loads correctly."""
        with app.app_context():
            set_current_tenant(user.tenant_id)
            
            # Create transaction with item
            transaction = Transaction(
                tenant_id=user.tenant_id,
                user_id=user.id,
                transaction_number='TEST-001'
            )
            transaction.save()
            transaction.add_item(product, 1)
            
            response = authenticated_client.get(f'/pos/checkout/{transaction.id}')
            assert response.status_code == 200
            assert b'Checkout' in response.data
            assert b'TEST-001' in response.data
            
            clear_current_tenant()
    
    def test_checkout_nonexistent_transaction(self, authenticated_client):
        """Test checkout page with non-existent transaction."""
        response = authenticated_client.get('/pos/checkout/99999')
        assert response.status_code == 302  # Redirect to dashboard
    
    def test_generate_receipt(self, authenticated_client, app, user, product):
        """Test receipt generation."""
        with app.app_context():
            set_current_tenant(user.tenant_id)
            
            # Create and complete transaction
            transaction = Transaction(
                tenant_id=user.tenant_id,
                user_id=user.id,
                transaction_number='TEST-001',
                status=TransactionStatus.COMPLETED
            )
            transaction.save()
            transaction.add_item(product, 1)
            
            response = authenticated_client.get(f'/pos/transaction/{transaction.id}/receipt')
            assert response.status_code == 200
            assert b'Receipt' in response.data
            assert b'TEST-001' in response.data
            
            clear_current_tenant()
    
    def test_print_receipt_json(self, authenticated_client, app, user, product):
        """Test receipt generation in JSON format for printing."""
        with app.app_context():
            set_current_tenant(user.tenant_id)
            
            # Create and complete transaction
            transaction = Transaction(
                tenant_id=user.tenant_id,
                user_id=user.id,
                transaction_number='TEST-001',
                status=TransactionStatus.COMPLETED
            )
            transaction.save()
            transaction.add_item(product, 1)
            
            response = authenticated_client.get(f'/pos/transaction/{transaction.id}/receipt/print')
            assert response.status_code == 200
            
            data = json.loads(response.data)
            assert data['success'] is True
            assert 'receipt' in data
            assert data['receipt']['transaction_number'] == 'TEST-001'
            
            clear_current_tenant()
    
    def test_get_pending_transactions(self, authenticated_client, app, user):
        """Test getting pending transactions."""
        with app.app_context():
            set_current_tenant(user.tenant_id)
            
            # Create pending transaction
            transaction = Transaction(
                tenant_id=user.tenant_id,
                user_id=user.id,
                transaction_number='TEST-001',
                status=TransactionStatus.PENDING
            )
            transaction.save()
            
            response = authenticated_client.get('/pos/transactions/pending')
            assert response.status_code == 200
            
            data = json.loads(response.data)
            assert 'transactions' in data
            assert len(data['transactions']) > 0
            assert data['transactions'][0]['status'] == 'pending'
            
            clear_current_tenant()