"""
Example of how to integrate audit logging into existing routes.

This file shows how to add audit logging to your existing POS system routes
using both decorators and manual logging approaches.
"""

from flask import Blueprint, request, jsonify, flash, redirect, url_for
from flask_login import login_required, current_user
from app.models.audit import AuditAction, AuditSeverity
from app.services.audit_service import AuditService
from app.utils.audit_decorators import (
    audit_action, audit_data_change, audit_authentication, 
    audit_transaction, audit_security_event
)

# Example blueprint
example_bp = Blueprint('example', __name__)


# Example 1: Using decorators for automatic audit logging
@example_bp.route('/products', methods=['POST'])
@login_required
@audit_action(
    action=AuditAction.PRODUCT_CREATED,
    description="Product '{result.name}' created with price ${result.price}",
    severity=AuditSeverity.MEDIUM,
    resource_type='product'
)
def create_product():
    """Create a new product with automatic audit logging."""
    from app.models.product import Product
    
    data = request.get_json()
    
    product = Product(
        name=data['name'],
        price=data['price'],
        tenant_id=current_user.tenant_id
    )
    product.save()
    
    return product  # Decorator will automatically log this


@example_bp.route('/products/<int:product_id>', methods=['PUT'])
@login_required
@audit_data_change(
    action=AuditAction.PRODUCT_UPDATED,
    resource_type='product',
    get_resource_id=lambda args, kwargs, result: args[0],  # product_id from URL
    get_old_values=lambda args, kwargs: Product.query.get(args[0]).to_dict()
)
def update_product(product_id):
    """Update a product with before/after value logging."""
    from app.models.product import Product
    
    product = Product.query.get_or_404(product_id)
    data = request.get_json()
    
    # Update product
    product.name = data.get('name', product.name)
    product.price = data.get('price', product.price)
    product.save()
    
    return product  # Decorator will log old vs new values


@example_bp.route('/auth/login', methods=['POST'])
@audit_authentication(AuditAction.LOGIN)
def login():
    """Login with automatic authentication audit logging."""
    from app.models.user import User
    
    email = request.form['email']
    password = request.form['password']
    tenant_id = request.form['tenant_id']
    
    user = User.authenticate(email, password, tenant_id)
    if not user:
        raise ValueError("Invalid credentials")  # Decorator will log this failure
    
    # Login user logic here...
    return user  # Decorator will log successful login


@example_bp.route('/transactions', methods=['POST'])
@login_required
@audit_transaction(AuditAction.TRANSACTION_CREATED)
def create_transaction():
    """Create transaction with automatic audit logging."""
    from app.models.transaction import Transaction
    
    data = request.get_json()
    
    transaction = Transaction(
        total_amount=data['total_amount'],
        tenant_id=current_user.tenant_id,
        user_id=current_user.id
    )
    transaction.save()
    
    return transaction  # Decorator will log transaction details


@example_bp.route('/admin/sensitive-data')
@login_required
@audit_security_event(AuditAction.UNAUTHORIZED_ACCESS, AuditSeverity.CRITICAL)
def sensitive_endpoint():
    """Protected endpoint that logs security events on unauthorized access."""
    
    if not current_user.is_admin():
        raise PermissionError("Admin access required")  # Will trigger security audit
    
    # Sensitive operations here...
    return {"data": "sensitive information"}


# Example 2: Manual audit logging for complex scenarios
@example_bp.route('/inventory/adjust', methods=['POST'])
@login_required
def adjust_inventory():
    """Manual audit logging for complex inventory adjustments."""
    from app.models.product import Product, InventoryMovement
    
    data = request.get_json()
    product_id = data['product_id']
    adjustment = data['adjustment']
    reason = data['reason']
    
    try:
        product = Product.query.get_or_404(product_id)
        old_quantity = product.quantity
        
        # Perform inventory adjustment
        product.quantity += adjustment
        product.save()
        
        # Create inventory movement record
        movement = InventoryMovement(
            product_id=product_id,
            movement_type='adjustment',
            quantity=adjustment,
            reason=reason,
            tenant_id=current_user.tenant_id,
            user_id=current_user.id
        )
        movement.save()
        
        # Manual audit logging with detailed information
        AuditService.log_data_change(
            action=AuditAction.INVENTORY_ADJUSTED,
            resource_type='product',
            resource_id=product_id,
            description=f"Inventory adjusted for {product.name}: {old_quantity} → {product.quantity} (Reason: {reason})",
            old_values={'quantity': old_quantity},
            new_values={'quantity': product.quantity, 'adjustment': adjustment, 'reason': reason}
        )
        
        return jsonify({
            'success': True,
            'product_id': product_id,
            'old_quantity': old_quantity,
            'new_quantity': product.quantity,
            'adjustment': adjustment
        })
        
    except Exception as e:
        # Log the failure
        AuditService.log(
            action=AuditAction.INVENTORY_ADJUSTED,
            description=f"Inventory adjustment failed for product {product_id}: {str(e)}",
            severity=AuditSeverity.HIGH,
            resource_type='product',
            resource_id=product_id,
            success=False,
            error_message=str(e)
        )
        
        return jsonify({'error': str(e)}), 400


@example_bp.route('/reports/sales')
@login_required
def generate_sales_report():
    """Example of logging data export/access events."""
    
    # Check permissions
    if not current_user.can_access_reports():
        AuditService.log_security_event(
            action=AuditAction.UNAUTHORIZED_ACCESS,
            description=f"User {current_user.email} attempted to access sales reports without permission",
            severity=AuditSeverity.HIGH
        )
        return jsonify({'error': 'Unauthorized'}), 403
    
    try:
        # Generate report logic here...
        report_data = {
            'total_sales': 10000.00,
            'transaction_count': 150,
            'period': '2024-01-01 to 2024-01-31'
        }
        
        # Log the report access
        AuditService.log(
            action=AuditAction.DATA_EXPORTED,
            description=f"Sales report generated for period {report_data['period']}",
            severity=AuditSeverity.MEDIUM,
            resource_type='report',
            new_values={
                'report_type': 'sales',
                'period': report_data['period'],
                'total_sales': report_data['total_sales'],
                'transaction_count': report_data['transaction_count']
            }
        )
        
        return jsonify(report_data)
        
    except Exception as e:
        AuditService.log(
            action=AuditAction.DATA_EXPORTED,
            description=f"Sales report generation failed: {str(e)}",
            severity=AuditSeverity.HIGH,
            success=False,
            error_message=str(e)
        )
        
        return jsonify({'error': 'Report generation failed'}), 500


# Example 3: Bulk operations with correlation IDs
@example_bp.route('/products/bulk-update', methods=['POST'])
@login_required
def bulk_update_products():
    """Example of using correlation IDs to track related operations."""
    import uuid
    
    data = request.get_json()
    product_updates = data['products']  # List of product updates
    
    # Generate correlation ID for this bulk operation
    correlation_id = str(uuid.uuid4())
    
    # Log the start of bulk operation
    AuditService.log(
        action=AuditAction.PRODUCT_UPDATED,
        description=f"Bulk product update started - {len(product_updates)} products",
        severity=AuditSeverity.MEDIUM,
        correlation_id=correlation_id,
        new_values={'product_count': len(product_updates)}
    )
    
    updated_products = []
    failed_updates = []
    
    for update_data in product_updates:
        try:
            product_id = update_data['id']
            product = Product.query.get(product_id)
            
            if product:
                old_values = {'name': product.name, 'price': product.price}
                
                # Update product
                product.name = update_data.get('name', product.name)
                product.price = update_data.get('price', product.price)
                product.save()
                
                # Log individual update with same correlation ID
                AuditService.log_data_change(
                    action=AuditAction.PRODUCT_UPDATED,
                    resource_type='product',
                    resource_id=product_id,
                    description=f"Product {product.name} updated in bulk operation",
                    old_values=old_values,
                    new_values={'name': product.name, 'price': product.price},
                    correlation_id=correlation_id
                )
                
                updated_products.append(product_id)
            
        except Exception as e:
            failed_updates.append({'id': update_data['id'], 'error': str(e)})
            
            # Log individual failure
            AuditService.log(
                action=AuditAction.PRODUCT_UPDATED,
                description=f"Product update failed in bulk operation: {str(e)}",
                severity=AuditSeverity.HIGH,
                resource_type='product',
                resource_id=update_data['id'],
                success=False,
                error_message=str(e),
                correlation_id=correlation_id
            )
    
    # Log completion of bulk operation
    AuditService.log(
        action=AuditAction.PRODUCT_UPDATED,
        description=f"Bulk product update completed - {len(updated_products)} successful, {len(failed_updates)} failed",
        severity=AuditSeverity.MEDIUM,
        correlation_id=correlation_id,
        success=len(failed_updates) == 0,
        new_values={
            'successful_updates': len(updated_products),
            'failed_updates': len(failed_updates),
            'updated_product_ids': updated_products
        }
    )
    
    return jsonify({
        'updated': updated_products,
        'failed': failed_updates,
        'correlation_id': correlation_id
    })


# Example 4: Suspicious activity detection
@example_bp.route('/api/data')
@login_required
def api_endpoint():
    """Example endpoint that monitors for suspicious activity."""
    
    # Check for suspicious patterns
    from datetime import datetime, timedelta
    
    # Check if user has made too many requests recently
    recent_logs = AuditService.get_user_activity(
        user_id=current_user.id,
        tenant_id=current_user.tenant_id,
        days=1
    )
    
    api_requests_today = len([log for log in recent_logs 
                             if 'api_endpoint' in log.description])
    
    if api_requests_today > 100:  # Threshold for suspicious activity
        AuditService.log_security_event(
            action=AuditAction.SUSPICIOUS_ACTIVITY,
            description=f"User {current_user.email} made {api_requests_today} API requests today",
            severity=AuditSeverity.MEDIUM,
            additional_data={
                'request_count': api_requests_today,
                'threshold': 100,
                'endpoint': 'api_endpoint'
            }
        )
    
    # Log normal API access
    AuditService.log(
        action=AuditAction.DATA_EXPORTED,
        description="API data endpoint accessed",
        severity=AuditSeverity.LOW,
        resource_type='api'
    )
    
    return jsonify({'data': 'api response'})


# Example usage in your existing routes:
"""
To integrate audit logging into your existing POS system:

1. Import the necessary modules:
   from app.services.audit_service import AuditService
   from app.models.audit import AuditAction, AuditSeverity
   from app.utils.audit_decorators import audit_action, audit_data_change

2. Add decorators to your existing routes:
   @audit_action(AuditAction.PRODUCT_CREATED, "Product created", AuditSeverity.MEDIUM)
   def your_existing_route():
       # your existing code
       return result

3. Or add manual logging:
   AuditService.log(
       action=AuditAction.PRODUCT_CREATED,
       description="Product created successfully",
       severity=AuditSeverity.MEDIUM
   )

4. For authentication routes, use:
   @audit_authentication(AuditAction.LOGIN)

5. For transaction routes, use:
   @audit_transaction(AuditAction.TRANSACTION_CREATED)

6. For sensitive/admin routes, use:
   @audit_security_event(AuditAction.UNAUTHORIZED_ACCESS, AuditSeverity.CRITICAL)
"""