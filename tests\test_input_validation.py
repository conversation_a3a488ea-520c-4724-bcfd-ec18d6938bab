"""Tests for input validation utilities."""

import pytest
from wtforms import ValidationError
from decimal import Decimal

from app.utils.validators import (
    validate_email, validate_password_strength, validate_phone_number,
    validate_positive_number, validate_non_negative_number, validate_business_name,
    validate_product_name, validate_sku, validate_barcode, validate_price,
    validate_stock_quantity, validate_tax_rate, validate_discount_value,
    validate_name_field, validate_tenant_id, validate_url, validate_color_hex,
    sanitize_input, validate_json_input, validate_search_query,
    validate_pagination_params, validate_date_range, CSRFProtection
)


class MockForm:
    """Mock form for testing validators."""
    pass


class MockField:
    """Mock field for testing validators."""
    def __init__(self, data):
        self.data = data


def test_validate_email():
    """Test email validation."""
    form = MockForm()
    
    # Valid emails
    valid_emails = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
    ]
    
    for email in valid_emails:
        field = MockField(email)
        validate_email(form, field)  # Should not raise
    
    # Invalid emails
    invalid_emails = [
        'invalid-email',
        '@domain.com',
        'test@',
        '<EMAIL>',
        'a' * 250 + '@domain.com'  # Too long
    ]
    
    for email in invalid_emails:
        field = MockField(email)
        with pytest.raises(ValidationError):
            validate_email(form, field)


def test_validate_password_strength():
    """Test password strength validation."""
    form = MockForm()
    
    # Valid passwords
    valid_passwords = [
        'StrongPass123',
        'MySecure1Password',
        'Complex@Pass1'
    ]
    
    for password in valid_passwords:
        field = MockField(password)
        validate_password_strength(form, field)  # Should not raise
    
    # Invalid passwords
    invalid_passwords = [
        'short',  # Too short
        'nouppercase123',  # No uppercase
        'NOLOWERCASE123',  # No lowercase
        'NoNumbers',  # No digits
        'password',  # Common weak password
        'a' * 130  # Too long
    ]
    
    for password in invalid_passwords:
        field = MockField(password)
        with pytest.raises(ValidationError):
            validate_password_strength(form, field)


def test_validate_phone_number():
    """Test phone number validation."""
    form = MockForm()
    
    # Valid phone numbers
    valid_phones = [
        '1234567890',
        '11234567890',
        '(*************',
        '************'
    ]
    
    for phone in valid_phones:
        field = MockField(phone)
        validate_phone_number(form, field)  # Should not raise
    
    # Invalid phone numbers
    invalid_phones = [
        '123456789',  # Too short
        '123456789012',  # Too long
        '2234567890',  # 11 digits but doesn't start with 1
        'abc-def-ghij'  # Non-numeric
    ]
    
    for phone in invalid_phones:
        field = MockField(phone)
        with pytest.raises(ValidationError):
            validate_phone_number(form, field)


def test_validate_positive_number():
    """Test positive number validation."""
    form = MockForm()
    
    # Valid positive numbers
    field = MockField(5.5)
    validate_positive_number(form, field)  # Should not raise
    
    field = MockField(1)
    validate_positive_number(form, field)  # Should not raise
    
    # Invalid numbers
    invalid_numbers = [0, -1, -5.5]
    
    for number in invalid_numbers:
        field = MockField(number)
        with pytest.raises(ValidationError):
            validate_positive_number(form, field)


def test_validate_business_name():
    """Test business name validation."""
    form = MockForm()
    
    # Valid business names
    valid_names = [
        'ABC Corp',
        'Smith & Associates',
        'Tech Solutions Inc.',
        'Joe\'s Pizza'
    ]
    
    for name in valid_names:
        field = MockField(name)
        validate_business_name(form, field)  # Should not raise
    
    # Invalid business names
    invalid_names = [
        '',  # Empty
        'A',  # Too short
        'a' * 101,  # Too long
        'Invalid<Name>',  # Invalid characters
    ]
    
    for name in invalid_names:
        field = MockField(name)
        with pytest.raises(ValidationError):
            validate_business_name(form, field)


def test_validate_sku():
    """Test SKU validation."""
    form = MockForm()
    
    # Valid SKUs
    valid_skus = [
        'ABC123',
        'PROD-001',
        'SKU_123'
    ]
    
    for sku in valid_skus:
        field = MockField(sku)
        validate_sku(form, field)  # Should not raise
    
    # Invalid SKUs
    invalid_skus = [
        'a' * 51,  # Too long
        'SKU 123',  # Contains space
        'SKU@123'  # Invalid character
    ]
    
    for sku in invalid_skus:
        field = MockField(sku)
        with pytest.raises(ValidationError):
            validate_sku(form, field)


def test_validate_price():
    """Test price validation."""
    form = MockForm()
    
    # Valid prices
    valid_prices = [0, 10.50, 999999.99]
    
    for price in valid_prices:
        field = MockField(price)
        validate_price(form, field)  # Should not raise
    
    # Invalid prices
    invalid_prices = [-1, 1000000.00, 'invalid']
    
    for price in invalid_prices:
        field = MockField(price)
        with pytest.raises(ValidationError):
            validate_price(form, field)


def test_sanitize_input():
    """Test input sanitization."""
    # Test XSS prevention
    assert sanitize_input('<script>alert("xss")</script>') == '&lt;script&gt;alert(&quot;xss&quot;)&lt;/script&gt;'
    
    # Test control character removal
    assert sanitize_input('test\x00\x01string') == 'teststring'
    
    # Test whitespace stripping
    assert sanitize_input('  test  ') == 'test'
    
    # Test None handling
    assert sanitize_input(None) is None
    
    # Test empty string
    assert sanitize_input('') == ''


def test_validate_json_input():
    """Test JSON input validation."""
    # Valid JSON
    data = {'name': 'test', 'value': 123}
    result = validate_json_input(data)
    assert result['name'] == 'test'
    assert result['value'] == 123
    
    # Test required fields
    with pytest.raises(ValueError, match='Missing required fields'):
        validate_json_input({}, required_fields=['name'])
    
    # Test max length
    with pytest.raises(ValueError, match='exceeds maximum length'):
        validate_json_input({'name': 'a' * 101}, max_length=100)
    
    # Test invalid data type
    with pytest.raises(ValueError, match='Invalid JSON data format'):
        validate_json_input("not a dict")


def test_validate_search_query():
    """Test search query validation."""
    # Valid search
    assert validate_search_query('test query') == 'test query'
    
    # Test length limiting
    long_query = 'a' * 150
    result = validate_search_query(long_query)
    assert len(result) == 100
    
    # Test SQL injection prevention
    assert validate_search_query('SELECT * FROM users') == ' * FROM users'
    assert validate_search_query("'; DROP TABLE users; --") == " TABLE users "
    
    # Test empty query
    assert validate_search_query('') == ''
    assert validate_search_query(None) == ''


def test_validate_pagination_params():
    """Test pagination parameter validation."""
    # Valid parameters
    page, per_page = validate_pagination_params(2, 25)
    assert page == 2
    assert per_page == 25
    
    # Test defaults
    page, per_page = validate_pagination_params(None, None)
    assert page == 1
    assert per_page == 20
    
    # Test limits
    page, per_page = validate_pagination_params(-1, 200)
    assert page == 1  # Minimum 1
    assert per_page == 100  # Maximum 100
    
    # Test invalid types
    page, per_page = validate_pagination_params('invalid', 'invalid')
    assert page == 1
    assert per_page == 20


def test_validate_date_range():
    """Test date range validation."""
    # Valid date range
    start, end, errors = validate_date_range('2023-01-01', '2023-12-31')
    assert len(errors) == 0
    assert start is not None
    assert end is not None
    
    # Invalid date format
    start, end, errors = validate_date_range('invalid', '2023-12-31')
    assert len(errors) == 1
    assert 'Invalid start date format' in errors[0]
    
    # Start date after end date
    start, end, errors = validate_date_range('2023-12-31', '2023-01-01')
    assert len(errors) == 1
    assert 'Start date cannot be after end date' in errors[0]
    
    # Date range too large
    start, end, errors = validate_date_range('2020-01-01', '2023-01-01')
    assert len(errors) == 1
    assert 'Date range cannot exceed 2 years' in errors[0]


def test_validate_color_hex():
    """Test hex color validation."""
    form = MockForm()
    
    # Valid colors
    valid_colors = ['#FF0000', '#00ff00', '#123ABC', '#abc']
    
    for color in valid_colors:
        field = MockField(color)
        validate_color_hex(form, field)  # Should not raise
    
    # Invalid colors
    invalid_colors = ['FF0000', '#GG0000', '#12345', 'red']
    
    for color in invalid_colors:
        field = MockField(color)
        with pytest.raises(ValidationError):
            validate_color_hex(form, field)


def test_validate_url():
    """Test URL validation."""
    form = MockForm()
    
    # Valid URLs
    valid_urls = [
        'https://example.com',
        'http://www.example.com/path',
        'https://subdomain.example.org/path?query=1'
    ]
    
    for url in valid_urls:
        field = MockField(url)
        validate_url(form, field)  # Should not raise
    
    # Invalid URLs
    invalid_urls = [
        'not-a-url',
        'ftp://example.com',
        'https://',
        'example.com'
    ]
    
    for url in invalid_urls:
        field = MockField(url)
        with pytest.raises(ValidationError):
            validate_url(form, field)