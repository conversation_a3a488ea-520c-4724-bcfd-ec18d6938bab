"""CLI commands for backup and audit operations."""

import click
import os
from datetime import datetime, timedelta
from flask import current_app
from flask.cli import with_appcontext
from app import db
from app.services.backup_service import BackupService
from app.services.audit_service import AuditService
from app.utils.data_retention import DataRetentionManager


@click.group()
def backup():
    """Backup and audit management commands."""
    pass


@backup.command()
@click.option('--tenant-id', type=int, help='Backup specific tenant (optional)')
@click.option('--type', 'backup_type', default='full', help='Backup type (full, incremental)')
@with_appcontext
def create(tenant_id, backup_type):
    """Create a database backup."""
    
    try:
        backup_service = BackupService()
        
        click.echo(f"Creating {backup_type} backup...")
        if tenant_id:
            click.echo(f"Tenant ID: {tenant_id}")
        
        backup_log = backup_service.create_full_backup(tenant_id)
        
        click.echo(f"✓ Backup created successfully!")
        click.echo(f"  Backup ID: {backup_log.id}")
        click.echo(f"  Filename: {backup_log.backup_filename}")
        click.echo(f"  Size: {backup_log.backup_size / (1024*1024):.2f} MB" if backup_log.backup_size else "  Size: Unknown")
        click.echo(f"  Records: {backup_log.records_count}" if backup_log.records_count else "  Records: Unknown")
        
    except Exception as e:
        click.echo(f"✗ Backup failed: {str(e)}", err=True)
        raise click.ClickException(str(e))


@backup.command()
@click.option('--backup-id', type=int, required=True, help='Backup ID to restore')
@click.option('--target-tenant-id', type=int, help='Target tenant ID for restore (optional)')
@click.confirmation_option(prompt='Are you sure you want to restore this backup?')
@with_appcontext
def restore(backup_id, target_tenant_id):
    """Restore from a backup."""
    
    try:
        backup_service = BackupService()
        
        click.echo(f"Restoring backup {backup_id}...")
        if target_tenant_id:
            click.echo(f"Target tenant ID: {target_tenant_id}")
        
        success = backup_service.restore_backup(backup_id, target_tenant_id)
        
        if success:
            click.echo("✓ Backup restored successfully!")
        else:
            click.echo("✗ Backup restore failed!", err=True)
            
    except Exception as e:
        click.echo(f"✗ Restore failed: {str(e)}", err=True)
        raise click.ClickException(str(e))


@backup.command()
@click.option('--backup-id', type=int, required=True, help='Backup ID to verify')
@with_appcontext
def verify(backup_id):
    """Verify backup integrity."""
    
    try:
        backup_service = BackupService()
        
        click.echo(f"Verifying backup {backup_id}...")
        
        is_valid = backup_service.verify_backup(backup_id)
        
        if is_valid:
            click.echo("✓ Backup verification successful!")
        else:
            click.echo("✗ Backup verification failed!", err=True)
            
    except Exception as e:
        click.echo(f"✗ Verification failed: {str(e)}", err=True)
        raise click.ClickException(str(e))


@backup.command()
@click.option('--limit', default=10, help='Number of recent backups to show')
@with_appcontext
def list(limit):
    """List recent backups."""
    
    try:
        from app.models.audit import BackupLog
        
        backups = BackupLog.get_recent_backups(limit=limit)
        
        if not backups:
            click.echo("No backups found.")
            return
        
        click.echo(f"\nRecent backups (showing {len(backups)} of {limit}):")
        click.echo("-" * 80)
        
        for backup in backups:
            status_icon = "✓" if backup.backup_status == 'completed' else "✗" if backup.backup_status == 'failed' else "⏳"
            size_mb = f"{backup.backup_size / (1024*1024):.2f} MB" if backup.backup_size else "Unknown"
            tenant_info = f"Tenant {backup.tenant_id}" if backup.tenant_id else "Full system"
            
            click.echo(f"{status_icon} ID: {backup.id:3d} | {backup.backup_type:12s} | {size_mb:10s} | {tenant_info:15s} | {backup.created_at}")
            
            if backup.error_message:
                click.echo(f"    Error: {backup.error_message}")
        
        click.echo("-" * 80)
        
    except Exception as e:
        click.echo(f"✗ Failed to list backups: {str(e)}", err=True)
        raise click.ClickException(str(e))


@backup.command()
@with_appcontext
def status():
    """Show backup system status."""
    
    try:
        backup_service = BackupService()
        status_info = backup_service.get_backup_status()
        
        click.echo("\n📊 Backup System Status")
        click.echo("=" * 50)
        click.echo(f"Total backups: {status_info['total_backups']}")
        click.echo(f"Successful backups: {status_info['successful_backups']}")
        click.echo(f"Success rate: {status_info['success_rate']}%")
        click.echo(f"Last successful backup: {status_info['last_successful_backup'] or 'Never'}")
        click.echo(f"Total backup size: {status_info['total_backup_size_mb']} MB")
        
        if status_info['recent_backups']:
            click.echo(f"\nRecent backups:")
            for backup in status_info['recent_backups'][:3]:
                click.echo(f"  • {backup['type']} backup ({backup['status']}) - {backup['size_mb']} MB")
        
    except Exception as e:
        click.echo(f"✗ Failed to get status: {str(e)}", err=True)
        raise click.ClickException(str(e))


@backup.command()
@click.option('--retention-days', default=30, help='Retention period in days')
@click.confirmation_option(prompt='Are you sure you want to clean up old backups?')
@with_appcontext
def cleanup(retention_days):
    """Clean up old backup files and logs."""
    
    try:
        backup_service = BackupService()
        
        click.echo(f"Cleaning up backups older than {retention_days} days...")
        
        deleted_count = backup_service.cleanup_old_backups(retention_days)
        
        click.echo(f"✓ Cleaned up {deleted_count} old backup records and files.")
        
    except Exception as e:
        click.echo(f"✗ Cleanup failed: {str(e)}", err=True)
        raise click.ClickException(str(e))


@click.group()
def audit():
    """Audit log management commands."""
    pass


@audit.command()
@click.option('--tenant-id', type=int, required=True, help='Tenant ID')
@click.option('--limit', default=50, help='Number of logs to show')
@click.option('--action', help='Filter by action type')
@click.option('--severity', help='Filter by severity (low, medium, high, critical)')
@click.option('--user-id', type=int, help='Filter by user ID')
@click.option('--days', default=7, help='Number of days to look back')
@with_appcontext
def logs(tenant_id, limit, action, severity, user_id, days):
    """Show audit logs for a tenant."""
    
    try:
        from app.models.audit import AuditAction, AuditSeverity
        
        # Convert string parameters to enums
        action_enum = None
        if action:
            try:
                action_enum = AuditAction(action.lower())
            except ValueError:
                click.echo(f"Invalid action: {action}")
                return
        
        severity_enum = None
        if severity:
            try:
                severity_enum = AuditSeverity(severity.lower())
            except ValueError:
                click.echo(f"Invalid severity: {severity}")
                return
        
        start_date = datetime.utcnow() - timedelta(days=days)
        
        logs = AuditService.get_audit_logs(
            tenant_id=tenant_id,
            limit=limit,
            action=action_enum,
            severity=severity_enum,
            user_id=user_id,
            start_date=start_date
        )
        
        if not logs:
            click.echo("No audit logs found.")
            return
        
        click.echo(f"\n📋 Audit Logs for Tenant {tenant_id} (last {days} days)")
        click.echo("=" * 80)
        
        for log in logs:
            severity_icon = {"low": "ℹ️", "medium": "⚠️", "high": "🚨", "critical": "🔥"}.get(log.severity.value, "•")
            success_icon = "✓" if log.success else "✗"
            user_info = f"User {log.user_id}" if log.user_id else "System"
            
            click.echo(f"{severity_icon} {success_icon} [{log.created_at}] {log.action.value}")
            click.echo(f"    {log.description}")
            click.echo(f"    {user_info} | IP: {log.ip_address or 'Unknown'}")
            
            if log.error_message:
                click.echo(f"    Error: {log.error_message}")
            
            click.echo()
        
    except Exception as e:
        click.echo(f"✗ Failed to get audit logs: {str(e)}", err=True)
        raise click.ClickException(str(e))


@audit.command()
@click.option('--tenant-id', type=int, help='Tenant ID (optional, shows all if not specified)')
@click.option('--hours', default=24, help='Hours to look back')
@with_appcontext
def security(tenant_id, hours):
    """Show security events."""
    
    try:
        events = AuditService.get_security_events(tenant_id=tenant_id, hours=hours)
        
        if not events:
            click.echo("No security events found.")
            return
        
        click.echo(f"\n🔒 Security Events (last {hours} hours)")
        click.echo("=" * 60)
        
        for event in events:
            severity_icon = {"low": "ℹ️", "medium": "⚠️", "high": "🚨", "critical": "🔥"}.get(event.severity.value, "•")
            
            click.echo(f"{severity_icon} [{event.created_at}] {event.action.value}")
            click.echo(f"    {event.description}")
            click.echo(f"    IP: {event.ip_address or 'Unknown'} | Tenant: {event.tenant_id}")
            click.echo()
        
    except Exception as e:
        click.echo(f"✗ Failed to get security events: {str(e)}", err=True)
        raise click.ClickException(str(e))


@audit.command()
@click.option('--tenant-id', type=int, required=True, help='Tenant ID')
@click.option('--days', default=30, help='Number of days for summary')
@with_appcontext
def summary(tenant_id, days):
    """Show audit summary for a tenant."""
    
    try:
        summary_data = AuditService.get_audit_summary(tenant_id=tenant_id, days=days)
        
        click.echo(f"\n📊 Audit Summary for Tenant {tenant_id} (last {days} days)")
        click.echo("=" * 60)
        click.echo(f"Total events: {summary_data['total_events']}")
        click.echo(f"Failed events: {summary_data['failed_events']}")
        click.echo(f"Success rate: {summary_data['success_rate']}%")
        click.echo(f"Unique users: {summary_data['unique_users']}")
        click.echo(f"Unique IP addresses: {summary_data['unique_ip_addresses']}")
        
        click.echo(f"\nEvents by severity:")
        for severity, count in summary_data['events_by_severity'].items():
            if count > 0:
                click.echo(f"  {severity}: {count}")
        
        click.echo(f"\nTop actions:")
        sorted_actions = sorted(summary_data['events_by_action'].items(), key=lambda x: x[1], reverse=True)
        for action, count in sorted_actions[:10]:
            click.echo(f"  {action}: {count}")
        
    except Exception as e:
        click.echo(f"✗ Failed to get audit summary: {str(e)}", err=True)
        raise click.ClickException(str(e))


@audit.command()
@click.option('--tenant-id', type=int, required=True, help='Tenant ID')
@click.option('--start-date', required=True, help='Start date (YYYY-MM-DD)')
@click.option('--end-date', required=True, help='End date (YYYY-MM-DD)')
@click.option('--output', default='audit_export.json', help='Output filename')
@with_appcontext
def export(tenant_id, start_date, end_date, output):
    """Export audit logs to JSON file."""
    
    try:
        start_dt = datetime.strptime(start_date, '%Y-%m-%d')
        end_dt = datetime.strptime(end_date, '%Y-%m-%d')
        
        click.echo(f"Exporting audit logs from {start_date} to {end_date}...")
        
        json_data = AuditService.export_audit_logs(
            tenant_id=tenant_id,
            start_date=start_dt,
            end_date=end_dt,
            format='json'
        )
        
        with open(output, 'w') as f:
            f.write(json_data)
        
        click.echo(f"✓ Audit logs exported to {output}")
        
    except ValueError as e:
        click.echo(f"✗ Invalid date format: {str(e)}", err=True)
        raise click.ClickException(str(e))
    except Exception as e:
        click.echo(f"✗ Export failed: {str(e)}", err=True)
        raise click.ClickException(str(e))


@audit.command()
@click.option('--retention-days', default=90, help='Retention period in days')
@click.confirmation_option(prompt='Are you sure you want to clean up old audit logs?')
@with_appcontext
def cleanup(retention_days):
    """Clean up old audit logs."""
    
    try:
        click.echo(f"Cleaning up audit logs older than {retention_days} days...")
        
        deleted_count = AuditService.cleanup_old_audit_logs(retention_days)
        
        click.echo(f"✓ Cleaned up {deleted_count} old audit log records.")
        
    except Exception as e:
        click.echo(f"✗ Cleanup failed: {str(e)}", err=True)
        raise click.ClickException(str(e))


@click.group()
def retention():
    """Data retention management commands."""
    pass


@retention.command()
@click.option('--dry-run', is_flag=True, help='Show what would be deleted without actually deleting')
@with_appcontext
def cleanup(dry_run):
    """Run data retention cleanup."""
    
    try:
        manager = DataRetentionManager()
        
        if dry_run:
            click.echo("🔍 Running cleanup in dry-run mode...")
        else:
            click.echo("🧹 Running data retention cleanup...")
        
        result = manager.run_cleanup(dry_run=dry_run)
        
        click.echo(f"\n📊 Cleanup Results")
        click.echo("=" * 40)
        click.echo(f"Total items deleted: {result['total_deleted']}")
        click.echo(f"Success: {result['success']}")
        
        for operation, details in result['operations'].items():
            click.echo(f"\n{operation}:")
            if isinstance(details, dict):
                for key, value in details.items():
                    if key not in ['cutoff_date']:  # Skip verbose fields
                        click.echo(f"  {key}: {value}")
        
        if result.get('errors'):
            click.echo(f"\nErrors:")
            for error in result['errors']:
                click.echo(f"  • {error}")
        
    except Exception as e:
        click.echo(f"✗ Cleanup failed: {str(e)}", err=True)
        raise click.ClickException(str(e))


@retention.command()
@with_appcontext
def status():
    """Show data retention status."""
    
    try:
        manager = DataRetentionManager()
        status_info = manager.get_retention_status()
        
        click.echo("\n📊 Data Retention Status")
        click.echo("=" * 50)
        
        click.echo("Retention Policies:")
        for policy, days in status_info['retention_policies'].items():
            click.echo(f"  {policy}: {days} days")
        
        click.echo("\nCurrent Data Sizes:")
        for data_type, info in status_info['current_data_sizes'].items():
            cleanup_needed = "⚠️" if info.get('cleanup_needed') else "✓"
            click.echo(f"  {cleanup_needed} {data_type}: {info['total_records']} total, {info['old_records']} old")
        
        if status_info.get('cleanup_recommendations'):
            click.echo("\nRecommendations:")
            for rec in status_info['cleanup_recommendations']:
                priority_icon = {"high": "🔥", "medium": "⚠️", "low": "ℹ️"}.get(rec['priority'], "•")
                click.echo(f"  {priority_icon} {rec['message']}")
        
    except Exception as e:
        click.echo(f"✗ Failed to get retention status: {str(e)}", err=True)
        raise click.ClickException(str(e))


# Register command groups
def register_commands(app):
    """Register CLI commands with the Flask app."""
    app.cli.add_command(backup)
    app.cli.add_command(audit)
    app.cli.add_command(retention)