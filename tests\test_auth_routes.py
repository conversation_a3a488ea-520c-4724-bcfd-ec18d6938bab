"""Integration tests for authentication routes."""

import pytest
from flask import url_for, session
from app import create_app, db
from app.models.user import User, Tenant
from config import TestingConfig


@pytest.fixture
def app():
    """Create test application."""
    app = create_app(TestingConfig)
    return app


@pytest.fixture
def client(app):
    """Create test client."""
    return app.test_client()


@pytest.fixture
def app_context(app):
    """Create application context."""
    with app.app_context():
        db.create_all()
        yield app
        db.session.remove()
        db.drop_all()


@pytest.fixture
def sample_tenant(app_context):
    """Create a sample tenant for testing."""
    tenant = Tenant.create(
        name='Test Business',
        business_type='restaurant',
        email='<EMAIL>',
        subscription_status='active'
    )
    return tenant


@pytest.fixture
def sample_user(app_context, sample_tenant):
    """Create a sample user for testing."""
    user = User.create_user(
        email='<EMAIL>',
        password='testpassword123',
        first_name='<PERSON>',
        last_name='Doe',
        tenant_id=sample_tenant.id,
        role='manager'
    )
    return user


class TestLoginRoute:
    """Test cases for login route."""
    
    def test_login_page_get(self, client, app_context):
        """Test GET request to login page."""
        response = client.get('/auth/login')
        assert response.status_code == 200
        assert b'Sign in to your account' in response.data
        assert b'Email address' in response.data
        assert b'Password' in response.data
        assert b'Tenant ID' in response.data
    
    def test_successful_login_post(self, client, app_context, sample_user, sample_tenant):
        """Test successful login POST request."""
        response = client.post('/auth/login', data={
            'email': '<EMAIL>',
            'password': 'testpassword123',
            'tenant_id': sample_tenant.id
        }, follow_redirects=True)
        
        assert response.status_code == 200
        assert b'Welcome back, John Doe!' in response.data
    
    def test_login_with_invalid_credentials(self, client, app_context, sample_user, sample_tenant):
        """Test login with invalid credentials."""
        response = client.post('/auth/login', data={
            'email': '<EMAIL>',
            'password': 'wrongpassword',
            'tenant_id': sample_tenant.id
        })
        
        assert response.status_code == 200
        assert b'Invalid email or password' in response.data
    
    def test_login_with_missing_fields(self, client, app_context):
        """Test login with missing required fields."""
        response = client.post('/auth/login', data={
            'email': '',
            'password': '',
            'tenant_id': ''
        })
        
        assert response.status_code == 200
        assert b'Email is required' in response.data
        assert b'Password is required' in response.data
        assert b'Tenant ID is required' in response.data
    
    def test_login_redirect_when_authenticated(self, client, app_context, sample_user, sample_tenant):
        """Test login redirect when user is already authenticated."""
        # First login
        client.post('/auth/login', data={
            'email': '<EMAIL>',
            'password': 'testpassword123',
            'tenant_id': sample_tenant.id
        })
        
        # Try to access login page again
        response = client.get('/auth/login')
        assert response.status_code == 302  # Redirect to dashboard


class TestLogoutRoute:
    """Test cases for logout route."""
    
    def test_logout_when_authenticated(self, client, app_context, sample_user, sample_tenant):
        """Test logout when user is authenticated."""
        # First login
        client.post('/auth/login', data={
            'email': '<EMAIL>',
            'password': 'testpassword123',
            'tenant_id': sample_tenant.id
        })
        
        # Then logout
        response = client.get('/auth/logout', follow_redirects=True)
        assert response.status_code == 200
        assert b'You have been logged out successfully.' in response.data
    
    def test_logout_when_not_authenticated(self, client, app_context):
        """Test logout when user is not authenticated."""
        response = client.get('/auth/logout')
        assert response.status_code == 302  # Redirect to login


class TestRegisterRoute:
    """Test cases for register route."""
    
    def test_register_page_get(self, client, app_context):
        """Test GET request to register page."""
        response = client.get('/auth/register')
        assert response.status_code == 200
        assert b'Create your business account' in response.data
        assert b'Business Name' in response.data
        assert b'Business Type' in response.data
        assert b'First Name' in response.data
        assert b'Last Name' in response.data
        assert b'Email Address' in response.data
    
    def test_successful_registration(self, client, app_context):
        """Test successful user registration."""
        response = client.post('/auth/register', data={
            'email': '<EMAIL>',
            'password': 'newpassword123',
            'confirm_password': 'newpassword123',
            'first_name': 'Jane',
            'last_name': 'Smith',
            'tenant_name': 'New Business',
            'business_type': 'retail'
        }, follow_redirects=True)
        
        assert response.status_code == 200
        assert b'Registration successful!' in response.data
        
        # Verify user was created
        user = User.query.filter_by(email='<EMAIL>').first()
        assert user is not None
        assert user.first_name == 'Jane'
        assert user.last_name == 'Smith'
        assert user.role == 'admin'  # First user should be admin
        
        # Verify tenant was created
        tenant = Tenant.query.filter_by(name='New Business').first()
        assert tenant is not None
        assert tenant.business_type == 'retail'
    
    def test_registration_with_existing_email(self, client, app_context, sample_user):
        """Test registration with already existing email."""
        response = client.post('/auth/register', data={
            'email': '<EMAIL>',  # Already exists
            'password': 'password123',
            'confirm_password': 'password123',
            'first_name': 'Another',
            'last_name': 'User',
            'tenant_name': 'Another Business',
            'business_type': 'restaurant'
        })
        
        assert response.status_code == 200
        assert b'already registered' in response.data
    
    def test_registration_with_password_mismatch(self, client, app_context):
        """Test registration with password mismatch."""
        response = client.post('/auth/register', data={
            'email': '<EMAIL>',
            'password': 'password123',
            'confirm_password': 'differentpassword',
            'first_name': 'Jane',
            'last_name': 'Smith',
            'tenant_name': 'New Business',
            'business_type': 'retail'
        })
        
        assert response.status_code == 200
        assert b'Passwords do not match' in response.data
    
    def test_registration_with_missing_fields(self, client, app_context):
        """Test registration with missing required fields."""
        response = client.post('/auth/register', data={
            'email': '',
            'password': '',
            'confirm_password': '',
            'first_name': '',
            'last_name': '',
            'tenant_name': '',
            'business_type': 'retail'
        })
        
        assert response.status_code == 200
        assert b'Email is required' in response.data
        assert b'Password is required' in response.data
        assert b'First name is required' in response.data
        assert b'Last name is required' in response.data
        assert b'Business name is required' in response.data
    
    def test_registration_with_short_password(self, client, app_context):
        """Test registration with password too short."""
        response = client.post('/auth/register', data={
            'email': '<EMAIL>',
            'password': '123',  # Too short
            'confirm_password': '123',
            'first_name': 'Jane',
            'last_name': 'Smith',
            'tenant_name': 'New Business',
            'business_type': 'retail'
        })
        
        assert response.status_code == 200
        assert b'Password must be at least 8 characters long' in response.data
    
    def test_register_redirect_when_authenticated(self, client, app_context, sample_user, sample_tenant):
        """Test register redirect when user is already authenticated."""
        # First login
        client.post('/auth/login', data={
            'email': '<EMAIL>',
            'password': 'testpassword123',
            'tenant_id': sample_tenant.id
        })
        
        # Try to access register page
        response = client.get('/auth/register')
        assert response.status_code == 302  # Redirect to dashboard


class TestForgotPasswordRoute:
    """Test cases for forgot password route."""
    
    def test_forgot_password_page_get(self, client, app_context):
        """Test GET request to forgot password page."""
        response = client.get('/auth/forgot-password')
        assert response.status_code == 200
        assert b'Reset your password' in response.data
        assert b'Enter your email and tenant ID' in response.data
    
    def test_forgot_password_post(self, client, app_context, sample_user, sample_tenant):
        """Test forgot password POST request."""
        response = client.post('/auth/forgot-password', data={
            'email': '<EMAIL>',
            'tenant_id': sample_tenant.id
        }, follow_redirects=True)
        
        assert response.status_code == 200
        assert b'If your email is registered, you will receive password reset instructions.' in response.data
    
    def test_forgot_password_with_missing_fields(self, client, app_context):
        """Test forgot password with missing fields."""
        response = client.post('/auth/forgot-password', data={
            'email': '',
            'tenant_id': ''
        })
        
        assert response.status_code == 200
        assert b'Email is required' in response.data
        assert b'Tenant ID is required' in response.data


class TestResetPasswordRoute:
    """Test cases for reset password route."""
    
    def test_reset_password_page_get(self, client, app_context):
        """Test GET request to reset password page."""
        response = client.get('/auth/reset-password/test-token')
        assert response.status_code == 200
        assert b'Set new password' in response.data
        assert b'New Password' in response.data
        assert b'Confirm New Password' in response.data
    
    def test_reset_password_with_valid_token(self, client, app_context, sample_user):
        """Test password reset with valid token."""
        # Generate reset token
        token = sample_user.generate_password_reset_token()
        
        response = client.post(f'/auth/reset-password/{token}', data={
            'password': 'newpassword123',
            'confirm_password': 'newpassword123'
        }, follow_redirects=True)
        
        assert response.status_code == 200
        assert b'Password reset successfully!' in response.data
        
        # Verify password was changed
        user = User.query.get(sample_user.id)
        assert user.check_password('newpassword123') is True
    
    def test_reset_password_with_invalid_token(self, client, app_context):
        """Test password reset with invalid token."""
        response = client.post('/auth/reset-password/invalid-token', data={
            'password': 'newpassword123',
            'confirm_password': 'newpassword123'
        })
        
        assert response.status_code == 200
        assert b'Invalid or expired reset token' in response.data
    
    def test_reset_password_with_password_mismatch(self, client, app_context, sample_user):
        """Test password reset with password mismatch."""
        token = sample_user.generate_password_reset_token()
        
        response = client.post(f'/auth/reset-password/{token}', data={
            'password': 'newpassword123',
            'confirm_password': 'differentpassword'
        })
        
        assert response.status_code == 200
        assert b'Passwords do not match' in response.data


class TestChangePasswordRoute:
    """Test cases for change password route."""
    
    def test_change_password_page_requires_auth(self, client, app_context):
        """Test that change password page requires authentication."""
        response = client.get('/auth/change-password')
        assert response.status_code == 302  # Redirect to login
    
    def test_change_password_page_get_authenticated(self, client, app_context, sample_user, sample_tenant):
        """Test GET request to change password page when authenticated."""
        # Login first
        client.post('/auth/login', data={
            'email': '<EMAIL>',
            'password': 'testpassword123',
            'tenant_id': sample_tenant.id
        })
        
        response = client.get('/auth/change-password')
        assert response.status_code == 200
        assert b'Change Password' in response.data
        assert b'Current Password' in response.data
        assert b'New Password' in response.data
    
    def test_successful_password_change(self, client, app_context, sample_user, sample_tenant):
        """Test successful password change."""
        # Login first
        client.post('/auth/login', data={
            'email': '<EMAIL>',
            'password': 'testpassword123',
            'tenant_id': sample_tenant.id
        })
        
        response = client.post('/auth/change-password', data={
            'current_password': 'testpassword123',
            'new_password': 'newpassword123',
            'confirm_password': 'newpassword123'
        }, follow_redirects=True)
        
        assert response.status_code == 200
        assert b'Password changed successfully!' in response.data
    
    def test_change_password_with_wrong_current_password(self, client, app_context, sample_user, sample_tenant):
        """Test password change with wrong current password."""
        # Login first
        client.post('/auth/login', data={
            'email': '<EMAIL>',
            'password': 'testpassword123',
            'tenant_id': sample_tenant.id
        })
        
        response = client.post('/auth/change-password', data={
            'current_password': 'wrongpassword',
            'new_password': 'newpassword123',
            'confirm_password': 'newpassword123'
        })
        
        assert response.status_code == 200
        assert b'Current password is incorrect' in response.data


class TestProfileRoute:
    """Test cases for profile route."""
    
    def test_profile_page_requires_auth(self, client, app_context):
        """Test that profile page requires authentication."""
        response = client.get('/auth/profile')
        assert response.status_code == 302  # Redirect to login
    
    def test_profile_page_authenticated(self, client, app_context, sample_user, sample_tenant):
        """Test profile page when authenticated."""
        # Login first
        client.post('/auth/login', data={
            'email': '<EMAIL>',
            'password': 'testpassword123',
            'tenant_id': sample_tenant.id
        })
        
        response = client.get('/auth/profile')
        assert response.status_code == 200
        assert b'User Profile' in response.data
        assert b'John Doe' in response.data
        assert b'<EMAIL>' in response.data
        assert b'Manager' in response.data


class TestAPIEndpoints:
    """Test cases for API endpoints."""
    
    def test_check_email_api_available_email(self, client, app_context):
        """Test check email API with available email."""
        response = client.post('/auth/api/check-email', 
                             json={'email': '<EMAIL>'},
                             content_type='application/json')
        
        assert response.status_code == 200
        data = response.get_json()
        assert data['available'] is True
        assert 'available' in data['message']
    
    def test_check_email_api_existing_email(self, client, app_context, sample_user):
        """Test check email API with existing email."""
        response = client.post('/auth/api/check-email', 
                             json={'email': '<EMAIL>'},
                             content_type='application/json')
        
        assert response.status_code == 200
        data = response.get_json()
        assert data['available'] is False
        assert 'already registered' in data['message']
    
    def test_check_email_api_missing_email(self, client, app_context):
        """Test check email API with missing email."""
        response = client.post('/auth/api/check-email', 
                             json={},
                             content_type='application/json')
        
        assert response.status_code == 200
        data = response.get_json()
        assert data['available'] is False
        assert 'required' in data['message']
    
    def test_user_info_api_requires_auth(self, client, app_context):
        """Test that user info API requires authentication."""
        response = client.get('/auth/api/user-info')
        assert response.status_code == 302  # Redirect to login
    
    def test_user_info_api_authenticated(self, client, app_context, sample_user, sample_tenant):
        """Test user info API when authenticated."""
        # Login first
        client.post('/auth/login', data={
            'email': '<EMAIL>',
            'password': 'testpassword123',
            'tenant_id': sample_tenant.id
        })
        
        response = client.get('/auth/api/user-info')
        assert response.status_code == 200
        
        data = response.get_json()
        assert data['email'] == '<EMAIL>'
        assert data['full_name'] == 'John Doe'
        assert data['role'] == 'manager'
        assert data['tenant_id'] == sample_tenant.id
        assert 'permissions' in data