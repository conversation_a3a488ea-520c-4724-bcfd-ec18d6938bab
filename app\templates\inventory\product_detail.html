{% extends "base.html" %}

{% block title %}{{ product.name }} - Product Details - SaaS POS System{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <!-- Header -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-3xl font-bold text-gray-900">{{ product.name }}</h1>
            <p class="text-gray-600 mt-1">Product Details</p>
        </div>
        <div class="flex space-x-4">
            <a href="{{ url_for('inventory.adjust_stock', product_id=product.id) }}" 
               class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium">
                Adjust Stock
            </a>
            <a href="{{ url_for('inventory.edit_product', product_id=product.id) }}" 
               class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium">
                Edit Product
            </a>
            <a href="{{ url_for('inventory.products') }}" 
               class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg font-medium">
                Back to Products
            </a>
        </div>
    </div>

    <!-- Flash Messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="mb-4 p-4 rounded-lg {% if category == 'error' %}bg-red-100 text-red-700 border border-red-300{% else %}bg-green-100 text-green-700 border border-green-300{% endif %}">
                    {{ message }}
                </div>
            {% endfor %}
        {% endif %}
    {% endwith %}

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Product Information -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Basic Information -->
            <div class="bg-white shadow rounded-lg p-6">
                <div class="flex items-start space-x-6">
                    <!-- Product Image -->
                    <div class="flex-shrink-0">
                        {% if product.image_url %}
                        <img class="h-32 w-32 rounded-lg object-cover" src="{{ product.image_url }}" alt="{{ product.name }}">
                        {% else %}
                        <div class="h-32 w-32 rounded-lg bg-gray-200 flex items-center justify-center">
                            <svg class="w-16 h-16 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                            </svg>
                        </div>
                        {% endif %}
                    </div>

                    <!-- Product Details -->
                    <div class="flex-1">
                        <div class="flex items-center space-x-3 mb-3">
                            <h2 class="text-2xl font-bold text-gray-900">{{ product.name }}</h2>
                            {% if product.is_featured %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                Featured
                            </span>
                            {% endif %}
                            {% if product.is_active %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                Active
                            </span>
                            {% else %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                Inactive
                            </span>
                            {% endif %}
                        </div>

                        {% if product.description %}
                        <p class="text-gray-600 mb-4">{{ product.description }}</p>
                        {% endif %}

                        <div class="grid grid-cols-2 gap-4 text-sm">
                            {% if product.sku %}
                            <div>
                                <span class="font-medium text-gray-700">SKU:</span>
                                <span class="text-gray-900">{{ product.sku }}</span>
                            </div>
                            {% endif %}
                            {% if product.barcode %}
                            <div>
                                <span class="font-medium text-gray-700">Barcode:</span>
                                <span class="text-gray-900">{{ product.barcode }}</span>
                            </div>
                            {% endif %}
                            <div>
                                <span class="font-medium text-gray-700">Unit:</span>
                                <span class="text-gray-900">{{ product.unit_of_measure }}</span>
                            </div>
                            {% if product.weight %}
                            <div>
                                <span class="font-medium text-gray-700">Weight:</span>
                                <span class="text-gray-900">{{ product.weight }}</span>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Pricing Information -->
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Pricing</h3>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div class="bg-blue-50 p-4 rounded-lg">
                        <div class="text-2xl font-bold text-blue-900">${{ "%.2f"|format(product.selling_price) }}</div>
                        <div class="text-sm text-blue-600">Selling Price</div>
                    </div>
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <div class="text-2xl font-bold text-gray-900">${{ "%.2f"|format(product.cost_price) }}</div>
                        <div class="text-sm text-gray-600">Cost Price</div>
                    </div>
                    <div class="bg-green-50 p-4 rounded-lg">
                        <div class="text-2xl font-bold text-green-900">${{ "%.2f"|format(product.profit_amount) }}</div>
                        <div class="text-sm text-green-600">Profit</div>
                    </div>
                    <div class="bg-purple-50 p-4 rounded-lg">
                        <div class="text-2xl font-bold text-purple-900">{{ "%.1f"|format(product.profit_margin) }}%</div>
                        <div class="text-sm text-purple-600">Margin</div>
                    </div>
                </div>
                
                {% if product.tax_rate > 0 %}
                <div class="mt-4 p-4 bg-yellow-50 rounded-lg">
                    <div class="flex justify-between items-center">
                        <div>
                            <span class="font-medium text-gray-700">Tax Rate:</span>
                            <span class="text-gray-900">{{ "%.2f"|format(product.tax_rate) }}%</span>
                        </div>
                        <div>
                            <span class="font-medium text-gray-700">Tax Amount:</span>
                            <span class="text-gray-900">${{ "%.2f"|format(product.tax_amount) }}</span>
                        </div>
                        <div>
                            <span class="font-medium text-gray-700">Price with Tax:</span>
                            <span class="text-gray-900">${{ "%.2f"|format(product.price_with_tax) }}</span>
                        </div>
                    </div>
                    {% if product.tax_inclusive %}
                    <div class="text-sm text-yellow-600 mt-2">Price includes tax</div>
                    {% endif %}
                </div>
                {% endif %}
            </div>

            <!-- Inventory Movement History -->
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Recent Stock Movements</h3>
                {% if movements %}
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Stock Level</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Reason</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {% for movement in movements[:10] %}
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ movement.created_at.strftime('%Y-%m-%d %H:%M') }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                        {% if movement.movement_type == 'sale' %}bg-red-100 text-red-800
                                        {% elif movement.movement_type == 'adjustment' %}bg-blue-100 text-blue-800
                                        {% elif movement.movement_type == 'initial_stock' %}bg-green-100 text-green-800
                                        {% else %}bg-gray-100 text-gray-800{% endif %}">
                                        {{ movement.movement_type.replace('_', ' ').title() }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm">
                                    <span class="{% if movement.quantity > 0 %}text-green-600{% else %}text-red-600{% endif %} font-medium">
                                        {% if movement.quantity > 0 %}+{% endif %}{{ movement.quantity }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ movement.old_stock }} → {{ movement.new_stock }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {{ movement.reason or '-' }}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-8">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">No stock movements</h3>
                    <p class="mt-1 text-sm text-gray-500">Stock movements will appear here when inventory changes.</p>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Stock Information -->
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Stock Information</h3>
                
                <!-- Current Stock -->
                <div class="mb-4">
                    <div class="flex items-center justify-between mb-2">
                        <span class="text-sm font-medium text-gray-700">Current Stock</span>
                        {% if product.is_out_of_stock %}
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                            Out of Stock
                        </span>
                        {% elif product.is_low_stock %}
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                            Low Stock
                        </span>
                        {% else %}
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            In Stock
                        </span>
                        {% endif %}
                    </div>
                    <div class="text-3xl font-bold text-gray-900">{{ product.current_stock }}</div>
                    <div class="text-sm text-gray-500">{{ product.unit_of_measure }}</div>
                </div>

                <!-- Stock Levels -->
                <div class="space-y-3">
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600">Minimum Level:</span>
                        <span class="text-sm font-medium text-gray-900">{{ product.minimum_stock }}</span>
                    </div>
                    {% if product.maximum_stock %}
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600">Maximum Level:</span>
                        <span class="text-sm font-medium text-gray-900">{{ product.maximum_stock }}</span>
                    </div>
                    {% endif %}
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600">Track Inventory:</span>
                        <span class="text-sm font-medium {% if product.track_inventory %}text-green-600{% else %}text-red-600{% endif %}">
                            {% if product.track_inventory %}Yes{% else %}No{% endif %}
                        </span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600">Allow Negative:</span>
                        <span class="text-sm font-medium {% if product.allow_negative_stock %}text-green-600{% else %}text-red-600{% endif %}">
                            {% if product.allow_negative_stock %}Yes{% else %}No{% endif %}
                        </span>
                    </div>
                </div>

                <!-- Stock Progress Bar -->
                {% if product.minimum_stock > 0 %}
                <div class="mt-4">
                    <div class="flex justify-between text-sm text-gray-600 mb-1">
                        <span>Stock Level</span>
                        <span>{{ "%.0f"|format((product.current_stock / product.minimum_stock) * 100) }}%</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        {% set stock_percentage = (product.current_stock / product.minimum_stock) * 100 %}
                        <div class="h-2 rounded-full {% if stock_percentage <= 25 %}bg-red-500{% elif stock_percentage <= 50 %}bg-yellow-500{% else %}bg-green-500{% endif %}" 
                             style="width: {{ [stock_percentage, 100]|min }}%"></div>
                    </div>
                </div>
                {% endif %}
            </div>

            <!-- Quick Actions -->
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
                <div class="space-y-3">
                    <a href="{{ url_for('inventory.adjust_stock', product_id=product.id) }}" 
                       class="w-full bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium text-center block">
                        Adjust Stock
                    </a>
                    <a href="{{ url_for('inventory.edit_product', product_id=product.id) }}" 
                       class="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium text-center block">
                        Edit Product
                    </a>
                    {% if product.is_active %}
                    <form method="POST" action="{{ url_for('inventory.delete_product', product_id=product.id) }}" 
                          onsubmit="return confirm('Are you sure you want to deactivate this product?')">
                        <button type="submit" 
                                class="w-full bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium">
                            Deactivate Product
                        </button>
                    </form>
                    {% endif %}
                </div>
            </div>

            <!-- Product Metadata -->
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Details</h3>
                <div class="space-y-3 text-sm">
                    {% if product.color %}
                    <div class="flex justify-between">
                        <span class="text-gray-600">Color:</span>
                        <span class="text-gray-900">{{ product.color }}</span>
                    </div>
                    {% endif %}
                    <div class="flex justify-between">
                        <span class="text-gray-600">Sort Order:</span>
                        <span class="text-gray-900">{{ product.sort_order }}</span>
                    </div>
                    {% if product.created_at %}
                      <span class="text-gray-900">
                        {% if product.created_at.strftime is defined %}
                          {{ product.created_at.strftime('%Y-%m-%d') }}
                        {% else %}
                          {{ product.created_at[:10] }}
                        {% endif %}
                      </span>
                    {% endif %}
                    {% if product.updated_at %}
                      <span class="text-gray-900">
                        {% if product.updated_at.strftime is defined %}
                          {{ product.updated_at.strftime('%Y-%m-%d') }}
                        {% else %}
                          {{ product.updated_at[:10] }}
                        {% endif %}
                      </span>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}