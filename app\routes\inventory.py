"""Inventory routes for SaaS POS System."""

from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from app.services.inventory_service import InventoryService, ProductNotFoundError, DuplicateProductError
from app.models.base import get_current_tenant
from app.models.product import Category

bp = Blueprint('inventory', __name__)


@bp.route('/')
@login_required
def index():
    """Inventory dashboard with overview of products and categories."""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    category_id = request.args.get('category_id', None, type=int)
    search = request.args.get('search', None)
    low_stock_only = request.args.get('low_stock', False, type=bool)
    
    # Get products with pagination
    result = InventoryService.get_products(
        page=page, 
        per_page=per_page,
        category_id=category_id,
        search=search,
        low_stock_only=low_stock_only
    )
    
    # Get categories for filter dropdown
    tenant_id = get_current_tenant()
    categories = Category.query.filter_by(tenant_id=tenant_id, is_active=True).order_by(Category.name).all()
    
    # Get low stock alerts
    low_stock_alerts = InventoryService.get_low_stock_alerts()
    
    return render_template(
        'inventory/index.html',
        products=result.get('products', []),
        pagination=result.get('pagination', {}),
        categories=categories,
        low_stock_alerts=low_stock_alerts.get('alerts', {}) if low_stock_alerts.get('success') else {},
        alert_summary=low_stock_alerts.get('summary', {}) if low_stock_alerts.get('success') else {},
        category_id=category_id,
        search=search,
        low_stock_only=low_stock_only
    )


@bp.route('/products')
@login_required
def products():
    """Product listing page."""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    category_id = request.args.get('category_id', None, type=int)
    search = request.args.get('search', None)
    low_stock_only = request.args.get('low_stock', False, type=bool)
    
    # Get products with pagination
    result = InventoryService.get_products(
        page=page, 
        per_page=per_page,
        category_id=category_id,
        search=search,
        low_stock_only=low_stock_only
    )
    
    # Get categories for filter dropdown
    tenant_id = get_current_tenant()
    categories = Category.query.filter_by(tenant_id=tenant_id, is_active=True).order_by(Category.name).all()
    
    return render_template(
        'inventory/products.html',
        products=result.get('products', []),
        pagination=result.get('pagination', {}),
        categories=categories,
        category_id=category_id,
        search=search,
        low_stock_only=low_stock_only
    )


@bp.route('/products/new', methods=['GET', 'POST'])
@login_required
def new_product():
    """Create a new product."""
    if request.method == 'POST':
        product_data = {
            'name': request.form.get('name'),
            'description': request.form.get('description'),
            'sku': request.form.get('sku'),
            'barcode': request.form.get('barcode'),
            'category_id': request.form.get('category_id', type=int),
            'cost_price': request.form.get('cost_price', type=float, default=0),
            'selling_price': request.form.get('selling_price', type=float, default=0),
            'current_stock': request.form.get('current_stock', type=int, default=0),
            'minimum_stock': request.form.get('minimum_stock', type=int, default=0),
            'maximum_stock': request.form.get('maximum_stock', type=int),
            'track_inventory': request.form.get('track_inventory') == 'on',
            'allow_negative_stock': request.form.get('allow_negative_stock') == 'on',
            'unit_of_measure': request.form.get('unit_of_measure', 'each'),
            'weight': request.form.get('weight', type=float),
            'tax_rate': request.form.get('tax_rate', type=float, default=0),
            'tax_inclusive': request.form.get('tax_inclusive') == 'on',
            'is_active': request.form.get('is_active') == 'on',
            'is_featured': request.form.get('is_featured') == 'on',
            'image_url': request.form.get('image_url'),
            'color': request.form.get('color'),
            'sort_order': request.form.get('sort_order', type=int, default=0)
        }
        
        result = InventoryService.create_product(product_data)
        
        if result.get('success'):
            flash('Product created successfully', 'success')
            return redirect(url_for('inventory.products'))
        else:
            flash(result.get('error', 'An error occurred while creating the product'), 'error')
    
    # Get categories for dropdown
    tenant_id = get_current_tenant()
    categories = Category.query.filter_by(tenant_id=tenant_id, is_active=True).order_by(Category.name).all()
    
    return render_template('inventory/product_form.html', product=None, categories=categories)


@bp.route('/products/<int:product_id>')
@login_required
def view_product(product_id):
    """View product details."""
    product = InventoryService.get_product(product_id)
    
    if not product:
        flash('Product not found', 'error')
        return redirect(url_for('inventory.products'))
    
    # Get inventory movement history
    from app.models.product import InventoryMovement
    movements = InventoryMovement.get_product_history(product_id)
    
    return render_template('inventory/product_detail.html', product=product, movements=movements)


@bp.route('/products/<int:product_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_product(product_id):
    """Edit an existing product."""
    product = InventoryService.get_product(product_id)
    
    if not product:
        flash('Product not found', 'error')
        return redirect(url_for('inventory.products'))
    
    if request.method == 'POST':
        product_data = {
            'name': request.form.get('name'),
            'description': request.form.get('description'),
            'sku': request.form.get('sku'),
            'barcode': request.form.get('barcode'),
            'category_id': request.form.get('category_id', type=int),
            'cost_price': request.form.get('cost_price', type=float, default=0),
            'selling_price': request.form.get('selling_price', type=float, default=0),
            'minimum_stock': request.form.get('minimum_stock', type=int, default=0),
            'maximum_stock': request.form.get('maximum_stock', type=int),
            'track_inventory': request.form.get('track_inventory') == 'on',
            'allow_negative_stock': request.form.get('allow_negative_stock') == 'on',
            'unit_of_measure': request.form.get('unit_of_measure', 'each'),
            'weight': request.form.get('weight', type=float),
            'tax_rate': request.form.get('tax_rate', type=float, default=0),
            'tax_inclusive': request.form.get('tax_inclusive') == 'on',
            'is_active': request.form.get('is_active') == 'on',
            'is_featured': request.form.get('is_featured') == 'on',
            'image_url': request.form.get('image_url'),
            'color': request.form.get('color'),
            'sort_order': request.form.get('sort_order', type=int, default=0)
        }
        
        result = InventoryService.update_product(product_id, product_data)
        
        if result.get('success'):
            flash('Product updated successfully', 'success')
            return redirect(url_for('inventory.view_product', product_id=product_id))
        else:
            flash(result.get('error', 'An error occurred while updating the product'), 'error')
    
    # Get categories for dropdown
    tenant_id = get_current_tenant()
    categories = Category.query.filter_by(tenant_id=tenant_id, is_active=True).order_by(Category.name).all()
    
    return render_template('inventory/product_form.html', product=product, categories=categories)


@bp.route('/products/<int:product_id>/delete', methods=['POST'])
@login_required
def delete_product(product_id):
    """Delete a product (soft delete)."""
    result = InventoryService.delete_product(product_id)
    
    if result.get('success'):
        flash('Product deleted successfully', 'success')
    else:
        flash(result.get('error', 'An error occurred while deleting the product'), 'error')
    
    return redirect(url_for('inventory.products'))


@bp.route('/products/<int:product_id>/adjust-stock', methods=['GET', 'POST'])
@login_required
def adjust_stock(product_id):
    """Adjust product stock levels."""
    product = InventoryService.get_product(product_id)
    
    if not product:
        flash('Product not found', 'error')
        return redirect(url_for('inventory.products'))
    
    if request.method == 'POST':
        try:
            quantity = int(request.form.get('quantity', 0))
            reason = request.form.get('reason', 'manual_adjustment')
            notes = request.form.get('notes', '')
            
            result = InventoryService.adjust_stock(
                product_id=product_id,
                quantity=quantity,
                reason=reason,
                notes=notes
            )
            
            if result.get('success'):
                flash('Stock adjusted successfully', 'success')
                return redirect(url_for('inventory.view_product', product_id=product_id))
            else:
                flash(result.get('error', 'An error occurred while adjusting stock'), 'error')
        except ValueError:
            flash('Invalid quantity value', 'error')
    
    return render_template('inventory/adjust_stock.html', product=product)


@bp.route('/categories')
@login_required
def categories():
    """Category management page."""
    tenant_id = get_current_tenant()
    categories = Category.query.filter_by(tenant_id=tenant_id).order_by(Category.name).all()
    category_tree = Category.get_category_tree(tenant_id)
    
    return render_template('inventory/categories.html', categories=categories, category_tree=category_tree)


@bp.route('/categories/new', methods=['GET', 'POST'])
@login_required
def new_category():
    """Create a new category."""
    if request.method == 'POST':
        tenant_id = get_current_tenant()
        if tenant_id is None:
            flash("Impossible de déterminer le tenant courant. Veuillez vous reconnecter ou contacter l'administrateur.", 'error')
            return redirect(url_for('inventory.categories'))
        name = request.form.get('name')
        description = request.form.get('description')
        parent_id = request.form.get('parent_id', type=int)
        color = request.form.get('color')
        icon = request.form.get('icon')
        sort_order = request.form.get('sort_order', type=int, default=0)
        is_active = request.form.get('is_active') == 'on'
        
        # Validate parent category if provided
        if parent_id:
            parent = Category.query.filter_by(id=parent_id, tenant_id=tenant_id).first()
            if not parent:
                flash('Parent category not found', 'error')
                return redirect(url_for('inventory.new_category'))
        
        # Create category
        category = Category(
            tenant_id=tenant_id,
            name=name,
            description=description,
            parent_id=parent_id,
            color=color,
            icon=icon,
            sort_order=sort_order,
            is_active=is_active
        )
        
        try:
            category.save()
            flash('Category created successfully', 'success')
            return redirect(url_for('inventory.categories'))
        except Exception as e:
            flash(f'An error occurred: {str(e)}', 'error')
    
    # Get categories for parent dropdown
    tenant_id = get_current_tenant()
    categories = Category.query.filter_by(tenant_id=tenant_id, is_active=True).order_by(Category.name).all()
    
    return render_template('inventory/category_form.html', category=None, categories=categories)


@bp.route('/categories/<int:category_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_category(category_id):
    """Edit an existing category."""
    tenant_id = get_current_tenant()
    category = Category.query.filter_by(id=category_id, tenant_id=tenant_id).first()
    
    if not category:
        flash('Category not found', 'error')
        return redirect(url_for('inventory.categories'))
    
    if request.method == 'POST':
        name = request.form.get('name')
        description = request.form.get('description')
        parent_id = request.form.get('parent_id', type=int)
        color = request.form.get('color')
        icon = request.form.get('icon')
        sort_order = request.form.get('sort_order', type=int, default=0)
        is_active = request.form.get('is_active') == 'on'
        
        # Prevent circular reference
        if parent_id == category_id:
            flash('A category cannot be its own parent', 'error')
            return redirect(url_for('inventory.edit_category', category_id=category_id))
        
        # Validate parent category if provided
        if parent_id:
            parent = Category.query.filter_by(id=parent_id, tenant_id=tenant_id).first()
            if not parent:
                flash('Parent category not found', 'error')
                return redirect(url_for('inventory.edit_category', category_id=category_id))
        
        # Update category
        category.name = name
        category.description = description
        category.parent_id = parent_id
        category.color = color
        category.icon = icon
        category.sort_order = sort_order
        category.is_active = is_active
        
        try:
            category.save()
            flash('Category updated successfully', 'success')
            return redirect(url_for('inventory.categories'))
        except Exception as e:
            flash(f'An error occurred: {str(e)}', 'error')
    
    # Get categories for parent dropdown (excluding self and children)
    categories = Category.query.filter_by(tenant_id=tenant_id, is_active=True).order_by(Category.name).all()
    
    return render_template('inventory/category_form.html', category=category, categories=categories)


@bp.route('/categories/<int:category_id>/delete', methods=['POST'])
@login_required
def delete_category(category_id):
    """Delete a category."""
    tenant_id = get_current_tenant()
    category = Category.query.filter_by(id=category_id, tenant_id=tenant_id).first()
    
    if not category:
        flash('Category not found', 'error')
        return redirect(url_for('inventory.categories'))
    
    if not category.can_be_deleted():
        flash('Cannot delete category with products or subcategories', 'error')
        return redirect(url_for('inventory.categories'))
    
    try:
        category.delete()
        flash('Category deleted successfully', 'success')
    except Exception as e:
        flash(f'An error occurred: {str(e)}', 'error')
    
    return redirect(url_for('inventory.categories'))


@bp.route('/low-stock')
@login_required
def low_stock():
    """View low stock products."""
    result = InventoryService.get_low_stock_alerts()
    
    if not result.get('success'):
        flash(result.get('error', 'An error occurred while retrieving low stock alerts'), 'error')
        return redirect(url_for('inventory.index'))
    
    return render_template(
        'inventory/low_stock.html',
        alerts=result.get('alerts', {}),
        summary=result.get('summary', {})
    )


@bp.route('/analytics')
@login_required
def analytics():
    """Product performance analytics."""
    from datetime import datetime, timedelta
    
    # Get date range from query parameters or use default (last 30 days)
    end_date = datetime.utcnow()
    start_date = end_date - timedelta(days=30)
    
    if request.args.get('start_date'):
        try:
            start_date = datetime.strptime(request.args.get('start_date'), '%Y-%m-%d')
        except ValueError:
            flash('Invalid start date format', 'error')
    
    if request.args.get('end_date'):
        try:
            end_date = datetime.strptime(request.args.get('end_date'), '%Y-%m-%d')
            # Set end date to end of day
            end_date = end_date.replace(hour=23, minute=59, second=59)
        except ValueError:
            flash('Invalid end date format', 'error')
    
    # Get product performance data
    result = InventoryService.get_product_performance_analytics(
        start_date=start_date,
        end_date=end_date
    )
    
    if not result.get('success'):
        flash(result.get('error', 'An error occurred while retrieving analytics data'), 'error')
        return redirect(url_for('inventory.index'))
    
    return render_template(
        'inventory/analytics.html',
        performance_data=result.get('performance_data', []),
        no_sales_data=result.get('no_sales_data', []),
        summary=result.get('summary', {}),
        date_range=result.get('date_range', {}),
        start_date=start_date.strftime('%Y-%m-%d'),
        end_date=end_date.strftime('%Y-%m-%d')
    )