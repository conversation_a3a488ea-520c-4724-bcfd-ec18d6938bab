"""Simple test script to verify Flask app structure."""

from run import app

def test_basic_routes():
    """Test basic application routes."""
    with app.test_client() as client:
        # Test home page
        response = client.get('/')
        print(f"Home page status: {response.status_code}")
        
        # Test auth routes
        response = client.get('/auth/login')
        print(f"Login page status: {response.status_code}")
        
        # Test other routes
        response = client.get('/pos/dashboard')
        print(f"POS dashboard status: {response.status_code}")
        
        response = client.get('/inventory/')
        print(f"Inventory page status: {response.status_code}")
        
        response = client.get('/reports/')
        print(f"Reports page status: {response.status_code}")
        
        response = client.get('/admin/')
        print(f"Admin page status: {response.status_code}")

if __name__ == '__main__':
    print("Testing Flask application structure...")
    test_basic_routes()
    print("All basic routes are working!")
    print("Flask application structure is properly configured!")