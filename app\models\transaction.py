"""Transaction and TransactionItem models for SaaS POS System."""

from decimal import Decimal
from datetime import datetime
from enum import Enum
from sqlalchemy import event
from app import db
from app.models.base import TenantAwareModel


class TransactionStatus(Enum):
    """Transaction status enumeration."""
    PENDING = 'pending'
    COMPLETED = 'completed'
    CANCELLED = 'cancelled'
    REFUNDED = 'refunded'
    PARTIALLY_REFUNDED = 'partially_refunded'


class PaymentMethod(Enum):
    """Payment method enumeration."""
    CASH = 'cash'
    CARD = 'card'
    DIGITAL_WALLET = 'digital_wallet'
    BANK_TRANSFER = 'bank_transfer'
    STORE_CREDIT = 'store_credit'
    OTHER = 'other'


class Transaction(TenantAwareModel):
    """Transaction model for sales records."""
    
    __tablename__ = 'transaction'
    
    # Transaction identification
    transaction_number = db.Column(db.String(50), nullable=False, unique=True, index=True)
    
    # User and customer information
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    user = db.relationship('User', backref='transactions')
    customer_name = db.Column(db.String(100), nullable=True)
    customer_email = db.Column(db.String(120), nullable=True)
    customer_phone = db.Column(db.String(20), nullable=True)
    
    # Transaction status and timing
    status = db.Column(db.Enum(TransactionStatus), nullable=False, default=TransactionStatus.PENDING)
    transaction_date = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)
    completed_at = db.Column(db.DateTime, nullable=True)
    
    # Financial details
    subtotal = db.Column(db.Numeric(10, 2), nullable=False, default=0.00)
    tax_amount = db.Column(db.Numeric(10, 2), nullable=False, default=0.00)
    discount_amount = db.Column(db.Numeric(10, 2), nullable=False, default=0.00)
    total_amount = db.Column(db.Numeric(10, 2), nullable=False, default=0.00)
    
    # Payment information
    payment_method = db.Column(db.Enum(PaymentMethod), nullable=True)
    amount_paid = db.Column(db.Numeric(10, 2), nullable=False, default=0.00)
    change_given = db.Column(db.Numeric(10, 2), nullable=False, default=0.00)
    
    # Discount details
    discount_type = db.Column(db.String(20), nullable=True)  # percentage, fixed_amount, coupon
    discount_value = db.Column(db.Numeric(10, 2), nullable=True)
    discount_reason = db.Column(db.String(200), nullable=True)
    
    # Additional information
    notes = db.Column(db.Text, nullable=True)
    receipt_number = db.Column(db.String(50), nullable=True)
    
    # Restaurant-specific fields
    table_number = db.Column(db.String(20), nullable=True)
    order_type = db.Column(db.String(20), nullable=True)  # dine_in, takeout, delivery
    
    # Relationships
    items = db.relationship('TransactionItem', backref='transaction_ref', lazy=True, cascade='all, delete-orphan')
    
    # Comprehensive indexing for optimization
    __table_args__ = (
        db.Index('idx_transaction_tenant_status', 'tenant_id', 'status'),
        db.Index('idx_transaction_tenant_date', 'tenant_id', 'transaction_date'),
        db.Index('idx_transaction_tenant_completed', 'tenant_id', 'completed_at'),
        db.Index('idx_transaction_user_tenant', 'user_id', 'tenant_id'),
        db.Index('idx_transaction_number', 'transaction_number'),
        db.Index('idx_transaction_customer_email', 'customer_email'),
        db.Index('idx_transaction_payment_method', 'payment_method'),
        db.Index('idx_transaction_table_number', 'table_number'),
        db.Index('idx_transaction_order_type', 'order_type'),
        db.Index('idx_transaction_daily_sales', 'tenant_id', 'status', 'completed_at'),
    )
    
    def __repr__(self):
        return f'<Transaction {self.transaction_number}>'
    
    def calculate_totals(self):
        """Calculate and update transaction totals based on items."""
        if not self.items:
            self.subtotal = Decimal('0.00')
            self.tax_amount = Decimal('0.00')
            self.total_amount = Decimal('0.00')
            return
        
        # Calculate subtotal and tax from items
        subtotal = Decimal('0.00')
        tax_amount = Decimal('0.00')
        
        for item in self.items:
            item.calculate_totals()
            subtotal += item.line_total
            tax_amount += item.tax_amount
        
        self.subtotal = subtotal
        self.tax_amount = tax_amount
        
        # Apply discount
        discounted_subtotal = self.subtotal - self.discount_amount
        if discounted_subtotal < 0:
            discounted_subtotal = Decimal('0.00')
        
        # Total = discounted subtotal + tax
        self.total_amount = discounted_subtotal + self.tax_amount
    
    def apply_discount(self, discount_type, discount_value, reason=None):
        """Apply discount to the transaction."""
        self.discount_type = discount_type
        self.discount_value = discount_value
        self.discount_reason = reason
        
        if discount_type == 'percentage':
            # Percentage discount on subtotal
            self.discount_amount = (self.subtotal * discount_value / 100).quantize(Decimal('0.01'))
        elif discount_type == 'fixed_amount':
            # Fixed amount discount
            self.discount_amount = min(discount_value, self.subtotal)
        else:
            self.discount_amount = Decimal('0.00')
        
        self.calculate_totals()
    
    def add_item(self, product, quantity, unit_price=None, discount_amount=None):
        """Add an item to the transaction."""
        # Use product's selling price if unit_price not provided
        if unit_price is None:
            unit_price = product.selling_price
        
        # Ensure transaction is saved to database first
        if not self.id:
            self.save()
        
        # Check if item already exists in the current items list
        existing_item = None
        for item in self.items:
            if item.product_id == product.id:
                existing_item = item
                break
        
        if existing_item:
            # Update existing item
            existing_item.quantity += quantity
            existing_item.calculate_totals()
            existing_item.save()
        else:
            # Create new item
            item = TransactionItem(
                transaction_id=self.id,
                product_id=product.id,
                tenant_id=self.tenant_id,
                quantity=quantity,
                unit_price=unit_price,
                discount_amount=discount_amount or Decimal('0.00'),
                product_name=product.name,
                product_sku=product.sku,
                cost_price=product.cost_price,
                tax_rate=product.tax_rate
            )
            item.calculate_totals()
            item.save()
        
        # Refresh the transaction to get updated items
        db.session.refresh(self)
        self.calculate_totals()
        self.save()
    
    def remove_item(self, product_id):
        """Remove an item from the transaction."""
        item = TransactionItem.query.filter_by(
            transaction_id=self.id,
            product_id=product_id
        ).first()
        
        if item:
            db.session.delete(item)
            self.calculate_totals()
    
    def update_item_quantity(self, product_id, new_quantity):
        """Update the quantity of an item in the transaction."""
        item = TransactionItem.query.filter_by(
            transaction_id=self.id,
            product_id=product_id
        ).first()
        
        if item:
            if new_quantity <= 0:
                db.session.delete(item)
            else:
                item.quantity = new_quantity
                item.calculate_totals()
            self.calculate_totals()
    
    def process_payment(self, payment_method, amount_paid):
        """Process payment for the transaction."""
        self.payment_method = payment_method
        self.amount_paid = amount_paid
        
        if amount_paid >= self.total_amount:
            self.change_given = amount_paid - self.total_amount
            self.status = TransactionStatus.COMPLETED
            self.completed_at = datetime.utcnow()
        else:
            self.change_given = Decimal('0.00')
            # Could implement partial payment logic here
    
    def cancel_transaction(self, reason=None):
        """Cancel the transaction."""
        if self.status == TransactionStatus.COMPLETED:
            raise ValueError("Cannot cancel a completed transaction")
        
        self.status = TransactionStatus.CANCELLED
        if reason:
            self.notes = f"Cancelled: {reason}"
    
    def refund_transaction(self, refund_amount=None, reason=None):
        """Refund the transaction (full or partial)."""
        if self.status != TransactionStatus.COMPLETED:
            raise ValueError("Can only refund completed transactions")
        
        if refund_amount is None:
            refund_amount = self.total_amount
        
        if refund_amount >= self.total_amount:
            self.status = TransactionStatus.REFUNDED
        else:
            self.status = TransactionStatus.PARTIALLY_REFUNDED
        
        if reason:
            current_notes = self.notes or ""
            self.notes = f"{current_notes}\nRefund: {refund_amount} - {reason}".strip()
    
    def get_item_count(self):
        """Get total number of items in the transaction."""
        return sum(item.quantity for item in self.items)
    
    def get_profit_amount(self):
        """Calculate total profit for the transaction."""
        return sum(item.get_profit_amount() for item in self.items)
    
    def is_paid(self):
        """Check if transaction is fully paid."""
        return self.amount_paid >= self.total_amount
    
    def get_balance_due(self):
        """Get remaining balance due."""
        return max(Decimal('0.00'), self.total_amount - self.amount_paid)
    
    @classmethod
    def generate_transaction_number(cls, tenant_id):
        """Generate a unique transaction number."""
        from datetime import datetime
        import random
        
        # Format: TXN-YYYYMMDD-HHMMSS-XXXX
        now = datetime.now()
        date_part = now.strftime("%Y%m%d")
        time_part = now.strftime("%H%M%S")
        random_part = f"{random.randint(1000, 9999)}"
        
        transaction_number = f"TXN-{date_part}-{time_part}-{random_part}"
        
        # Ensure uniqueness
        while cls.query.filter_by(transaction_number=transaction_number).first():
            random_part = f"{random.randint(1000, 9999)}"
            transaction_number = f"TXN-{date_part}-{time_part}-{random_part}"
        
        return transaction_number
    
    @classmethod
    def get_daily_sales(cls, tenant_id, date=None):
        """Get daily sales summary."""
        if date is None:
            date = datetime.now().date()
        
        start_date = datetime.combine(date, datetime.min.time())
        end_date = datetime.combine(date, datetime.max.time())
        
        transactions = cls.query.filter(
            cls.tenant_id == tenant_id,
            cls.status == TransactionStatus.COMPLETED,
            cls.completed_at.between(start_date, end_date)
        ).all()
        
        return {
            'transaction_count': len(transactions),
            'total_sales': sum(t.total_amount for t in transactions),
            'total_tax': sum(t.tax_amount for t in transactions),
            'total_discount': sum(t.discount_amount for t in transactions),
            'total_profit': sum(t.get_profit_amount() for t in transactions)
        }


class TransactionItem(TenantAwareModel):
    """Individual items within a transaction."""
    
    __tablename__ = 'transaction_item'
    
    # References
    transaction_id = db.Column(db.Integer, db.ForeignKey('transaction.id'), nullable=False)
    product_id = db.Column(db.Integer, db.ForeignKey('product.id'), nullable=False)
    product = db.relationship('Product', backref='transaction_items')
    
    # Item details
    quantity = db.Column(db.Integer, nullable=False, default=1)
    unit_price = db.Column(db.Numeric(10, 2), nullable=False)
    
    # Calculated fields
    line_total = db.Column(db.Numeric(10, 2), nullable=False, default=0.00)
    tax_rate = db.Column(db.Numeric(5, 4), nullable=False, default=0.0000)
    tax_amount = db.Column(db.Numeric(10, 2), nullable=False, default=0.00)
    
    # Discount for this specific item
    discount_amount = db.Column(db.Numeric(10, 2), nullable=False, default=0.00)
    
    # Product snapshot (in case product details change later)
    product_name = db.Column(db.String(200), nullable=False)
    product_sku = db.Column(db.String(100), nullable=True)
    cost_price = db.Column(db.Numeric(10, 2), nullable=False, default=0.00)
    
    # Indexes for optimization
    __table_args__ = (
        db.Index('idx_transaction_item_tenant_transaction', 'tenant_id', 'transaction_id'),
        db.Index('idx_transaction_item_tenant_product', 'tenant_id', 'product_id'),
        db.Index('idx_transaction_item_transaction_product', 'transaction_id', 'product_id'),
        db.Index('idx_transaction_item_product_date', 'product_id', 'created_at'),
    )
    
    def __repr__(self):
        return f'<TransactionItem {self.product_name} x{self.quantity}>'
    
    def calculate_totals(self):
        """Calculate line total and tax amount."""
        # Skip calculation if essential fields are not set
        if self.quantity is None or self.unit_price is None:
            return
        
        # Get product details if not already set
        if not self.product_name and self.product:
            self.product_name = self.product.name
            self.product_sku = self.product.sku
            self.cost_price = self.product.cost_price
            self.tax_rate = self.product.tax_rate
        
        # Ensure discount_amount is not None
        if self.discount_amount is None:
            self.discount_amount = Decimal('0.00')
        
        # Ensure tax_rate is not None
        if self.tax_rate is None:
            self.tax_rate = Decimal('0.0000')
        
        # Calculate line total before tax
        gross_total = self.quantity * self.unit_price
        discounted_total = gross_total - self.discount_amount
        
        if discounted_total < 0:
            discounted_total = Decimal('0.00')
        
        # Calculate tax
        if self.product and self.product.tax_inclusive:
            # Tax is included in unit price
            self.tax_amount = discounted_total - (discounted_total / (1 + self.tax_rate))
            self.line_total = discounted_total
        else:
            # Tax is added to unit price
            self.tax_amount = discounted_total * self.tax_rate
            self.line_total = discounted_total
    
    def get_profit_amount(self):
        """Calculate profit for this line item."""
        if not self.cost_price:
            return Decimal('0.00')
        
        # Profit = (selling price - cost price) * quantity - discount
        unit_profit = self.unit_price - self.cost_price
        total_profit = (unit_profit * self.quantity) - self.discount_amount
        return max(Decimal('0.00'), total_profit)
    
    def get_profit_margin(self):
        """Calculate profit margin percentage for this line item."""
        if not self.cost_price or self.cost_price == 0:
            return Decimal('0.00')
        
        unit_profit = self.unit_price - self.cost_price
        return (unit_profit / self.cost_price) * 100
    
    def get_total_cost(self):
        """Get total cost for this line item."""
        return self.cost_price * self.quantity
    
    def get_discount_percentage(self):
        """Calculate discount percentage for this line item."""
        gross_total = self.quantity * self.unit_price
        if gross_total == 0:
            return Decimal('0.00')
        return (self.discount_amount / gross_total) * 100


# Note: Removed automatic event listeners to avoid recursion issues
# Calculations will be called manually when needed


@event.listens_for(TransactionItem, 'after_insert')
@event.listens_for(TransactionItem, 'after_update')
@event.listens_for(TransactionItem, 'after_delete')
def update_transaction_totals(mapper, connection, target):
    """Update transaction totals when items change."""
    if hasattr(target, 'transaction_id') and target.transaction_id:
        # We need to update the transaction totals
        # This will be handled by the transaction's calculate_totals method
        pass


# Validation event listeners
@event.listens_for(TransactionItem, 'before_insert')
@event.listens_for(TransactionItem, 'before_update')
def validate_transaction_item(mapper, connection, target):
    """Validate transaction item data."""
    if target.quantity <= 0:
        raise ValueError("Quantity must be greater than 0")
    
    if target.unit_price < 0:
        raise ValueError("Unit price cannot be negative")
    
    if target.discount_amount < 0:
        raise ValueError("Discount amount cannot be negative")
    
    # Ensure discount doesn't exceed line total
    gross_total = target.quantity * target.unit_price
    if target.discount_amount > gross_total:
        raise ValueError("Discount amount cannot exceed line total")


@event.listens_for(Transaction, 'before_insert')
def set_transaction_number(mapper, connection, target):
    """Set transaction number if not already set."""
    if not target.transaction_number:
        target.transaction_number = Transaction.generate_transaction_number(target.tenant_id)