"""POS routes for SaaS POS System."""

from flask import Blueprint, render_template, request, jsonify, redirect, url_for, flash
from flask_login import login_required, current_user
from decimal import Decimal, InvalidOperation

from app.services.pos_service import POSService
from app.services.inventory_service import InventoryService
from app.models.product import Product, Category
from app.models.base import set_current_tenant
from app.forms.pos_forms import (
    CustomerForm, AddItemForm, UpdateQuantityForm, DiscountForm,
    PaymentForm, CancelTransactionForm
)
from app.utils.decorators import (
    validate_json_request, validate_search_params, validate_numeric_params
)
from app.utils.validators import validate_search_query, validate_pagination_params

bp = Blueprint('pos', __name__, url_prefix='/pos')


@bp.before_request
@login_required
def require_login():
    """Require login for all POS routes."""
    if not current_user.can_process_transactions():
        flash('You do not have permission to access the POS system.', 'error')
        return redirect(url_for('main.index'))
    
    # Set tenant context
    set_current_tenant(current_user.tenant_id)


@bp.route('/dashboard')
def dashboard():
    """POS dashboard with product selection interface."""
    try:
        # Get categories for navigation
        categories = Category.query.filter_by(
            tenant_id=current_user.tenant_id,
            is_active=True
        ).order_by(Category.sort_order, Category.name).all()
        
        # Get featured products
        featured_products = Product.query.filter_by(
            tenant_id=current_user.tenant_id,
            is_active=True,
            is_featured=True
        ).order_by(Product.sort_order, Product.name).limit(12).all()
        
        # Get pending transactions for current user
        pending_transactions = POSService.get_pending_transactions(current_user.id)
        
        return render_template('pos/dashboard.html',
                             categories=categories,
                             featured_products=featured_products,
                             pending_transactions=pending_transactions)
    
    except Exception as e:
        flash(f'Error loading POS dashboard: {str(e)}', 'error')
        return redirect(url_for('main.index'))


@bp.route('/products')
@validate_search_params()
def products():
    """Get products for POS interface (AJAX endpoint)."""
    try:
        category_id = request.args.get('category_id', type=int)
        search = getattr(request, 'validated_search', '')
        page = getattr(request, 'validated_page', 1)
        per_page = getattr(request, 'validated_per_page', 50)
        
        # Get products using inventory service
        result = InventoryService.get_products(
            page=page,
            per_page=per_page,
            category_id=category_id,
            search=search,
            active_only=True
        )
        
        if not result['success']:
            return jsonify({'error': result['error']}), 400
        
        # Format products for POS interface
        products_data = []
        for product in result['products']:
            products_data.append({
                'id': product['id'],
                'name': product['name'],
                'sku': product['sku'],
                'selling_price': product['selling_price'],
                'current_stock': product['current_stock'],
                'track_inventory': product['track_inventory'],
                'can_sell': not product['track_inventory'] or product['current_stock'] > 0,
                'is_low_stock': product['is_low_stock'],
                'image_url': product['image_url'],
                'color': product['color']
            })
        
        return jsonify({
            'products': products_data,
            'pagination': result['pagination']
        })
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@bp.route('/transaction/create', methods=['POST'])
@validate_json_request(max_length=500)
def create_transaction():
    """Create a new transaction."""
    try:
        data = getattr(request, 'validated_json', {})
        
        customer_data = None
        if data.get('customer_name') or data.get('customer_email') or data.get('customer_phone'):
            customer_data = {
                'name': data.get('customer_name'),
                'email': data.get('customer_email'),
                'phone': data.get('customer_phone'),
                'table_number': data.get('table_number'),
                'order_type': data.get('order_type', 'dine_in')
            }
        
        result = POSService.create_transaction(current_user.id, customer_data)
        
        if result['success']:
            return jsonify(result)
        else:
            return jsonify({'error': result['error']}), 400
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@bp.route('/transaction/<int:transaction_id>')
def get_transaction(transaction_id):
    """Get transaction details."""
    try:
        transaction = POSService.get_transaction(transaction_id)
        
        if transaction:
            return jsonify({'transaction': transaction})
        else:
            return jsonify({'error': 'Transaction not found'}), 404
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@bp.route('/transaction/<int:transaction_id>/add_item', methods=['POST'])
@validate_json_request(required_fields=['product_id'], max_length=200)
@validate_numeric_params('transaction_id')
def add_item_to_cart(transaction_id):
    """Add item to transaction cart."""
    try:
        data = getattr(request, 'validated_json', {})
        product_id = data.get('product_id')
        quantity = data.get('quantity', 1)
        unit_price = data.get('unit_price')
        
        if quantity <= 0:
            return jsonify({'error': 'Quantity must be greater than 0'}), 400
        
        # Convert unit_price to Decimal if provided
        if unit_price is not None:
            try:
                unit_price = Decimal(str(unit_price))
            except (ValueError, InvalidOperation):
                return jsonify({'error': 'Invalid unit price format'}), 400
        
        result = POSService.add_item_to_cart(transaction_id, product_id, quantity, unit_price)
        
        if result['success']:
            return jsonify(result)
        else:
            return jsonify({'error': result['error']}), 400
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@bp.route('/transaction/<int:transaction_id>/remove_item', methods=['POST'])
@validate_json_request(required_fields=['product_id'], max_length=100)
@validate_numeric_params('transaction_id')
def remove_item_from_cart(transaction_id):
    """Remove item from transaction cart."""
    try:
        data = getattr(request, 'validated_json', {})
        product_id = data.get('product_id')
        
        result = POSService.remove_item_from_cart(transaction_id, product_id)
        
        if result['success']:
            return jsonify(result)
        else:
            return jsonify({'error': result['error']}), 400
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@bp.route('/transaction/<int:transaction_id>/update_quantity', methods=['POST'])
@validate_json_request(required_fields=['product_id', 'quantity'], max_length=100)
@validate_numeric_params('transaction_id')
def update_item_quantity(transaction_id):
    """Update item quantity in transaction cart."""
    try:
        data = getattr(request, 'validated_json', {})
        product_id = data.get('product_id')
        quantity = data.get('quantity')
        
        if quantity < 0:
            return jsonify({'error': 'Quantity cannot be negative'}), 400
        
        result = POSService.update_item_quantity(transaction_id, product_id, quantity)
        
        if result['success']:
            return jsonify(result)
        else:
            return jsonify({'error': result['error']}), 400
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@bp.route('/transaction/<int:transaction_id>/apply_discount', methods=['POST'])
@validate_json_request(required_fields=['discount_type', 'discount_value'], max_length=300)
@validate_numeric_params('transaction_id')
def apply_discount(transaction_id):
    """Apply discount to transaction."""
    try:
        data = getattr(request, 'validated_json', {})
        discount_type = data.get('discount_type')
        discount_value = data.get('discount_value')
        reason = data.get('reason')
        
        try:
            discount_value = Decimal(str(discount_value))
        except (ValueError, InvalidOperation):
            return jsonify({'error': 'Invalid discount value format'}), 400
        
        result = POSService.apply_discount(transaction_id, discount_type, discount_value, reason)
        
        if result['success']:
            return jsonify(result)
        else:
            return jsonify({'error': result['error']}), 400
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@bp.route('/transaction/<int:transaction_id>/process_payment', methods=['POST'])
@validate_json_request(required_fields=['payment_method', 'amount_paid'], max_length=200)
@validate_numeric_params('transaction_id')
def process_payment(transaction_id):
    """Process payment for transaction."""
    try:
        data = getattr(request, 'validated_json', {})
        payment_method = data.get('payment_method')
        amount_paid = data.get('amount_paid')
        
        try:
            amount_paid = Decimal(str(amount_paid))
        except (ValueError, InvalidOperation):
            return jsonify({'error': 'Invalid amount format'}), 400
        
        result = POSService.process_payment(transaction_id, payment_method, amount_paid)
        
        if result['success']:
            return jsonify(result)
        else:
            return jsonify({'error': result['error']}), 400
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@bp.route('/transaction/<int:transaction_id>/cancel', methods=['POST'])
def cancel_transaction(transaction_id):
    """Cancel transaction."""
    try:
        data = request.get_json() or {}
        reason = data.get('reason')
        
        result = POSService.cancel_transaction(transaction_id, reason)
        
        if result['success']:
            return jsonify(result)
        else:
            return jsonify({'error': result['error']}), 400
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@bp.route('/transaction/<int:transaction_id>/receipt')
def generate_receipt(transaction_id):
    """Generate and display receipt."""
    try:
        result = POSService.generate_receipt(transaction_id)
        
        if result['success']:
            return render_template('pos/receipt.html', receipt=result['receipt'])
        else:
            flash(f'Error generating receipt: {result["error"]}', 'error')
            return redirect(url_for('pos.dashboard'))
    
    except Exception as e:
        flash(f'Error generating receipt: {str(e)}', 'error')
        return redirect(url_for('pos.dashboard'))


@bp.route('/transaction/<int:transaction_id>/receipt/print')
def print_receipt(transaction_id):
    """Generate receipt for printing (JSON format)."""
    try:
        result = POSService.generate_receipt(transaction_id)
        
        if result['success']:
            return jsonify(result)
        else:
            return jsonify({'error': result['error']}), 400
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@bp.route('/transactions/pending')
def pending_transactions():
    """Get pending transactions."""
    try:
        user_id = request.args.get('user_id', type=int)
        transactions = POSService.get_pending_transactions(user_id)
        
        return jsonify({'transactions': transactions})
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@bp.route('/checkout/<int:transaction_id>')
def checkout(transaction_id):
    """Checkout page for completing transaction."""
    try:
        transaction = POSService.get_transaction(transaction_id)
        
        if not transaction or not isinstance(transaction, dict):
            flash('Transaction not found.', 'error')
            return redirect(url_for('pos.dashboard'))
        
        if 'status' not in transaction:
            flash('Invalid transaction data.', 'error')
            return redirect(url_for('pos.dashboard'))
        
        if transaction['status'] != 'pending':
            flash('Transaction is not in pending status.', 'error')
            return redirect(url_for('pos.dashboard'))
        
        # S'assurer que tous les champs requis sont présents
        required_fields = ['id', 'transaction_number', 'total_amount', 'subtotal', 'tax_amount', 'items']
        if not all(field in transaction for field in required_fields):
            flash('Invalid transaction data structure.', 'error')
            return redirect(url_for('pos.dashboard'))
            
        return render_template('pos/checkout.html', transaction=transaction)
    
    except Exception as e:
        flash(f'Error loading checkout: {str(e)}', 'error')
        return redirect(url_for('pos.dashboard'))





