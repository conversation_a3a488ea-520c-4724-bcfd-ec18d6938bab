"""Audit logging models for SaaS POS System."""

from datetime import datetime
from enum import Enum
from app import db
from app.models.base import BaseModel


class AuditAction(Enum):
    """Enumeration of audit actions."""
    
    # Authentication actions
    LOGIN = 'login'
    LOGOUT = 'logout'
    LOGIN_FAILED = 'login_failed'
    PASSWORD_RESET = 'password_reset'
    ACCOUNT_LOCKED = 'account_locked'
    
    # User management actions
    USER_CREATED = 'user_created'
    USER_UPDATED = 'user_updated'
    USER_DELETED = 'user_deleted'
    USER_ROLE_CHANGED = 'user_role_changed'
    
    # Product/Inventory actions
    PRODUCT_CREATED = 'product_created'
    PRODUCT_UPDATED = 'product_updated'
    PRODUCT_DELETED = 'product_deleted'
    INVENTORY_ADJUSTED = 'inventory_adjusted'
    
    # Transaction actions
    TRANSACTION_CREATED = 'transaction_created'
    TRANSACTION_UPDATED = 'transaction_updated'
    TRANSACTION_VOIDED = 'transaction_voided'
    PAYMENT_PROCESSED = 'payment_processed'
    
    # System actions
    BACKUP_CREATED = 'backup_created'
    BACKUP_RESTORED = 'backup_restored'
    DATA_EXPORTED = 'data_exported'
    SETTINGS_CHANGED = 'settings_changed'
    
    # Security actions
    UNAUTHORIZED_ACCESS = 'unauthorized_access'
    SUSPICIOUS_ACTIVITY = 'suspicious_activity'
    DATA_BREACH_ATTEMPT = 'data_breach_attempt'


class AuditSeverity(Enum):
    """Enumeration of audit severity levels."""
    
    LOW = 'low'
    MEDIUM = 'medium'
    HIGH = 'high'
    CRITICAL = 'critical'


class AuditLog(BaseModel):
    """Audit log model for tracking critical operations."""
    
    __tablename__ = 'audit_log'
    
    # Core audit information
    action = db.Column(db.Enum(AuditAction), nullable=False, index=True)
    severity = db.Column(db.Enum(AuditSeverity), nullable=False, default=AuditSeverity.LOW, index=True)
    
    # User and tenant information
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=True, index=True)
    tenant_id = db.Column(db.Integer, db.ForeignKey('tenant.id'), nullable=False, index=True)
    
    # Request information
    ip_address = db.Column(db.String(45), nullable=True, index=True)  # IPv6 support
    user_agent = db.Column(db.Text, nullable=True)
    request_method = db.Column(db.String(10), nullable=True)
    request_url = db.Column(db.Text, nullable=True)
    
    # Audit details
    description = db.Column(db.Text, nullable=False)
    old_values = db.Column(db.JSON, nullable=True)  # Previous state for updates
    new_values = db.Column(db.JSON, nullable=True)  # New state for updates
    
    # Resource information
    resource_type = db.Column(db.String(50), nullable=True, index=True)  # e.g., 'product', 'user', 'transaction'
    resource_id = db.Column(db.String(50), nullable=True, index=True)    # ID of the affected resource
    
    # Status and outcome
    success = db.Column(db.Boolean, nullable=False, default=True, index=True)
    error_message = db.Column(db.Text, nullable=True)
    
    # Additional metadata
    session_id = db.Column(db.String(100), nullable=True, index=True)
    correlation_id = db.Column(db.String(100), nullable=True, index=True)  # For tracking related operations
    
    # Relationships
    user = db.relationship('User', backref='audit_logs', lazy=True)
    tenant = db.relationship('Tenant', backref='audit_logs', lazy=True)
    
    # Indexes for performance
    __table_args__ = (
        db.Index('idx_audit_tenant_action', 'tenant_id', 'action'),
        db.Index('idx_audit_tenant_created', 'tenant_id', 'created_at'),
        db.Index('idx_audit_user_action', 'user_id', 'action'),
        db.Index('idx_audit_severity_created', 'severity', 'created_at'),
        db.Index('idx_audit_resource', 'resource_type', 'resource_id'),
        db.Index('idx_audit_success_created', 'success', 'created_at'),
        db.Index('idx_audit_ip_created', 'ip_address', 'created_at'),
    )
    
    def __repr__(self):
        return f'<AuditLog {self.action.value} by {self.user_id} at {self.created_at}>'
    
    @classmethod
    def log_action(cls, action, tenant_id, description, user_id=None, severity=AuditSeverity.LOW,
                   ip_address=None, user_agent=None, request_method=None, request_url=None,
                   old_values=None, new_values=None, resource_type=None, resource_id=None,
                   success=True, error_message=None, session_id=None, correlation_id=None):
        """Create an audit log entry."""
        
        audit_log = cls(
            action=action,
            severity=severity,
            user_id=user_id,
            tenant_id=tenant_id,
            ip_address=ip_address,
            user_agent=user_agent,
            request_method=request_method,
            request_url=request_url,
            description=description,
            old_values=old_values,
            new_values=new_values,
            resource_type=resource_type,
            resource_id=str(resource_id) if resource_id else None,
            success=success,
            error_message=error_message,
            session_id=session_id,
            correlation_id=correlation_id
        )
        
        return audit_log.save()
    
    @classmethod
    def get_logs_for_tenant(cls, tenant_id, limit=100, offset=0, action=None, severity=None,
                           start_date=None, end_date=None, user_id=None):
        """Get audit logs for a specific tenant with filtering options."""
        
        query = cls.query.filter_by(tenant_id=tenant_id)
        
        if action:
            query = query.filter_by(action=action)
        
        if severity:
            query = query.filter_by(severity=severity)
        
        if user_id:
            query = query.filter_by(user_id=user_id)
        
        if start_date:
            query = query.filter(cls.created_at >= start_date)
        
        if end_date:
            query = query.filter(cls.created_at <= end_date)
        
        return query.order_by(cls.created_at.desc()).offset(offset).limit(limit).all()
    
    @classmethod
    def get_security_events(cls, tenant_id=None, hours=24):
        """Get security-related events from the last N hours."""
        
        from datetime import timedelta
        
        security_actions = [
            AuditAction.LOGIN_FAILED,
            AuditAction.ACCOUNT_LOCKED,
            AuditAction.UNAUTHORIZED_ACCESS,
            AuditAction.SUSPICIOUS_ACTIVITY,
            AuditAction.DATA_BREACH_ATTEMPT
        ]
        
        since = datetime.utcnow() - timedelta(hours=hours)
        query = cls.query.filter(
            cls.action.in_(security_actions),
            cls.created_at >= since
        )
        
        if tenant_id:
            query = query.filter_by(tenant_id=tenant_id)
        
        return query.order_by(cls.created_at.desc()).all()
    
    @classmethod
    def cleanup_old_logs(cls, retention_days=90):
        """Clean up audit logs older than retention period."""
        
        from datetime import timedelta
        
        cutoff_date = datetime.utcnow() - timedelta(days=retention_days)
        
        # Delete old logs in batches to avoid locking the table
        batch_size = 1000
        deleted_count = 0
        
        while True:
            logs_to_delete = cls.query.filter(cls.created_at < cutoff_date).limit(batch_size).all()
            
            if not logs_to_delete:
                break
            
            for log in logs_to_delete:
                db.session.delete(log)
            
            db.session.commit()
            deleted_count += len(logs_to_delete)
        
        return deleted_count


class BackupLog(BaseModel):
    """Backup log model for tracking backup operations."""
    
    __tablename__ = 'backup_log'
    
    # Backup information
    backup_type = db.Column(db.String(20), nullable=False, index=True)  # 'full', 'incremental', 'manual'
    backup_status = db.Column(db.String(20), nullable=False, default='in_progress', index=True)  # 'in_progress', 'completed', 'failed'
    
    # File information
    backup_filename = db.Column(db.String(255), nullable=False)
    backup_path = db.Column(db.Text, nullable=False)
    backup_size = db.Column(db.BigInteger, nullable=True)  # Size in bytes
    
    # Timing information
    started_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)
    completed_at = db.Column(db.DateTime, nullable=True)
    duration_seconds = db.Column(db.Integer, nullable=True)
    
    # Backup details
    tenant_id = db.Column(db.Integer, db.ForeignKey('tenant.id'), nullable=True, index=True)  # NULL for full system backup
    tables_backed_up = db.Column(db.JSON, nullable=True)  # List of table names
    records_count = db.Column(db.Integer, nullable=True)  # Total records backed up
    
    # Error information
    error_message = db.Column(db.Text, nullable=True)
    error_details = db.Column(db.JSON, nullable=True)
    
    # Verification
    checksum = db.Column(db.String(64), nullable=True)  # SHA-256 checksum
    verified = db.Column(db.Boolean, nullable=False, default=False)
    verification_date = db.Column(db.DateTime, nullable=True)
    
    # Relationships
    tenant = db.relationship('Tenant', backref='backup_logs', lazy=True)
    
    # Indexes for performance
    __table_args__ = (
        db.Index('idx_backup_status_created', 'backup_status', 'created_at'),
        db.Index('idx_backup_type_created', 'backup_type', 'created_at'),
        db.Index('idx_backup_tenant_created', 'tenant_id', 'created_at'),
        db.Index('idx_backup_completed', 'completed_at'),
    )
    
    def __repr__(self):
        return f'<BackupLog {self.backup_type} {self.backup_status} at {self.created_at}>'
    
    def mark_completed(self, backup_size=None, records_count=None, checksum=None):
        """Mark backup as completed."""
        self.backup_status = 'completed'
        self.completed_at = datetime.utcnow()
        self.duration_seconds = int((self.completed_at - self.started_at).total_seconds())
        
        if backup_size:
            self.backup_size = backup_size
        
        if records_count:
            self.records_count = records_count
        
        if checksum:
            self.checksum = checksum
        
        self.save()
    
    def mark_failed(self, error_message, error_details=None):
        """Mark backup as failed."""
        self.backup_status = 'failed'
        self.completed_at = datetime.utcnow()
        self.duration_seconds = int((self.completed_at - self.started_at).total_seconds())
        self.error_message = error_message
        self.error_details = error_details
        self.save()
    
    def verify_backup(self, checksum):
        """Verify backup integrity."""
        self.verified = (self.checksum == checksum)
        self.verification_date = datetime.utcnow()
        self.save()
        return self.verified
    
    @classmethod
    def get_recent_backups(cls, limit=10, backup_type=None, tenant_id=None):
        """Get recent backup logs."""
        query = cls.query
        
        if backup_type:
            query = query.filter_by(backup_type=backup_type)
        
        if tenant_id:
            query = query.filter_by(tenant_id=tenant_id)
        
        return query.order_by(cls.created_at.desc()).limit(limit).all()
    
    @classmethod
    def cleanup_old_backups(cls, retention_days=30):
        """Clean up old backup logs and files."""
        from datetime import timedelta
        import os
        
        cutoff_date = datetime.utcnow() - timedelta(days=retention_days)
        old_backups = cls.query.filter(cls.created_at < cutoff_date).all()
        
        deleted_count = 0
        for backup in old_backups:
            # Try to delete the backup file
            try:
                if os.path.exists(backup.backup_path):
                    os.remove(backup.backup_path)
            except Exception as e:
                # Log the error but continue with database cleanup
                print(f"Failed to delete backup file {backup.backup_path}: {e}")
            
            # Delete the database record
            db.session.delete(backup)
            deleted_count += 1
        
        db.session.commit()
        return deleted_count