"""Unit tests for base model functionality."""

import pytest
from datetime import datetime
from flask import g
from app import create_app, db
from app.models.base import BaseModel, TenantAwareModel, set_current_tenant, get_current_tenant, clear_current_tenant
from config import TestingConfig


# Mock tenant table for testing
class MockTenant(BaseModel):
    """Mock tenant model for testing."""
    __tablename__ = 'tenant'
    name = db.Column(db.String(100), nullable=False)


# Test model classes
class TestModel(BaseModel):
    """Test model without tenant isolation."""
    __tablename__ = 'test_model'
    name = db.Column(db.String(100), nullable=False)


class TestTenantModel(TenantAwareModel):
    """Test model with tenant isolation."""
    __tablename__ = 'test_tenant_model'
    name = db.Column(db.String(100), nullable=False)


@pytest.fixture
def app():
    """Create test application."""
    app = create_app(TestingConfig)
    return app


@pytest.fixture
def client(app):
    """Create test client."""
    return app.test_client()


@pytest.fixture
def app_context(app):
    """Create application context."""
    with app.app_context():
        db.create_all()
        yield app
        db.session.remove()
        db.drop_all()


class TestBaseModel:
    """Test cases for BaseModel functionality."""
    
    def test_base_model_creation(self, app_context):
        """Test basic model creation and saving."""
        model = TestModel(name='Test Item')
        saved_model = model.save()
        
        assert saved_model.id is not None
        assert saved_model.name == 'Test Item'
        assert saved_model.created_at is not None
        assert saved_model.updated_at is not None
        assert isinstance(saved_model.created_at, datetime)
        assert isinstance(saved_model.updated_at, datetime)
    
    def test_base_model_create_classmethod(self, app_context):
        """Test model creation using create classmethod."""
        model = TestModel.create(name='Test Item 2')
        
        assert model.id is not None
        assert model.name == 'Test Item 2'
        assert model.created_at is not None
        assert model.updated_at is not None
    
    def test_base_model_to_dict(self, app_context):
        """Test model to_dict conversion."""
        model = TestModel.create(name='Test Item 3')
        model_dict = model.to_dict()
        
        assert isinstance(model_dict, dict)
        assert model_dict['id'] == model.id
        assert model_dict['name'] == 'Test Item 3'
        assert 'created_at' in model_dict
        assert 'updated_at' in model_dict
        # Datetime should be converted to ISO format string
        assert isinstance(model_dict['created_at'], str)
        assert isinstance(model_dict['updated_at'], str)
    
    def test_base_model_delete(self, app_context):
        """Test model deletion."""
        model = TestModel.create(name='Test Item to Delete')
        model_id = model.id
        
        # Verify model exists
        found_model = TestModel.query.get(model_id)
        assert found_model is not None
        
        # Delete model
        result = model.delete()
        assert result is True
        
        # Verify model is deleted
        deleted_model = TestModel.query.get(model_id)
        assert deleted_model is None
    
    def test_updated_at_changes(self, app_context):
        """Test that updated_at changes when model is modified."""
        model = TestModel.create(name='Original Name')
        original_updated_at = model.updated_at
        
        # Small delay to ensure timestamp difference
        import time
        time.sleep(0.01)
        
        # Update model
        model.name = 'Updated Name'
        model.save()
        
        assert model.updated_at > original_updated_at


class TestTenantAwareModel:
    """Test cases for TenantAwareModel functionality."""
    
    def test_tenant_model_creation_with_tenant_id(self, app_context):
        """Test tenant model creation with explicit tenant_id."""
        model = TestTenantModel.create(name='Tenant Item', tenant_id=1)
        
        assert model.id is not None
        assert model.name == 'Tenant Item'
        assert model.tenant_id == 1
    
    def test_tenant_model_creation_with_current_tenant(self, app_context):
        """Test tenant model creation with current tenant set."""
        set_current_tenant(2)
        
        model = TestTenantModel.create(name='Auto Tenant Item')
        
        assert model.id is not None
        assert model.name == 'Auto Tenant Item'
        assert model.tenant_id == 2
        
        clear_current_tenant()
    
    def test_tenant_model_creation_without_tenant(self, app_context):
        """Test tenant model creation without tenant_id should fail."""
        clear_current_tenant()
        
        with pytest.raises(Exception):  # Should fail due to NOT NULL constraint
            TestTenantModel.create(name='No Tenant Item')


class TestTenantUtilities:
    """Test cases for tenant utility functions."""
    
    def test_set_and_get_current_tenant(self, app_context):
        """Test setting and getting current tenant."""
        # Initially no tenant should be set
        assert get_current_tenant() is None
        
        # Set tenant
        set_current_tenant(5)
        assert get_current_tenant() == 5
        
        # Clear tenant
        clear_current_tenant()
        assert get_current_tenant() is None
    
    def test_multiple_tenant_operations(self, app_context):
        """Test multiple tenant operations."""
        # Set first tenant
        set_current_tenant(1)
        model1 = TestTenantModel.create(name='Tenant 1 Item')
        
        # Set second tenant
        set_current_tenant(2)
        model2 = TestTenantModel.create(name='Tenant 2 Item')
        
        # Verify tenant isolation
        assert model1.tenant_id == 1
        assert model2.tenant_id == 2
        assert model1.tenant_id != model2.tenant_id
        
        clear_current_tenant()


class TestDatabaseOperations:
    """Test database operations and error handling."""
    
    def test_save_rollback_on_error(self, app_context):
        """Test that save operations rollback on error."""
        # This test would need a scenario that causes a database error
        # For now, we'll test the basic save functionality
        model = TestModel(name='Test Rollback')
        saved_model = model.save()
        assert saved_model.id is not None
    
    def test_delete_rollback_on_error(self, app_context):
        """Test that delete operations rollback on error."""
        model = TestModel.create(name='Test Delete Rollback')
        result = model.delete()
        assert result is True