"""Unit tests for AuthService."""

import pytest
from datetime import datetime, timedelta
from unittest.mock import patch, MagicMock
from flask import session
from flask_login import current_user

from app import create_app, db, cache
from app.models.user import User, Tenant
from app.services.auth_service import (
    AuthService, 
    AuthenticationError, 
    InvalidCredentialsError,
    AccountLockedError,
    TenantInactiveError
)
from config import TestingConfig


@pytest.fixture
def app():
    """Create test application."""
    app = create_app(TestingConfig)
    return app


@pytest.fixture
def client(app):
    """Create test client."""
    return app.test_client()


@pytest.fixture
def app_context(app):
    """Create application context."""
    with app.app_context():
        db.create_all()
        yield app
        db.session.remove()
        db.drop_all()


@pytest.fixture
def request_context(app):
    """Create request context for session testing."""
    with app.test_request_context():
        yield app


@pytest.fixture
def sample_tenant(app_context):
    """Create a sample tenant for testing."""
    tenant = Tenant.create(
        name='Test Business',
        business_type='restaurant',
        email='<EMAIL>',
        subscription_status='active'
    )
    return tenant


@pytest.fixture
def inactive_tenant(app_context):
    """Create an inactive tenant for testing."""
    tenant = Tenant.create(
        name='Inactive Business',
        business_type='retail',
        email='<EMAIL>',
        subscription_status='inactive'
    )
    return tenant


@pytest.fixture
def expired_tenant(app_context):
    """Create an expired tenant for testing."""
    tenant = Tenant.create(
        name='Expired Business',
        business_type='retail',
        email='<EMAIL>',
        subscription_status='active',
        subscription_expires=datetime.utcnow() - timedelta(days=1)
    )
    return tenant


@pytest.fixture
def sample_user(app_context, sample_tenant):
    """Create a sample user for testing."""
    user = User.create_user(
        email='<EMAIL>',
        password='testpassword123',
        first_name='John',
        last_name='Doe',
        tenant_id=sample_tenant.id,
        role='manager'
    )
    return user


@pytest.fixture
def admin_user(app_context, sample_tenant):
    """Create an admin user for testing."""
    user = User.create_user(
        email='<EMAIL>',
        password='adminpassword123',
        first_name='Admin',
        last_name='User',
        tenant_id=sample_tenant.id,
        role='admin'
    )
    return user


@pytest.fixture
def locked_user(app_context, sample_tenant):
    """Create a locked user for testing."""
    user = User.create_user(
        email='<EMAIL>',
        password='password123',
        first_name='Locked',
        last_name='User',
        tenant_id=sample_tenant.id
    )
    # Lock the account
    user.failed_login_attempts = 5
    user.locked_until = datetime.utcnow() + timedelta(minutes=30)
    user.save()
    return user


class TestAuthServiceLogin:
    """Test cases for AuthService login functionality."""
    
    def test_successful_login(self, request_context, sample_user, sample_tenant):
        """Test successful user login."""
        result = AuthService.login(
            email='<EMAIL>',
            password='testpassword123',
            tenant_id=sample_tenant.id,
            remember_me=False
        )
        
        assert result['success'] is True
        assert result['user']['id'] == sample_user.id
        assert result['user']['email'] == sample_user.email
        assert result['user']['full_name'] == 'John Doe'
        assert result['user']['role'] == 'manager'
        assert result['tenant']['id'] == sample_tenant.id
        assert result['tenant']['name'] == sample_tenant.name
        
        # Check session data
        assert session['tenant_id'] == sample_tenant.id
        assert session['tenant_name'] == sample_tenant.name
        assert session['business_type'] == sample_tenant.business_type
    
    def test_login_with_invalid_credentials(self, request_context, sample_user, sample_tenant):
        """Test login with invalid credentials."""
        with pytest.raises(InvalidCredentialsError):
            AuthService.login(
                email='<EMAIL>',
                password='wrongpassword',
                tenant_id=sample_tenant.id
            )
    
    def test_login_with_nonexistent_user(self, request_context, sample_tenant):
        """Test login with non-existent user."""
        with pytest.raises(InvalidCredentialsError):
            AuthService.login(
                email='<EMAIL>',
                password='password123',
                tenant_id=sample_tenant.id
            )
    
    def test_login_with_inactive_tenant(self, request_context, sample_user, inactive_tenant):
        """Test login with inactive tenant."""
        # Create user for inactive tenant
        user = User.create_user(
            email='<EMAIL>',
            password='password123',
            first_name='Test',
            last_name='User',
            tenant_id=inactive_tenant.id
        )
        
        with pytest.raises(TenantInactiveError):
            AuthService.login(
                email='<EMAIL>',
                password='password123',
                tenant_id=inactive_tenant.id
            )
    
    def test_login_with_expired_tenant(self, request_context, expired_tenant):
        """Test login with expired tenant subscription."""
        # Create user for expired tenant
        user = User.create_user(
            email='<EMAIL>',
            password='password123',
            first_name='Test',
            last_name='User',
            tenant_id=expired_tenant.id
        )
        
        with pytest.raises(TenantInactiveError):
            AuthService.login(
                email='<EMAIL>',
                password='password123',
                tenant_id=expired_tenant.id
            )
    
    def test_login_with_locked_account(self, request_context, locked_user, sample_tenant):
        """Test login with locked account."""
        with pytest.raises(AccountLockedError):
            AuthService.login(
                email='<EMAIL>',
                password='password123',
                tenant_id=sample_tenant.id
            )
    
    def test_login_with_inactive_user(self, request_context, sample_user, sample_tenant):
        """Test login with inactive user."""
        sample_user.is_active = False
        sample_user.save()
        
        with pytest.raises(InvalidCredentialsError):
            AuthService.login(
                email='<EMAIL>',
                password='testpassword123',
                tenant_id=sample_tenant.id
            )
    
    def test_login_with_nonexistent_tenant(self, request_context, sample_user):
        """Test login with non-existent tenant."""
        with pytest.raises(TenantInactiveError):
            AuthService.login(
                email='<EMAIL>',
                password='testpassword123',
                tenant_id=99999  # Non-existent tenant ID
            )
    
    @patch('app.services.auth_service.cache')
    def test_login_caches_permissions(self, mock_cache, request_context, sample_user, sample_tenant):
        """Test that login caches user permissions."""
        AuthService.login(
            email='<EMAIL>',
            password='testpassword123',
            tenant_id=sample_tenant.id
        )
        
        # Verify cache.set was called
        mock_cache.set.assert_called_once()
        call_args = mock_cache.set.call_args
        assert call_args[0][0] == f"user_permissions_{sample_user.id}"
        assert 'can_access_reports' in call_args[0][1]


class TestAuthServiceLogout:
    """Test cases for AuthService logout functionality."""
    
    @patch('app.services.auth_service.current_user')
    @patch('app.services.auth_service.logout_user')
    def test_successful_logout(self, mock_logout_user, mock_current_user, request_context):
        """Test successful logout."""
        mock_current_user.is_authenticated = True
        mock_current_user.id = 1
        
        with patch.object(AuthService, '_clear_user_cache') as mock_clear_cache:
            result = AuthService.logout()
            
            assert result['success'] is True
            assert result['message'] == 'Successfully logged out'
            mock_logout_user.assert_called_once()
            mock_clear_cache.assert_called_once_with(1)
    
    @patch('app.services.auth_service.current_user')
    @patch('app.services.auth_service.logout_user')
    def test_logout_unauthenticated_user(self, mock_logout_user, mock_current_user, request_context):
        """Test logout when user is not authenticated."""
        mock_current_user.is_authenticated = False
        
        result = AuthService.logout()
        
        assert result['success'] is True
        mock_logout_user.assert_called_once()


class TestAuthServiceRegistration:
    """Test cases for AuthService registration functionality."""
    
    def test_successful_registration(self, app_context):
        """Test successful user and tenant registration."""
        result = AuthService.register(
            email='<EMAIL>',
            password='newpassword123',
            first_name='New',
            last_name='User',
            tenant_name='New Business',
            business_type='retail'
        )
        
        assert result['success'] is True
        assert result['user']['email'] == '<EMAIL>'
        assert result['user']['full_name'] == 'New User'
        assert result['user']['role'] == 'admin'  # First user is admin
        assert result['tenant']['name'] == 'New Business'
        assert result['tenant']['business_type'] == 'retail'
        assert 'verification_token' in result
        
        # Verify user and tenant were created in database
        user = User.query.filter_by(email='<EMAIL>').first()
        assert user is not None
        assert user.role == 'admin'
        
        tenant = Tenant.query.filter_by(name='New Business').first()
        assert tenant is not None
        assert tenant.business_type == 'retail'
    
    def test_registration_with_existing_email(self, app_context, sample_user):
        """Test registration with already existing email."""
        result = AuthService.register(
            email='<EMAIL>',  # Already exists
            password='password123',
            first_name='Another',
            last_name='User',
            tenant_name='Another Business',
            business_type='restaurant'
        )
        
        assert result['success'] is False
        assert 'already registered' in result['error']
    
    @patch('app.services.auth_service.db.session.rollback')
    def test_registration_database_error(self, mock_rollback, app_context):
        """Test registration with database error."""
        with patch('app.models.user.Tenant.save', side_effect=Exception('Database error')):
            result = AuthService.register(
                email='<EMAIL>',
                password='password123',
                first_name='Error',
                last_name='User',
                tenant_name='Error Business',
                business_type='retail'
            )
            
            assert result['success'] is False
            assert 'error occurred during registration' in result['error']
            mock_rollback.assert_called_once()


class TestAuthServiceEmailVerification:
    """Test cases for AuthService email verification."""
    
    def test_successful_email_verification(self, app_context, sample_user):
        """Test successful email verification."""
        # Set verification token
        sample_user.email_verification_token = 'test_token_123'
        sample_user.save()
        
        result = AuthService.verify_email('test_token_123')
        
        assert result['success'] is True
        assert result['message'] == 'Email verified successfully'
        
        # Verify user email is marked as verified
        user = User.query.get(sample_user.id)
        assert user.email_verified is True
        assert user.email_verification_token is None
    
    def test_email_verification_invalid_token(self, app_context):
        """Test email verification with invalid token."""
        result = AuthService.verify_email('invalid_token')
        
        assert result['success'] is False
        assert 'Invalid verification token' in result['error']


class TestAuthServicePasswordReset:
    """Test cases for AuthService password reset functionality."""
    
    def test_password_reset_request(self, app_context, sample_user, sample_tenant):
        """Test password reset request."""
        result = AuthService.request_password_reset(
            email='<EMAIL>',
            tenant_id=sample_tenant.id
        )
        
        assert result['success'] is True
        assert 'reset_token' in result
        
        # Verify token was set in database
        user = User.query.get(sample_user.id)
        assert user.password_reset_token is not None
        assert user.password_reset_expires is not None
    
    def test_password_reset_request_nonexistent_user(self, app_context, sample_tenant):
        """Test password reset request for non-existent user."""
        result = AuthService.request_password_reset(
            email='<EMAIL>',
            tenant_id=sample_tenant.id
        )
        
        # Should still return success for security (don't reveal if email exists)
        assert result['success'] is True
        assert 'If the email exists' in result['message']
    
    def test_successful_password_reset(self, app_context, sample_user):
        """Test successful password reset."""
        # Generate reset token
        token = sample_user.generate_password_reset_token()
        original_hash = sample_user.password_hash
        
        result = AuthService.reset_password(token, 'newpassword123')
        
        assert result['success'] is True
        assert result['message'] == 'Password reset successfully'
        
        # Verify password was changed
        user = User.query.get(sample_user.id)
        assert user.password_hash != original_hash
        assert user.check_password('newpassword123') is True
        assert user.password_reset_token is None
    
    def test_password_reset_invalid_token(self, app_context):
        """Test password reset with invalid token."""
        result = AuthService.reset_password('invalid_token', 'newpassword123')
        
        assert result['success'] is False
        assert 'Invalid or expired reset token' in result['error']


class TestAuthServicePasswordChange:
    """Test cases for AuthService password change functionality."""
    
    @patch('app.services.auth_service.current_user')
    def test_successful_password_change(self, mock_current_user, app_context, sample_user):
        """Test successful password change."""
        mock_current_user.is_authenticated = True
        mock_current_user.check_password.return_value = True
        mock_current_user.set_password = MagicMock()
        mock_current_user.save = MagicMock()
        
        result = AuthService.change_password('oldpassword', 'newpassword123')
        
        assert result['success'] is True
        assert result['message'] == 'Password changed successfully'
        mock_current_user.set_password.assert_called_once_with('newpassword123')
        mock_current_user.save.assert_called_once()
    
    @patch('app.services.auth_service.current_user')
    def test_password_change_unauthenticated(self, mock_current_user, app_context):
        """Test password change when not authenticated."""
        mock_current_user.is_authenticated = False
        
        result = AuthService.change_password('oldpassword', 'newpassword123')
        
        assert result['success'] is False
        assert 'not authenticated' in result['error']
    
    @patch('app.services.auth_service.current_user')
    def test_password_change_incorrect_current_password(self, mock_current_user, app_context):
        """Test password change with incorrect current password."""
        mock_current_user.is_authenticated = True
        mock_current_user.check_password = MagicMock(return_value=False)
        
        result = AuthService.change_password('wrongpassword', 'newpassword123')
        
        assert result['success'] is False
        assert 'Current password is incorrect' in result['error']


class TestAuthServiceAccountUnlock:
    """Test cases for AuthService account unlock functionality."""
    
    @patch('app.services.auth_service.current_user')
    def test_successful_account_unlock(self, mock_current_user, app_context, locked_user, sample_tenant):
        """Test successful account unlock by admin."""
        mock_current_user.is_authenticated = True
        mock_current_user.is_admin.return_value = True
        mock_current_user.tenant_id = sample_tenant.id
        
        result = AuthService.unlock_account(locked_user.id)
        
        assert result['success'] is True
        assert result['message'] == 'Account unlocked successfully'
        
        # Verify account was unlocked
        user = User.query.get(locked_user.id)
        assert user.failed_login_attempts == 0
        assert user.locked_until is None
    
    @patch('app.services.auth_service.current_user')
    def test_account_unlock_insufficient_permissions(self, mock_current_user, app_context, locked_user):
        """Test account unlock with insufficient permissions."""
        mock_current_user.is_authenticated = True
        mock_current_user.is_admin = MagicMock(return_value=False)
        
        result = AuthService.unlock_account(locked_user.id)
        
        assert result['success'] is False
        assert 'Insufficient permissions' in result['error']
    
    @patch('app.services.auth_service.current_user')
    def test_account_unlock_user_not_found(self, mock_current_user, app_context, sample_tenant):
        """Test account unlock for non-existent user."""
        mock_current_user.is_authenticated = True
        mock_current_user.is_admin.return_value = True
        mock_current_user.tenant_id = sample_tenant.id
        
        result = AuthService.unlock_account(99999)  # Non-existent user ID
        
        assert result['success'] is False
        assert 'User not found' in result['error']


class TestAuthServiceUserInfo:
    """Test cases for AuthService user info functionality."""
    
    @patch('app.services.auth_service.current_user')
    def test_get_current_user_info_authenticated(self, mock_current_user, app_context):
        """Test getting current user info when authenticated."""
        mock_current_user.is_authenticated = True
        mock_current_user.id = 1
        mock_current_user.email = '<EMAIL>'
        mock_current_user.get_full_name = MagicMock(return_value='Test User')
        mock_current_user.role = 'manager'
        mock_current_user.tenant_id = 1
        mock_current_user.can_access_reports = MagicMock(return_value=True)
        mock_current_user.can_manage_inventory = MagicMock(return_value=True)
        mock_current_user.can_process_transactions = MagicMock(return_value=True)
        mock_current_user.is_admin = MagicMock(return_value=False)
        mock_current_user.is_manager = MagicMock(return_value=True)
        
        result = AuthService.get_current_user_info()
        
        assert result is not None
        assert result['id'] == 1
        assert result['email'] == '<EMAIL>'
        assert result['full_name'] == 'Test User'
        assert result['role'] == 'manager'
        assert result['tenant_id'] == 1
        assert result['permissions']['can_access_reports'] is True
        assert result['permissions']['is_admin'] is False
        assert result['permissions']['is_manager'] is True
    
    @patch('app.services.auth_service.current_user')
    def test_get_current_user_info_unauthenticated(self, mock_current_user, app_context):
        """Test getting current user info when not authenticated."""
        mock_current_user.is_authenticated = False
        
        result = AuthService.get_current_user_info()
        
        assert result is None


class TestAuthServiceCaching:
    """Test cases for AuthService caching functionality."""
    
    @patch('app.services.auth_service.cache')
    def test_cache_user_permissions(self, mock_cache, app_context, sample_user):
        """Test caching user permissions."""
        AuthService._cache_user_permissions(sample_user)
        
        mock_cache.set.assert_called_once()
        call_args = mock_cache.set.call_args
        assert call_args[0][0] == f"user_permissions_{sample_user.id}"
        assert isinstance(call_args[0][1], dict)
        assert 'can_access_reports' in call_args[0][1]
        assert call_args[1]['timeout'] == 3600
    
    @patch('app.services.auth_service.cache')
    def test_clear_user_cache(self, mock_cache, app_context):
        """Test clearing user cache."""
        user_id = 123
        AuthService._clear_user_cache(user_id)
        
        mock_cache.delete.assert_called_once_with(f"user_permissions_{user_id}")
    
    @patch('app.services.auth_service.cache')
    def test_get_cached_permissions(self, mock_cache, app_context):
        """Test getting cached permissions."""
        user_id = 123
        expected_permissions = {'can_access_reports': True}
        mock_cache.get.return_value = expected_permissions
        
        result = AuthService.get_cached_permissions(user_id)
        
        assert result == expected_permissions
        mock_cache.get.assert_called_once_with(f"user_permissions_{user_id}")
    
    @patch('app.services.auth_service.cache')
    def test_cache_error_handling(self, mock_cache, app_context, sample_user):
        """Test cache error handling."""
        mock_cache.set.side_effect = Exception('Cache error')
        
        # Should not raise exception
        AuthService._cache_user_permissions(sample_user)
        
        mock_cache.delete.side_effect = Exception('Cache error')
        AuthService._clear_user_cache(123)
        
        mock_cache.get.side_effect = Exception('Cache error')
        result = AuthService.get_cached_permissions(123)
        assert result is None