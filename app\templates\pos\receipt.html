{% extends "base.html" %}

{% block title %}Receipt - {{ receipt.transaction_number }}{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-100 py-8">
    <div class="max-w-2xl mx-auto px-4">
        <!-- Receipt Container -->
        <div class="bg-white rounded-lg shadow-lg p-8 mb-6">
            <!-- Header -->
            <div class="text-center mb-8">
                <h1 class="text-3xl font-bold text-gray-900 mb-2">{{ receipt.tenant.name }}</h1>
                {% if receipt.tenant.address %}
                <p class="text-gray-600">{{ receipt.tenant.address }}</p>
                {% endif %}
                {% if receipt.tenant.phone %}
                <p class="text-gray-600">Phone: {{ receipt.tenant.phone }}</p>
                {% endif %}
                {% if receipt.tenant.email %}
                <p class="text-gray-600">Email: {{ receipt.tenant.email }}</p>
                {% endif %}
            </div>

            <!-- Receipt Info -->
            <div class="border-t border-b border-gray-200 py-4 mb-6">
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <p class="text-sm text-gray-600">Receipt Number</p>
                        <p class="font-semibold">{{ receipt.receipt_number }}</p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-600">Transaction Number</p>
                        <p class="font-semibold">{{ receipt.transaction_number }}</p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-600">Date & Time</p>
                        <p class="font-semibold">{{ receipt.date }}</p>
                    </div>
                    {% if receipt.customer.name %}
                    <div>
                        <p class="text-sm text-gray-600">Customer</p>
                        <p class="font-semibold">{{ receipt.customer.name }}</p>
                        {% if receipt.customer.email %}
                        <p class="text-xs text-gray-500">{{ receipt.customer.email }}</p>
                        {% endif %}
                        {% if receipt.customer.phone %}
                        <p class="text-xs text-gray-500">{{ receipt.customer.phone }}</p>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Items -->
            <div class="mb-6">
                <h2 class="text-lg font-semibold mb-4">Items</h2>
                <div class="space-y-3">
                    {% for item in receipt.items %}
                    <div class="flex justify-between items-start py-2 border-b border-gray-100">
                        <div class="flex-1">
                            <h3 class="font-medium">{{ item.name }}</h3>
                            {% if item.sku %}
                            <p class="text-sm text-gray-500">SKU: {{ item.sku }}</p>
                            {% endif %}
                            <p class="text-sm text-gray-600">{{ item.quantity }} × ${{ "%.2f"|format(item.unit_price) }}</p>
                        </div>
                        <div class="text-right">
                            <p class="font-semibold">${{ "%.2f"|format(item.line_total) }}</p>
                            {% if item.discount > 0 %}
                            <p class="text-sm text-green-600">Discount: -${{ "%.2f"|format(item.discount) }}</p>
                            {% endif %}
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>

            <!-- Totals -->
            <div class="border-t border-gray-200 pt-4 mb-6">
                <div class="space-y-2">
                    <div class="flex justify-between">
                        <span class="text-gray-600">Subtotal:</span>
                        <span>${{ "%.2f"|format(receipt.totals.subtotal) }}</span>
                    </div>
                    {% if receipt.totals.discount_amount > 0 %}
                    <div class="flex justify-between text-green-600">
                        <span>Total Discount:</span>
                        <span>-${{ "%.2f"|format(receipt.totals.discount_amount) }}</span>
                    </div>
                    {% endif %}
                    <div class="flex justify-between">
                        <span class="text-gray-600">Tax:</span>
                        <span>${{ "%.2f"|format(receipt.totals.tax_amount) }}</span>
                    </div>
                    <div class="flex justify-between font-bold text-lg border-t pt-2">
                        <span>Total:</span>
                        <span>${{ "%.2f"|format(receipt.totals.total_amount) }}</span>
                    </div>
                </div>
            </div>

            <!-- Payment Info -->
            <div class="border-t border-gray-200 pt-4 mb-6">
                <div class="space-y-2">
                    <div class="flex justify-between">
                        <span class="text-gray-600">Payment Method:</span>
                        <span class="capitalize">{{ receipt.payment.method.replace('_', ' ') }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">Amount Paid:</span>
                        <span>${{ "%.2f"|format(receipt.totals.amount_paid) }}</span>
                    </div>
                    {% if receipt.totals.change_given > 0 %}
                    <div class="flex justify-between font-semibold">
                        <span>Change Given:</span>
                        <span>${{ "%.2f"|format(receipt.totals.change_given) }}</span>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Status -->
            <div class="text-center mb-6">
                <span class="inline-block bg-green-100 text-green-800 px-4 py-2 rounded-full font-semibold">
                    {{ receipt.payment.status.replace('_', ' ').title() }}
                </span>
            </div>

            <!-- Notes -->
            {% if receipt.notes %}
            <div class="border-t border-gray-200 pt-4 mb-6">
                <h3 class="font-semibold mb-2">Notes:</h3>
                <p class="text-gray-600">{{ receipt.notes }}</p>
            </div>
            {% endif %}

            <!-- Footer -->
            <div class="text-center text-gray-500 text-sm border-t border-gray-200 pt-4">
                <p class="mb-2">Thank you for your business!</p>
                <p>Please keep this receipt for your records.</p>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex justify-center space-x-4">
            <button onclick="window.print()" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold">
                Print Receipt
            </button>
            <a href="{{ url_for('pos.dashboard') }}" class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-3 rounded-lg font-semibold">
                Back to POS
            </a>
        </div>
    </div>
</div>

<style>
@media print {
    body { 
        background: white !important; 
        -webkit-print-color-adjust: exact;
    }
    .bg-gray-100 { background: white !important; }
    .shadow-lg { box-shadow: none !important; }
    .rounded-lg { border-radius: 0 !important; }
    .mb-6:last-child { display: none; } /* Hide action buttons when printing */
}
</style>
{% endblock %}