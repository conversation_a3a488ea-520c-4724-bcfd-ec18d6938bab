"""User and Tenant models for SaaS POS System."""

from datetime import datetime
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from app import db
from app.models.base import BaseModel


class Tenant(BaseModel):
    """Tenant model for multi-tenancy support."""
    
    __tablename__ = 'tenant'
    
    # Basic tenant information
    name = db.Column(db.String(100), nullable=False)
    business_type = db.Column(db.String(50), nullable=False, default='retail')
    subscription_status = db.Column(db.String(20), nullable=False, default='active')
    
    # Contact information
    email = db.Column(db.String(120), nullable=True)
    phone = db.Column(db.String(20), nullable=True)
    address = db.Column(db.Text, nullable=True)
    
    # Subscription details
    subscription_plan = db.Column(db.String(50), nullable=False, default='basic')
    subscription_expires = db.Column(db.DateTime, nullable=True)
    
    # Settings
    timezone = db.Column(db.String(50), nullable=False, default='UTC')
    currency = db.Column(db.String(3), nullable=False, default='USD')
    
    # Relationships
    users = db.relationship('User', backref='tenant', lazy=True, cascade='all, delete-orphan')
    
    # Indexes for optimization
    __table_args__ = (
        db.Index('idx_tenant_subscription_status', 'subscription_status'),
        db.Index('idx_tenant_business_type', 'business_type'),
        db.Index('idx_tenant_subscription_expires', 'subscription_expires'),
        db.Index('idx_tenant_name', 'name'),
    )
    
    def __repr__(self):
        return f'<Tenant {self.name}>'
    
    def is_active(self):
        """Check if tenant subscription is active."""
        return self.subscription_status == 'active'
    
    def is_subscription_expired(self):
        """Check if subscription has expired."""
        if not self.subscription_expires:
            return False
        return datetime.utcnow() > self.subscription_expires
    
    def get_active_users_count(self):
        """Get count of active users for this tenant."""
        return User.query.filter_by(tenant_id=self.id, is_active=True).count()


class User(UserMixin, BaseModel):
    """User model with authentication and tenant relationship."""
    
    __tablename__ = 'user'
    
    # Basic user information
    email = db.Column(db.String(120), nullable=False, index=True)
    password_hash = db.Column(db.String(255), nullable=False)
    first_name = db.Column(db.String(50), nullable=False)
    last_name = db.Column(db.String(50), nullable=False)
    
    # User status and role
    is_active = db.Column(db.Boolean, nullable=False, default=True)
    role = db.Column(db.String(50), nullable=False, default='user')
    
    # Tenant relationship
    tenant_id = db.Column(db.Integer, db.ForeignKey('tenant.id'), nullable=False, index=True)
    
    # Authentication tracking
    last_login = db.Column(db.DateTime, nullable=True)
    login_count = db.Column(db.Integer, nullable=False, default=0)
    failed_login_attempts = db.Column(db.Integer, nullable=False, default=0)
    locked_until = db.Column(db.DateTime, nullable=True)
    
    # Email verification
    email_verified = db.Column(db.Boolean, nullable=False, default=False)
    email_verification_token = db.Column(db.String(100), nullable=True)
    
    # Password reset
    password_reset_token = db.Column(db.String(100), nullable=True)
    password_reset_expires = db.Column(db.DateTime, nullable=True)
    
    # Indexes and constraints for optimization
    __table_args__ = (
        db.UniqueConstraint('email', 'tenant_id', name='unique_email_per_tenant'),
        db.Index('idx_user_tenant_active', 'tenant_id', 'is_active'),
        db.Index('idx_user_tenant_role', 'tenant_id', 'role'),
        db.Index('idx_user_email_tenant', 'email', 'tenant_id'),
        db.Index('idx_user_last_login', 'last_login'),
        db.Index('idx_user_locked_until', 'locked_until'),
        db.Index('idx_user_email_verification', 'email_verification_token'),
        db.Index('idx_user_password_reset', 'password_reset_token'),
    )
    
    def __repr__(self):
        return f'<User {self.email}>'
    
    def set_password(self, password):
        """Set password hash."""
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        """Check password against hash."""
        return check_password_hash(self.password_hash, password)
    
    def get_full_name(self):
        """Get user's full name."""
        return f"{self.first_name} {self.last_name}"
    
    def is_admin(self):
        """Check if user has admin role."""
        return self.role == 'admin'
    
    def is_manager(self):
        """Check if user has manager role."""
        return self.role in ['admin', 'manager']
    
    def can_access_reports(self):
        """Check if user can access reports."""
        return self.role in ['admin', 'manager']
    
    def can_manage_inventory(self):
        """Check if user can manage inventory."""
        return self.role in ['admin', 'manager']
    
    def can_process_transactions(self):
        """Check if user can process transactions."""
        return self.role in ['admin', 'manager', 'cashier']
    
    def is_account_locked(self):
        """Check if account is locked due to failed login attempts."""
        if not self.locked_until:
            return False
        return datetime.utcnow() < self.locked_until
    
    def record_login_attempt(self, success=True):
        """Record login attempt."""
        if success:
            self.last_login = datetime.utcnow()
            self.login_count += 1
            self.failed_login_attempts = 0
            self.locked_until = None
        else:
            self.failed_login_attempts += 1
            # Lock account after 5 failed attempts for 30 minutes
            if self.failed_login_attempts >= 5:
                from datetime import timedelta
                self.locked_until = datetime.utcnow() + timedelta(minutes=30)
        
        self.save()
    
    def unlock_account(self):
        """Unlock user account."""
        self.failed_login_attempts = 0
        self.locked_until = None
        self.save()
    
    def verify_email(self):
        """Mark email as verified."""
        self.email_verified = True
        self.email_verification_token = None
        self.save()
    
    def generate_password_reset_token(self):
        """Generate password reset token."""
        import secrets
        from datetime import timedelta
        
        self.password_reset_token = secrets.token_urlsafe(32)
        self.password_reset_expires = datetime.utcnow() + timedelta(hours=1)
        self.save()
        return self.password_reset_token
    
    def reset_password(self, new_password, token):
        """Reset password using token."""
        if not self.password_reset_token or self.password_reset_token != token:
            return False
        
        if not self.password_reset_expires or datetime.utcnow() > self.password_reset_expires:
            return False
        
        self.set_password(new_password)
        self.password_reset_token = None
        self.password_reset_expires = None
        self.save()
        return True
    
    @classmethod
    def create_user(cls, email, password, first_name, last_name, tenant_id, role='user'):
        """Create a new user with hashed password."""
        user = cls(
            email=email,
            first_name=first_name,
            last_name=last_name,
            tenant_id=tenant_id,
            role=role
        )
        user.set_password(password)
        return user.save()
    
    @classmethod
    def authenticate(cls, email, password, tenant_id):
        """Authenticate user with email and password."""
        user = cls.query.filter_by(email=email, tenant_id=tenant_id).first()
        
        if not user or not user.is_active:
            return None
        
        if user.is_account_locked():
            return None
        
        if user.check_password(password):
            user.record_login_attempt(success=True)
            return user
        else:
            user.record_login_attempt(success=False)
            return None