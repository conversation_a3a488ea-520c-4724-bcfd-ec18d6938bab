"""Cache management utilities for improved performance."""

from functools import wraps
from flask import current_app, g
from flask_caching import Cache
import json
import hashlib
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)

# Initialize cache (will be configured in app factory)
cache = Cache()


class CacheManager:
    """Centralized cache management."""
    
    # Cache key prefixes
    TENANT_PREFIX = "tenant"
    USER_PREFIX = "user"
    PRODUCT_PREFIX = "product"
    CATEGORY_PREFIX = "category"
    TRANSACTION_PREFIX = "transaction"
    REPORT_PREFIX = "report"
    
    # Cache timeouts (in seconds)
    SHORT_TIMEOUT = 300      # 5 minutes
    MEDIUM_TIMEOUT = 1800    # 30 minutes
    LONG_TIMEOUT = 3600      # 1 hour
    DAILY_TIMEOUT = 86400    # 24 hours
    
    @staticmethod
    def generate_cache_key(prefix, tenant_id, *args, **kwargs):
        """Generate a consistent cache key."""
        key_parts = [prefix, str(tenant_id)]
        
        # Add positional arguments
        for arg in args:
            key_parts.append(str(arg))
        
        # Add keyword arguments (sorted for consistency)
        for k, v in sorted(kwargs.items()):
            key_parts.append(f"{k}:{v}")
        
        key = ":".join(key_parts)
        
        # Hash long keys to avoid Redis key length limits
        if len(key) > 200:
            key_hash = hashlib.md5(key.encode()).hexdigest()
            key = f"{prefix}:hash:{key_hash}"
        
        return key
    
    @staticmethod
    def get_tenant_cache_key(tenant_id, key_suffix):
        """Get cache key for tenant-specific data."""
        return CacheManager.generate_cache_key(
            CacheManager.TENANT_PREFIX, 
            tenant_id, 
            key_suffix
        )
    
    @staticmethod
    def invalidate_tenant_cache(tenant_id, pattern=None):
        """Invalidate all cache entries for a tenant."""
        if pattern:
            cache_key = CacheManager.generate_cache_key(
                CacheManager.TENANT_PREFIX, 
                tenant_id, 
                pattern
            )
            cache.delete(cache_key)
        else:
            # Invalidate all tenant cache (requires Redis)
            try:
                pattern = f"{CacheManager.TENANT_PREFIX}:{tenant_id}:*"
                cache.delete_many(*cache.cache._read_clients.keys(pattern))
            except Exception as e:
                logger.warning(f"Could not invalidate tenant cache pattern: {e}")
    
    @staticmethod
    def cache_product_data(tenant_id, product_id, data, timeout=MEDIUM_TIMEOUT):
        """Cache product data."""
        cache_key = CacheManager.generate_cache_key(
            CacheManager.PRODUCT_PREFIX, 
            tenant_id, 
            product_id
        )
        cache.set(cache_key, data, timeout=timeout)
    
    @staticmethod
    def get_cached_product_data(tenant_id, product_id):
        """Get cached product data."""
        cache_key = CacheManager.generate_cache_key(
            CacheManager.PRODUCT_PREFIX, 
            tenant_id, 
            product_id
        )
        return cache.get(cache_key)
    
    @staticmethod
    def cache_report_data(tenant_id, report_type, filters, data, timeout=SHORT_TIMEOUT):
        """Cache report data."""
        cache_key = CacheManager.generate_cache_key(
            CacheManager.REPORT_PREFIX, 
            tenant_id, 
            report_type,
            **filters
        )
        cache.set(cache_key, data, timeout=timeout)
    
    @staticmethod
    def get_cached_report_data(tenant_id, report_type, filters):
        """Get cached report data."""
        cache_key = CacheManager.generate_cache_key(
            CacheManager.REPORT_PREFIX, 
            tenant_id, 
            report_type,
            **filters
        )
        return cache.get(cache_key)
    
    @staticmethod
    def invalidate_product_cache(tenant_id, product_id=None):
        """Invalidate product cache."""
        if product_id:
            cache_key = CacheManager.generate_cache_key(
                CacheManager.PRODUCT_PREFIX, 
                tenant_id, 
                product_id
            )
            cache.delete(cache_key)
        else:
            # Invalidate all product cache for tenant
            CacheManager.invalidate_tenant_cache(tenant_id, f"{CacheManager.PRODUCT_PREFIX}:*")


def cached_query(timeout=CacheManager.MEDIUM_TIMEOUT, key_prefix="query"):
    """Decorator to cache query results."""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Generate cache key from function name and arguments
            tenant_id = getattr(g, 'current_tenant_id', 'global')
            cache_key = CacheManager.generate_cache_key(
                key_prefix,
                tenant_id,
                func.__name__,
                *args,
                **kwargs
            )
            
            # Try to get from cache
            result = cache.get(cache_key)
            if result is not None:
                return result
            
            # Execute function and cache result
            result = func(*args, **kwargs)
            cache.set(cache_key, result, timeout=timeout)
            return result
        
        return wrapper
    return decorator


def cached_property_method(timeout=CacheManager.MEDIUM_TIMEOUT):
    """Decorator to cache model property methods."""
    def decorator(func):
        @wraps(func)
        def wrapper(self, *args, **kwargs):
            # Generate cache key from model and method
            tenant_id = getattr(self, 'tenant_id', 'global')
            model_id = getattr(self, 'id', 'new')
            
            cache_key = CacheManager.generate_cache_key(
                "property",
                tenant_id,
                self.__class__.__name__,
                model_id,
                func.__name__,
                *args,
                **kwargs
            )
            
            # Try to get from cache
            result = cache.get(cache_key)
            if result is not None:
                return result
            
            # Execute method and cache result
            result = func(self, *args, **kwargs)
            cache.set(cache_key, result, timeout=timeout)
            return result
        
        return wrapper
    return decorator


class ReportCache:
    """Specialized caching for reports and analytics."""
    
    @staticmethod
    def get_daily_sales_cache_key(tenant_id, date):
        """Get cache key for daily sales data."""
        return f"daily_sales:{tenant_id}:{date.strftime('%Y-%m-%d')}"
    
    @staticmethod
    def cache_daily_sales(tenant_id, date, data):
        """Cache daily sales data."""
        cache_key = ReportCache.get_daily_sales_cache_key(tenant_id, date)
        
        # Cache completed days for longer
        if date.date() < datetime.now().date():
            timeout = CacheManager.DAILY_TIMEOUT
        else:
            timeout = CacheManager.SHORT_TIMEOUT
        
        cache.set(cache_key, data, timeout=timeout)
    
    @staticmethod
    def get_cached_daily_sales(tenant_id, date):
        """Get cached daily sales data."""
        cache_key = ReportCache.get_daily_sales_cache_key(tenant_id, date)
        return cache.get(cache_key)
    
    @staticmethod
    def get_product_performance_cache_key(tenant_id, start_date, end_date):
        """Get cache key for product performance data."""
        return f"product_performance:{tenant_id}:{start_date}:{end_date}"
    
    @staticmethod
    def cache_product_performance(tenant_id, start_date, end_date, data):
        """Cache product performance data."""
        cache_key = ReportCache.get_product_performance_cache_key(tenant_id, start_date, end_date)
        cache.set(cache_key, data, timeout=CacheManager.MEDIUM_TIMEOUT)
    
    @staticmethod
    def get_cached_product_performance(tenant_id, start_date, end_date):
        """Get cached product performance data."""
        cache_key = ReportCache.get_product_performance_cache_key(tenant_id, start_date, end_date)
        return cache.get(cache_key)
    
    @staticmethod
    def invalidate_sales_cache(tenant_id, date=None):
        """Invalidate sales cache for a specific date or all dates."""
        if date:
            cache_key = ReportCache.get_daily_sales_cache_key(tenant_id, date)
            cache.delete(cache_key)
        else:
            # Invalidate all sales cache for tenant
            try:
                pattern = f"daily_sales:{tenant_id}:*"
                cache.delete_many(*cache.cache._read_clients.keys(pattern))
            except Exception as e:
                logger.warning(f"Could not invalidate sales cache pattern: {e}")


def init_cache(app):
    """Initialize cache with app."""
    cache.init_app(app)
    return cache