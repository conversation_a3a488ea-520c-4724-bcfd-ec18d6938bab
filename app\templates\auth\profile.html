{% extends "base.html" %}

{% block title %}Profile - SaaS POS System{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-3xl mx-auto">
        <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg leading-6 font-medium text-gray-900">
                        User Profile
                    </h3>
                    <a href="{{ url_for('main.dashboard') }}" 
                       class="text-indigo-600 hover:text-indigo-500 text-sm font-medium">
                        Back to Dashboard
                    </a>
                </div>
                
                {% if user %}
                <div class="mt-6 border-t border-gray-200 pt-6">
                    <dl class="divide-y divide-gray-200">
                        <div class="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4">
                            <dt class="text-sm font-medium text-gray-500">Full name</dt>
                            <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ user.full_name }}</dd>
                        </div>
                        
                        <div class="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4">
                            <dt class="text-sm font-medium text-gray-500">Email address</dt>
                            <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ user.email }}</dd>
                        </div>
                        
                        <div class="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4">
                            <dt class="text-sm font-medium text-gray-500">Role</dt>
                            <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                           {% if user.role == 'admin' %}bg-purple-100 text-purple-800
                                           {% elif user.role == 'manager' %}bg-blue-100 text-blue-800
                                           {% elif user.role == 'cashier' %}bg-green-100 text-green-800
                                           {% else %}bg-gray-100 text-gray-800{% endif %}">
                                    {{ user.role.title() }}
                                </span>
                            </dd>
                        </div>
                        
                        <div class="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4">
                            <dt class="text-sm font-medium text-gray-500">Tenant ID</dt>
                            <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ user.tenant_id }}</dd>
                        </div>
                        
                        <div class="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4">
                            <dt class="text-sm font-medium text-gray-500">Permissions</dt>
                            <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                                <div class="space-y-2">
                                    {% if user.permissions.can_process_transactions %}
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            Process Transactions
                                        </span>
                                    {% endif %}
                                    
                                    {% if user.permissions.can_manage_inventory %}
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                            Manage Inventory
                                        </span>
                                    {% endif %}
                                    
                                    {% if user.permissions.can_access_reports %}
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                            Access Reports
                                        </span>
                                    {% endif %}
                                    
                                    {% if user.permissions.is_admin %}
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                            Administrator
                                        </span>
                                    {% endif %}
                                </div>
                            </dd>
                        </div>
                    </dl>
                </div>
                
                <div class="mt-6 border-t border-gray-200 pt-6">
                    <div class="flex justify-end space-x-3">
                        <a href="{{ url_for('auth.change_password') }}" 
                           class="bg-indigo-600 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            Change Password
                        </a>
                    </div>
                </div>
                {% else %}
                <div class="mt-6 text-center">
                    <p class="text-sm text-gray-500">Unable to load user information.</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}