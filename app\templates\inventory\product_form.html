{% extends "base.html" %}

{% block title %}{% if product %}Edit Product{% else %}Add Product{% endif %} - SaaS POS System{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <!-- Header -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-3xl font-bold text-gray-900">
                {% if product %}Edit Product{% else %}Add Product{% endif %}
            </h1>
            <p class="text-gray-600 mt-1">
                {% if product %}Update product information and settings{% else %}Create a new product for your inventory{% endif %}
            </p>
        </div>
        <div class="flex space-x-4">
            <a href="{{ url_for('inventory.products') }}" 
               class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg font-medium">
                Back to Products
            </a>
        </div>
    </div>

    <!-- Flash Messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="mb-4 p-4 rounded-lg {% if category == 'error' %}bg-red-100 text-red-700 border border-red-300{% else %}bg-green-100 text-green-700 border border-green-300{% endif %}">
                    {{ message }}
                </div>
            {% endfor %}
        {% endif %}
    {% endwith %}

    <!-- Product Form -->
    <form method="POST" class="space-y-6">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Main Product Information -->
            <div class="lg:col-span-2">
                <div class="bg-white shadow rounded-lg p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Product Information</h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <!-- Product Name -->
                        <div class="md:col-span-2">
                            <label for="name" class="block text-sm font-medium text-gray-700 mb-1">
                                Product Name <span class="text-red-500">*</span>
                            </label>
                            <input type="text" name="name" id="name" required
                                   value="{{ product.name if product else '' }}"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                   placeholder="Enter product name">
                        </div>

                        <!-- Description -->
                        <div class="md:col-span-2">
                            <label for="description" class="block text-sm font-medium text-gray-700 mb-1">
                                Description
                            </label>
                            <textarea name="description" id="description" rows="3"
                                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                      placeholder="Enter product description">{{ product.description if product else '' }}</textarea>
                        </div>

                        <!-- SKU -->
                        <div>
                            <label for="sku" class="block text-sm font-medium text-gray-700 mb-1">
                                SKU
                            </label>
                            <input type="text" name="sku" id="sku"
                                   value="{{ product.sku if product else '' }}"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                   placeholder="Enter SKU">
                        </div>

                        <!-- Barcode -->
                        <div>
                            <label for="barcode" class="block text-sm font-medium text-gray-700 mb-1">
                                Barcode
                            </label>
                            <input type="text" name="barcode" id="barcode"
                                   value="{{ product.barcode if product else '' }}"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                   placeholder="Enter barcode">
                        </div>

                        <!-- Category -->
                        <div>
                            <label for="category_id" class="block text-sm font-medium text-gray-700 mb-1">
                                Category
                            </label>
                            <select name="category_id" id="category_id"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                <option value="">Select Category</option>
                                {% for category in categories %}
                                    <option value="{{ category.id }}" 
                                            {% if product and product.category_id == category.id %}selected{% endif %}>
                                        {{ category.name }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>

                        <!-- Unit of Measure -->
                        <div>
                            <label for="unit_of_measure" class="block text-sm font-medium text-gray-700 mb-1">
                                Unit of Measure
                            </label>
                            <select name="unit_of_measure" id="unit_of_measure"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                <option value="each" {% if not product or product.unit_of_measure == 'each' %}selected{% endif %}>Each</option>
                                <option value="kg" {% if product and product.unit_of_measure == 'kg' %}selected{% endif %}>Kilogram</option>
                                <option value="g" {% if product and product.unit_of_measure == 'g' %}selected{% endif %}>Gram</option>
                                <option value="lb" {% if product and product.unit_of_measure == 'lb' %}selected{% endif %}>Pound</option>
                                <option value="oz" {% if product and product.unit_of_measure == 'oz' %}selected{% endif %}>Ounce</option>
                                <option value="l" {% if product and product.unit_of_measure == 'l' %}selected{% endif %}>Liter</option>
                                <option value="ml" {% if product and product.unit_of_measure == 'ml' %}selected{% endif %}>Milliliter</option>
                                <option value="m" {% if product and product.unit_of_measure == 'm' %}selected{% endif %}>Meter</option>
                                <option value="cm" {% if product and product.unit_of_measure == 'cm' %}selected{% endif %}>Centimeter</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Pricing Information -->
                <div class="bg-white shadow rounded-lg p-6 mt-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Pricing</h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <!-- Cost Price -->
                        <div>
                            <label for="cost_price" class="block text-sm font-medium text-gray-700 mb-1">
                                Cost Price
                            </label>
                            <div class="relative">
                                <span class="absolute left-3 top-2 text-gray-500">$</span>
                                <input type="number" name="cost_price" id="cost_price" step="0.01" min="0"
                                       value="{{ product.cost_price if product else '' }}"
                                       class="w-full pl-8 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                       placeholder="0.00">
                            </div>
                        </div>

                        <!-- Selling Price -->
                        <div>
                            <label for="selling_price" class="block text-sm font-medium text-gray-700 mb-1">
                                Selling Price <span class="text-red-500">*</span>
                            </label>
                            <div class="relative">
                                <span class="absolute left-3 top-2 text-gray-500">$</span>
                                <input type="number" name="selling_price" id="selling_price" step="0.01" min="0" required
                                       value="{{ product.selling_price if product else '' }}"
                                       class="w-full pl-8 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                       placeholder="0.00">
                            </div>
                        </div>

                        <!-- Tax Rate -->
                        <div>
                            <label for="tax_rate" class="block text-sm font-medium text-gray-700 mb-1">
                                Tax Rate (%)
                            </label>
                            <input type="number" name="tax_rate" id="tax_rate" step="0.01" min="0" max="100"
                                   value="{{ product.tax_rate if product else '' }}"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                   placeholder="0.00">
                        </div>

                        <!-- Tax Inclusive -->
                        <div class="flex items-center pt-6">
                            <input type="checkbox" name="tax_inclusive" id="tax_inclusive"
                                   {% if product and product.tax_inclusive %}checked{% endif %}
                                   class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                            <label for="tax_inclusive" class="ml-2 text-sm text-gray-700">
                                Price includes tax
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Inventory Settings -->
                <div class="bg-white shadow rounded-lg p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Inventory</h3>
                    
                    <div class="space-y-4">
                        <!-- Track Inventory -->
                        <div class="flex items-center">
                            <input type="checkbox" name="track_inventory" id="track_inventory"
                                   {% if not product or product.track_inventory %}checked{% endif %}
                                   class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                            <label for="track_inventory" class="ml-2 text-sm text-gray-700">
                                Track inventory
                            </label>
                        </div>

                        <!-- Current Stock (only for new products) -->
                        {% if not product %}
                        <div>
                            <label for="current_stock" class="block text-sm font-medium text-gray-700 mb-1">
                                Initial Stock
                            </label>
                            <input type="number" name="current_stock" id="current_stock" min="0"
                                   value="0"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        {% endif %}

                        <!-- Minimum Stock -->
                        <div>
                            <label for="minimum_stock" class="block text-sm font-medium text-gray-700 mb-1">
                                Minimum Stock Level
                            </label>
                            <input type="number" name="minimum_stock" id="minimum_stock" min="0"
                                   value="{{ product.minimum_stock if product else '0' }}"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>

                        <!-- Maximum Stock -->
                        <div>
                            <label for="maximum_stock" class="block text-sm font-medium text-gray-700 mb-1">
                                Maximum Stock Level
                            </label>
                            <input type="number" name="maximum_stock" id="maximum_stock" min="0"
                                   value="{{ product.maximum_stock if product else '' }}"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>

                        <!-- Allow Negative Stock -->
                        <div class="flex items-center">
                            <input type="checkbox" name="allow_negative_stock" id="allow_negative_stock"
                                   {% if product and product.allow_negative_stock %}checked{% endif %}
                                   class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                            <label for="allow_negative_stock" class="ml-2 text-sm text-gray-700">
                                Allow negative stock
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Product Settings -->
                <div class="bg-white shadow rounded-lg p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Settings</h3>
                    
                    <div class="space-y-4">
                        <!-- Active -->
                        <div class="flex items-center">
                            <input type="checkbox" name="is_active" id="is_active"
                                   {% if not product or product.is_active %}checked{% endif %}
                                   class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                            <label for="is_active" class="ml-2 text-sm text-gray-700">
                                Active
                            </label>
                        </div>

                        <!-- Featured -->
                        <div class="flex items-center">
                            <input type="checkbox" name="is_featured" id="is_featured"
                                   {% if product and product.is_featured %}checked{% endif %}
                                   class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                            <label for="is_featured" class="ml-2 text-sm text-gray-700">
                                Featured product
                            </label>
                        </div>

                        <!-- Sort Order -->
                        <div>
                            <label for="sort_order" class="block text-sm font-medium text-gray-700 mb-1">
                                Sort Order
                            </label>
                            <input type="number" name="sort_order" id="sort_order" min="0"
                                   value="{{ product.sort_order if product else '0' }}"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>

                        <!-- Weight -->
                        <div>
                            <label for="weight" class="block text-sm font-medium text-gray-700 mb-1">
                                Weight
                            </label>
                            <input type="number" name="weight" id="weight" step="0.01" min="0"
                                   value="{{ product.weight if product else '' }}"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                   placeholder="0.00">
                        </div>

                        <!-- Color -->
                        <div>
                            <label for="color" class="block text-sm font-medium text-gray-700 mb-1">
                                Color
                            </label>
                            <input type="text" name="color" id="color"
                                   value="{{ product.color if product else '' }}"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                   placeholder="e.g., Red, Blue, #FF0000">
                        </div>

                        <!-- Image URL -->
                        <div>
                            <label for="image_url" class="block text-sm font-medium text-gray-700 mb-1">
                                Image URL
                            </label>
                            <input type="url" name="image_url" id="image_url"
                                   value="{{ product.image_url if product else '' }}"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                   placeholder="https://example.com/image.jpg">
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200">
            <a href="{{ url_for('inventory.products') }}" 
               class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-6 py-2 rounded-lg font-medium">
                Cancel
            </a>
            <button type="submit" 
                    class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium">
                {% if product %}Update Product{% else %}Create Product{% endif %}
            </button>
        </div>
    </form>
</div>

<script>
// Simple form validation and UX improvements
document.addEventListener('DOMContentLoaded', function() {
    const trackInventoryCheckbox = document.getElementById('track_inventory');
    const inventoryFields = ['current_stock', 'minimum_stock', 'maximum_stock', 'allow_negative_stock'];
    
    function toggleInventoryFields() {
        const isTracked = trackInventoryCheckbox.checked;
        inventoryFields.forEach(fieldId => {
            const field = document.getElementById(fieldId);
            if (field) {
                if (field.type === 'checkbox') {
                    field.disabled = !isTracked;
                    if (!isTracked) field.checked = false;
                } else {
                    field.disabled = !isTracked;
                    if (!isTracked) field.value = '';
                }
                field.parentElement.style.opacity = isTracked ? '1' : '0.5';
            }
        });
    }
    
    trackInventoryCheckbox.addEventListener('change', toggleInventoryFields);
    toggleInventoryFields(); // Initial state
    
    // Calculate profit margin
    const costPriceField = document.getElementById('cost_price');
    const sellingPriceField = document.getElementById('selling_price');
    
    function calculateProfitMargin() {
        const costPrice = parseFloat(costPriceField.value) || 0;
        const sellingPrice = parseFloat(sellingPriceField.value) || 0;
        
        if (costPrice > 0 && sellingPrice > 0) {
            const profit = sellingPrice - costPrice;
            const margin = (profit / sellingPrice) * 100;
            
            // You could display this somewhere if needed
            console.log(`Profit: $${profit.toFixed(2)}, Margin: ${margin.toFixed(2)}%`);
        }
    }
    
    costPriceField.addEventListener('input', calculateProfitMargin);
    sellingPriceField.addEventListener('input', calculateProfitMargin);
});
</script>
{% endblock %}