"""Business configuration models for SaaS POS System."""

from datetime import datetime
from sqlalchemy.dialects.postgresql import JSO<PERSON>
from app import db
from app.models.base import TenantAwareModel


class BusinessSettings(TenantAwareModel):
    """Business settings model for tenant-specific configurations."""
    
    __tablename__ = 'business_settings'
    
    # Business type and configuration
    business_type = db.Column(db.String(50), nullable=False, default='retail')
    business_name = db.Column(db.String(100), nullable=False)
    
    # Feature toggles
    enable_table_management = db.Column(db.<PERSON>olean, nullable=False, default=False)
    enable_kitchen_display = db.Column(db.Boolean, nullable=False, default=False)
    enable_order_tracking = db.Column(db.Boolean, nullable=False, default=False)
    enable_barcode_scanning = db.Column(db.Boolean, nullable=False, default=False)
    enable_appointment_scheduling = db.Column(db.<PERSON>, nullable=False, default=False)
    enable_service_tracking = db.Column(db.<PERSON>, nullable=False, default=False)
    enable_inventory_alerts = db.Column(db.Boolean, nullable=False, default=True)
    enable_loyalty_program = db.Column(db.Boolean, nullable=False, default=False)
    
    # Business-specific settings stored as JSON
    restaurant_settings = db.Column(JSON, nullable=True)
    retail_settings = db.Column(JSON, nullable=True)
    service_settings = db.Column(JSON, nullable=True)
    
    # Tax and pricing settings
    default_tax_rate = db.Column(db.Numeric(5, 4), nullable=False, default=0.0)
    currency = db.Column(db.String(3), nullable=False, default='USD')
    timezone = db.Column(db.String(50), nullable=False, default='UTC')
    
    # Receipt and printing settings
    receipt_header = db.Column(db.Text, nullable=True)
    receipt_footer = db.Column(db.Text, nullable=True)
    auto_print_receipts = db.Column(db.Boolean, nullable=False, default=True)
    
    # Low stock threshold
    low_stock_threshold = db.Column(db.Integer, nullable=False, default=10)
    
    def __repr__(self):
        return f'<BusinessSettings {self.business_name} ({self.business_type})>'
    
    def get_business_type_display(self):
        """Get human-readable business type."""
        business_types = {
            'restaurant': 'Restaurant',
            'retail': 'Retail Store',
            'service': 'Service Business',
            'cafe': 'Cafe',
            'bar': 'Bar/Pub',
            'food_truck': 'Food Truck',
            'grocery': 'Grocery Store',
            'pharmacy': 'Pharmacy',
            'salon': 'Salon/Spa',
            'repair': 'Repair Service'
        }
        return business_types.get(self.business_type, self.business_type.title())
    
    def is_restaurant_type(self):
        """Check if business type is restaurant-related."""
        restaurant_types = ['restaurant', 'cafe', 'bar', 'food_truck']
        return self.business_type in restaurant_types
    
    def is_retail_type(self):
        """Check if business type is retail-related."""
        retail_types = ['retail', 'grocery', 'pharmacy']
        return self.business_type in retail_types
    
    def is_service_type(self):
        """Check if business type is service-related."""
        service_types = ['service', 'salon', 'repair']
        return self.business_type in service_types
    
    def get_enabled_features(self):
        """Get list of enabled features for this business."""
        features = []
        feature_mapping = {
            'enable_table_management': 'Table Management',
            'enable_kitchen_display': 'Kitchen Display',
            'enable_order_tracking': 'Order Tracking',
            'enable_barcode_scanning': 'Barcode Scanning',
            'enable_appointment_scheduling': 'Appointment Scheduling',
            'enable_service_tracking': 'Service Tracking',
            'enable_inventory_alerts': 'Inventory Alerts',
            'enable_loyalty_program': 'Loyalty Program'
        }
        
        for feature_key, feature_name in feature_mapping.items():
            if getattr(self, feature_key, False):
                features.append(feature_name)
        
        return features
    
    def apply_business_type_template(self, business_type):
        """Apply default settings based on business type."""
        self.business_type = business_type
        
        # Reset all features first
        self.enable_table_management = False
        self.enable_kitchen_display = False
        self.enable_order_tracking = False
        self.enable_barcode_scanning = False
        self.enable_appointment_scheduling = False
        self.enable_service_tracking = False
        self.enable_inventory_alerts = True  # Always enabled
        self.enable_loyalty_program = False
        
        # Apply restaurant template
        if business_type in ['restaurant', 'cafe', 'bar', 'food_truck']:
            self.enable_table_management = True
            self.enable_kitchen_display = True
            self.enable_order_tracking = True
            self.restaurant_settings = {
                'max_tables': 50,
                'table_prefix': 'T',
                'order_timeout_minutes': 30,
                'kitchen_display_refresh_seconds': 5,
                'enable_table_qr_codes': False,
                'enable_takeout_orders': True,
                'enable_delivery_orders': False
            }
        
        # Apply retail template
        elif business_type in ['retail', 'grocery', 'pharmacy']:
            self.enable_barcode_scanning = True
            self.enable_loyalty_program = True
            self.retail_settings = {
                'enable_product_variants': True,
                'enable_bulk_pricing': True,
                'enable_customer_accounts': True,
                'enable_layaway': False,
                'barcode_format': 'UPC',
                'auto_generate_barcodes': False
            }
        
        # Apply service template
        elif business_type in ['service', 'salon', 'repair']:
            self.enable_appointment_scheduling = True
            self.enable_service_tracking = True
            self.service_settings = {
                'appointment_duration_minutes': 60,
                'advance_booking_days': 30,
                'enable_recurring_appointments': True,
                'enable_service_packages': True,
                'enable_staff_scheduling': True,
                'require_deposits': False,
                'deposit_percentage': 20
            }
        
        return self
    
    def get_restaurant_settings(self):
        """Get restaurant-specific settings with defaults."""
        if not self.restaurant_settings:
            return {}
        
        defaults = {
            'max_tables': 50,
            'table_prefix': 'T',
            'order_timeout_minutes': 30,
            'kitchen_display_refresh_seconds': 5,
            'enable_table_qr_codes': False,
            'enable_takeout_orders': True,
            'enable_delivery_orders': False
        }
        
        # Merge with stored settings
        settings = defaults.copy()
        settings.update(self.restaurant_settings)
        return settings
    
    def get_retail_settings(self):
        """Get retail-specific settings with defaults."""
        if not self.retail_settings:
            return {}
        
        defaults = {
            'enable_product_variants': True,
            'enable_bulk_pricing': True,
            'enable_customer_accounts': True,
            'enable_layaway': False,
            'barcode_format': 'UPC',
            'auto_generate_barcodes': False
        }
        
        # Merge with stored settings
        settings = defaults.copy()
        settings.update(self.retail_settings)
        return settings
    
    def get_service_settings(self):
        """Get service-specific settings with defaults."""
        if not self.service_settings:
            return {}
        
        defaults = {
            'appointment_duration_minutes': 60,
            'advance_booking_days': 30,
            'enable_recurring_appointments': True,
            'enable_service_packages': True,
            'enable_staff_scheduling': True,
            'require_deposits': False,
            'deposit_percentage': 20
        }
        
        # Merge with stored settings
        settings = defaults.copy()
        settings.update(self.service_settings)
        return settings
    
    def update_restaurant_settings(self, **kwargs):
        """Update restaurant-specific settings."""
        current_settings = self.get_restaurant_settings()
        current_settings.update(kwargs)
        self.restaurant_settings = current_settings
        return self
    
    def update_retail_settings(self, **kwargs):
        """Update retail-specific settings."""
        current_settings = self.get_retail_settings()
        current_settings.update(kwargs)
        self.retail_settings = current_settings
        return self
    
    def update_service_settings(self, **kwargs):
        """Update service-specific settings."""
        current_settings = self.get_service_settings()
        current_settings.update(kwargs)
        self.service_settings = current_settings
        return self
    
    @classmethod
    def create_for_tenant(cls, tenant_id, business_name, business_type='retail'):
        """Create business settings for a tenant with default template."""
        settings = cls(
            tenant_id=tenant_id,
            business_name=business_name
        )
        settings.apply_business_type_template(business_type)
        return settings.save()
    
    @classmethod
    def get_for_tenant(cls, tenant_id):
        """Get business settings for a tenant."""
        return cls.query.filter_by(tenant_id=tenant_id).first()
    
    @classmethod
    def get_or_create_for_tenant(cls, tenant_id, business_name=None, business_type='retail'):
        """Get existing business settings or create new ones for a tenant."""
        settings = cls.get_for_tenant(tenant_id)
        if not settings:
            if not business_name:
                business_name = f"Business {tenant_id}"
            settings = cls.create_for_tenant(tenant_id, business_name, business_type)
        return settings


class BusinessTypeTemplate:
    """Business type template definitions."""
    
    RESTAURANT = {
        'name': 'Restaurant',
        'code': 'restaurant',
        'description': 'Full-service restaurant with table management and kitchen operations',
        'features': [
            'enable_table_management',
            'enable_kitchen_display',
            'enable_order_tracking',
            'enable_inventory_alerts'
        ],
        'settings': {
            'max_tables': 50,
            'table_prefix': 'T',
            'order_timeout_minutes': 30,
            'kitchen_display_refresh_seconds': 5,
            'enable_table_qr_codes': False,
            'enable_takeout_orders': True,
            'enable_delivery_orders': False
        }
    }
    
    CAFE = {
        'name': 'Cafe',
        'code': 'cafe',
        'description': 'Coffee shop or casual dining establishment',
        'features': [
            'enable_table_management',
            'enable_order_tracking',
            'enable_inventory_alerts'
        ],
        'settings': {
            'max_tables': 20,
            'table_prefix': 'C',
            'order_timeout_minutes': 15,
            'enable_takeout_orders': True,
            'enable_delivery_orders': False
        }
    }
    
    RETAIL = {
        'name': 'Retail Store',
        'code': 'retail',
        'description': 'General retail store with product sales',
        'features': [
            'enable_barcode_scanning',
            'enable_inventory_alerts',
            'enable_loyalty_program'
        ],
        'settings': {
            'enable_product_variants': True,
            'enable_bulk_pricing': True,
            'enable_customer_accounts': True,
            'barcode_format': 'UPC',
            'auto_generate_barcodes': False
        }
    }
    
    SERVICE = {
        'name': 'Service Business',
        'code': 'service',
        'description': 'Service-based business with appointments',
        'features': [
            'enable_appointment_scheduling',
            'enable_service_tracking',
            'enable_inventory_alerts'
        ],
        'settings': {
            'appointment_duration_minutes': 60,
            'advance_booking_days': 30,
            'enable_recurring_appointments': True,
            'enable_service_packages': True,
            'require_deposits': False
        }
    }
    
    @classmethod
    def get_all_templates(cls):
        """Get all available business type templates."""
        return [
            cls.RESTAURANT,
            cls.CAFE,
            cls.RETAIL,
            cls.SERVICE
        ]
    
    @classmethod
    def get_template(cls, business_type):
        """Get template for specific business type."""
        templates = {
            'restaurant': cls.RESTAURANT,
            'cafe': cls.CAFE,
            'retail': cls.RETAIL,
            'service': cls.SERVICE
        }
        return templates.get(business_type)