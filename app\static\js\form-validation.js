/**
 * Client-side form validation for SaaS POS System
 * Provides real-time validation feedback and prevents invalid form submissions
 */

class FormValidator {
    constructor(form, options = {}) {
        this.form = form;
        this.options = {
            showErrors: true,
            validateOnInput: true,
            validateOnBlur: true,
            submitOnValid: true,
            ...options
        };
        
        this.errors = new Map();
        this.validators = new Map();
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.setupValidators();
        this.createErrorContainer();
    }
    
    setupEventListeners() {
        // Prevent form submission if validation fails
        this.form.addEventListener('submit', (e) => {
            if (!this.validateAll()) {
                e.preventDefault();
                this.showErrors();
                this.focusFirstError();
            }
        });
        
        // Real-time validation on input
        if (this.options.validateOnInput) {
            this.form.addEventListener('input', (e) => {
                if (e.target.matches('input, select, textarea')) {
                    this.validateField(e.target);
                }
            });
        }
        
        // Validation on blur
        if (this.options.validateOnBlur) {
            this.form.addEventListener('blur', (e) => {
                if (e.target.matches('input, select, textarea')) {
                    this.validateField(e.target);
                }
            }, true);
        }
    }
    
    setupValidators() {
        // Email validation - only for email fields
        this.addValidator('email', (value, field) => {
            // Only apply to email fields
            if (field.type !== 'email') return null;
            
            if (!value && field.required) return 'Email is required';
            if (value && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
                return 'Please enter a valid email address';
            }
            return null;
        });
        
        // Password validation - only for password fields
        this.addValidator('password', (value, field) => {
            // Only apply to password fields
            if (field.type !== 'password') return null;
            
            if (!value && field.required) return 'Password is required';
            if (value && value.length < 8) {
                return 'Password must be at least 8 characters long';
            }
            if (value && !/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(value)) {
                return 'Password must contain at least one uppercase letter, one lowercase letter, and one number';
            }
            return null;
        });
        
        // Required field validation
        this.addValidator('required', (value, field) => {
            if (field.required && (!value || value.trim() === '')) {
                return `${this.getFieldLabel(field)} is required`;
            }
            return null;
        });
        
        // Number validation - only for number fields
        this.addValidator('number', (value, field) => {
            // Only apply to number fields
            if (field.type !== 'number') return null;
            
            if (value) {
                const num = parseFloat(value);
                if (isNaN(num)) return 'Please enter a valid number';
                
                if (field.min !== null && num < parseFloat(field.min)) {
                    return `Value must be at least ${field.min}`;
                }
                
                if (field.max !== null && num > parseFloat(field.max)) {
                    return `Value must be no more than ${field.max}`;
                }
            }
            return null;
        });
        
        // Text length validation
        this.addValidator('length', (value, field) => {
            if (value) {
                // Only check minLength if it's set and is a positive number
                if (field.minLength && parseInt(field.minLength) > 0 && value.length < parseInt(field.minLength)) {
                    return `Must be at least ${field.minLength} characters long`;
                }
                
                // Only check maxLength if it's set and is a positive number
                if (field.maxLength && parseInt(field.maxLength) > 0 && value.length > parseInt(field.maxLength)) {
                    return `Must be no more than ${field.maxLength} characters long`;
                }
            }
            return null;
        });
        
        // Phone number validation - only for tel fields
        this.addValidator('tel', (value, field) => {
            // Only apply to tel fields
            if (field.type !== 'tel') return null;
            
            if (value) {
                // Basic phone number validation (can be enhanced for specific formats)
                if (!/^[\+]?[\d\s\-\(\)]{10,}$/.test(value)) {
                    return 'Please enter a valid phone number';
                }
            }
            return null;
        });
        
        // Price validation (for POS system) - only for fields with data-validate="price"
        this.addValidator('price', (value, field) => {
            // Only apply to fields with data-validate="price"
            if (field.dataset.validate !== 'price') return null;
            
            if (value) {
                const price = parseFloat(value);
                if (isNaN(price) || price < 0) {
                    return 'Please enter a valid price (must be 0 or greater)';
                }
                if (price > 999999.99) {
                    return 'Price cannot exceed $999,999.99';
                }
            }
            return null;
        });
        
        // Stock quantity validation - only for fields with data-validate="stock"
        this.addValidator('stock', (value, field) => {
            // Only apply to fields with data-validate="stock"
            if (field.dataset.validate !== 'stock') return null;
            
            if (value) {
                const stock = parseInt(value);
                if (isNaN(stock) || stock < 0) {
                    return 'Stock quantity must be 0 or greater';
                }
                if (stock > 999999) {
                    return 'Stock quantity cannot exceed 999,999';
                }
            }
            return null;
        });
    }
    
    addValidator(name, validatorFn) {
        this.validators.set(name, validatorFn);
    }
    
    validateField(field) {
        const value = field.value;
        const errors = [];
        
        // Run all applicable validators
        for (const [name, validator] of this.validators) {
            const error = validator(value, field);
            if (error) {
                errors.push(error);
            }
        }
        
        // Store or clear errors for this field
        if (errors.length > 0) {
            this.errors.set(field.name || field.id, errors[0]); // Show first error
            this.showFieldError(field, errors[0]);
        } else {
            this.errors.delete(field.name || field.id);
            this.clearFieldError(field);
        }
        
        return errors.length === 0;
    }
    
    validateAll() {
        const fields = this.form.querySelectorAll('input, select, textarea');
        let isValid = true;
        
        fields.forEach(field => {
            if (!this.validateField(field)) {
                isValid = false;
            }
        });
        
        return isValid;
    }
    
    showFieldError(field, message) {
        if (!this.options.showErrors) return;
        
        // Remove existing error
        this.clearFieldError(field);
        
        // Add error class to field
        field.classList.add('border-red-500', 'focus:border-red-500', 'focus:ring-red-500');
        field.classList.remove('border-gray-300', 'focus:border-blue-500', 'focus:ring-blue-500');
        
        // Create error message element
        const errorElement = document.createElement('div');
        errorElement.className = 'field-error text-red-600 text-sm mt-1';
        errorElement.textContent = message;
        errorElement.setAttribute('role', 'alert');
        errorElement.setAttribute('aria-live', 'polite');
        
        // Insert error message after the field
        field.parentNode.insertBefore(errorElement, field.nextSibling);
        
        // Update aria-describedby for accessibility
        const errorId = `error-${field.name || field.id}-${Date.now()}`;
        errorElement.id = errorId;
        
        const existingDescribedBy = field.getAttribute('aria-describedby') || '';
        field.setAttribute('aria-describedby', `${existingDescribedBy} ${errorId}`.trim());
        field.setAttribute('aria-invalid', 'true');
    }
    
    clearFieldError(field) {
        // Remove error classes
        field.classList.remove('border-red-500', 'focus:border-red-500', 'focus:ring-red-500');
        field.classList.add('border-gray-300', 'focus:border-blue-500', 'focus:ring-blue-500');
        
        // Remove error message
        const errorElement = field.parentNode.querySelector('.field-error');
        if (errorElement) {
            // Clean up aria-describedby
            const describedBy = field.getAttribute('aria-describedby') || '';
            const newDescribedBy = describedBy.replace(errorElement.id, '').trim();
            if (newDescribedBy) {
                field.setAttribute('aria-describedby', newDescribedBy);
            } else {
                field.removeAttribute('aria-describedby');
            }
            
            errorElement.remove();
        }
        
        field.setAttribute('aria-invalid', 'false');
    }
    
    showErrors() {
        if (!this.options.showErrors || this.errors.size === 0) return;
        
        // Create or update error summary
        let errorSummary = this.form.querySelector('.form-error-summary');
        if (!errorSummary) {
            errorSummary = document.createElement('div');
            errorSummary.className = 'form-error-summary bg-red-50 border border-red-200 rounded-md p-4 mb-4';
            errorSummary.setAttribute('role', 'alert');
            errorSummary.setAttribute('aria-live', 'assertive');
            this.form.insertBefore(errorSummary, this.form.firstChild);
        }
        
        const errorList = Array.from(this.errors.values());
        errorSummary.innerHTML = `
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-red-800">
                        Please correct the following errors:
                    </h3>
                    <div class="mt-2 text-sm text-red-700">
                        <ul class="list-disc list-inside space-y-1">
                            ${errorList.map(error => `<li>${error}</li>`).join('')}
                        </ul>
                    </div>
                </div>
            </div>
        `;
    }
    
    hideErrors() {
        const errorSummary = this.form.querySelector('.form-error-summary');
        if (errorSummary) {
            errorSummary.remove();
        }
    }
    
    focusFirstError() {
        const firstErrorField = this.form.querySelector('.border-red-500');
        if (firstErrorField) {
            firstErrorField.focus();
        }
    }
    
    getFieldLabel(field) {
        // Try to find associated label
        const label = this.form.querySelector(`label[for="${field.id}"]`) || 
                     field.closest('.form-group')?.querySelector('label');
        
        if (label) {
            return label.textContent.replace('*', '').trim();
        }
        
        // Fallback to placeholder or field name
        return field.placeholder || field.name || 'Field';
    }
    
    createErrorContainer() {
        // This method can be used to create a dedicated error container
        // if needed for specific form layouts
    }
    
    reset() {
        this.errors.clear();
        this.hideErrors();
        
        // Clear all field errors
        const fields = this.form.querySelectorAll('input, select, textarea');
        fields.forEach(field => this.clearFieldError(field));
    }
    
    isValid() {
        return this.errors.size === 0;
    }
    
    getErrors() {
        return new Map(this.errors);
    }
}

// Auto-initialize form validation on forms with data-validate attribute
document.addEventListener('DOMContentLoaded', function() {
    const forms = document.querySelectorAll('form[data-validate]');
    
    forms.forEach(form => {
        const options = {};
        
        // Parse options from data attributes
        if (form.dataset.validateOnInput === 'false') options.validateOnInput = false;
        if (form.dataset.validateOnBlur === 'false') options.validateOnBlur = false;
        if (form.dataset.showErrors === 'false') options.showErrors = false;
        
        new FormValidator(form, options);
    });
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = FormValidator;
}

// Global access
window.FormValidator = FormValidator;