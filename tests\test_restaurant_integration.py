"""Integration tests for restaurant functionality."""

import pytest
from datetime import datetime, timedelta
from app import create_app, db
from app.models.user import User, Tenant
from app.models.product import Product, Category
from app.models.restaurant import Table, Order, OrderItem, TableStatus, OrderStatus
from app.models.transaction import Transaction, TransactionStatus
from app.models.business import BusinessSettings
from app.models.base import set_current_tenant, clear_current_tenant
from app.services.restaurant_service import RestaurantService
from config import TestingConfig


@pytest.fixture
def app():
    """Create application for testing."""
    app = create_app(TestingConfig)
    return app


@pytest.fixture
def app_context(app):
    """Create application context."""
    with app.app_context():
        db.create_all()
        yield app
        db.session.remove()
        db.drop_all()


@pytest.fixture
def tenant(app_context):
    """Create test tenant."""
    tenant = Tenant(
        name="Test Restaurant",
        business_type="restaurant"
    )
    tenant.save()
    set_current_tenant(tenant.id)
    return tenant


@pytest.fixture
def user(app_context, tenant):
    """Create test user."""
    user = User.create_user(
        email="<EMAIL>",
        password="password123",
        first_name="Test",
        last_name="User",
        tenant_id=tenant.id,
        role="manager"
    )
    return user


@pytest.fixture
def business_settings(app_context, tenant):
    """Create business settings."""
    settings = BusinessSettings.create_for_tenant(
        tenant_id=tenant.id,
        business_name="Test Restaurant",
        business_type="restaurant"
    )
    return settings


@pytest.fixture
def category(app_context, tenant):
    """Create test category."""
    category = Category(
        tenant_id=tenant.id,
        name="Food",
        description="Food items"
    )
    return category.save()


@pytest.fixture
def products(app_context, tenant, category):
    """Create test products."""
    burger = Product(
        tenant_id=tenant.id,
        name="Burger",
        sku="BURGER001",
        category_id=category.id,
        selling_price=12.99,
        cost_price=6.50,
        current_stock=100
    ).save()
    
    fries = Product(
        tenant_id=tenant.id,
        name="Fries",
        sku="FRIES001",
        category_id=category.id,
        selling_price=4.99,
        cost_price=2.00,
        current_stock=200
    ).save()
    
    return [burger, fries]


class TestRestaurantWorkflow:
    """Test complete restaurant workflow."""
    
    def test_complete_dine_in_workflow(self, app_context, tenant, user, products):
        """Test complete dine-in workflow from seating to payment."""
        burger, fries = products
        
        # 1. Create table and seat party
        table = RestaurantService.create_table("T1", capacity=4, section="Main Dining")
        RestaurantService.seat_party("T1", party_size=2, estimated_duration=60)
        
        # Verify table is occupied
        assert table.status == TableStatus.OCCUPIED
        assert table.current_party_size == 2
        
        # 2. Create order for the table
        order = RestaurantService.create_order(
            table_number="T1",
            order_type="dine_in",
            customer_name="John Doe",
            user_id=user.id
        )
        
        assert order.table_id == table.id
        assert order.order_type == "dine_in"
        assert order.status == OrderStatus.PENDING
        
        # 3. Add items to order
        RestaurantService.add_item_to_order(order.id, burger.id, quantity=2)
        RestaurantService.add_item_to_order(order.id, fries.id, quantity=1, special_instructions="Extra crispy")
        
        # Verify items were added
        db.session.refresh(order)
        assert len(order.items) == 2
        
        # 4. Kitchen workflow - start preparation
        order = RestaurantService.start_order_preparation(order.id)
        assert order.status == OrderStatus.PREPARING
        assert order.preparation_started_at is not None
        
        # 5. Mark order ready
        order = RestaurantService.mark_order_ready(order.id)
        assert order.status == OrderStatus.READY
        assert order.ready_at is not None
        
        # 6. Serve order
        order = RestaurantService.serve_order(order.id)
        assert order.status == OrderStatus.SERVED
        assert order.served_at is not None
        
        # 7. Create transaction from order
        transaction = RestaurantService.create_transaction_from_order(order.id, user.id)
        
        assert transaction.customer_name == "John Doe"
        assert transaction.table_number == "T1"
        assert len(transaction.items) == 2
        
        # Verify transaction totals
        transaction.calculate_totals()
        expected_total = (burger.selling_price * 2) + fries.selling_price
        assert transaction.subtotal == expected_total
        
        # 8. Process payment
        transaction.process_payment("card", transaction.total_amount)
        assert transaction.status == TransactionStatus.COMPLETED
        assert transaction.is_paid()
        
        # 9. Clear table
        table = RestaurantService.clear_table("T1")
        assert table.status == TableStatus.AVAILABLE
        assert table.current_party_size is None
    
    def test_takeout_workflow(self, app_context, tenant, user, products):
        """Test takeout order workflow."""
        burger, fries = products
        
        # 1. Create takeout order (no table)
        order = RestaurantService.create_order(
            order_type="takeout",
            customer_name="Jane Smith",
            customer_phone="555-1234",
            user_id=user.id
        )
        
        assert order.table_id is None
        assert order.order_type == "takeout"
        assert order.customer_phone == "555-1234"
        
        # 2. Add items
        RestaurantService.add_item_to_order(order.id, burger.id, quantity=1)
        RestaurantService.add_item_to_order(order.id, fries.id, quantity=2)
        
        # 3. Kitchen workflow
        RestaurantService.start_order_preparation(order.id)
        RestaurantService.mark_order_ready(order.id)
        RestaurantService.serve_order(order.id)
        
        # 4. Create and process transaction
        transaction = RestaurantService.create_transaction_from_order(order.id, user.id)
        transaction.calculate_totals()
        transaction.process_payment("cash", transaction.total_amount)
        
        assert transaction.status == TransactionStatus.COMPLETED
        assert transaction.order_type == "takeout"
    
    def test_kitchen_display_orders(self, app_context, tenant, user, products):
        """Test kitchen display functionality."""
        burger, fries = products
        
        # Create multiple orders in different states
        order1 = RestaurantService.create_order(order_type="dine_in", user_id=user.id)
        order2 = RestaurantService.create_order(order_type="takeout", user_id=user.id)
        order3 = RestaurantService.create_order(order_type="delivery", user_id=user.id)
        
        # Add items to orders
        RestaurantService.add_item_to_order(order1.id, burger.id, 1)
        RestaurantService.add_item_to_order(order2.id, fries.id, 1)
        RestaurantService.add_item_to_order(order3.id, burger.id, 2)
        
        # Start preparation on one order
        RestaurantService.start_order_preparation(order2.id)
        
        # Mark one as ready
        RestaurantService.start_order_preparation(order3.id)
        RestaurantService.mark_order_ready(order3.id)
        
        # Get kitchen display orders
        kitchen_orders = RestaurantService.get_kitchen_display_orders()
        
        # Should show all active orders (pending, preparing, ready)
        assert len(kitchen_orders) == 3
        
        # Verify order statuses
        statuses = [o.status for o in kitchen_orders]
        assert OrderStatus.PENDING in statuses
        assert OrderStatus.PREPARING in statuses
        assert OrderStatus.READY in statuses
    
    def test_table_management_features(self, app_context, tenant, business_settings):
        """Test table management features."""
        # 1. Setup restaurant tables
        tables = RestaurantService.setup_restaurant_tables(business_settings)
        assert len(tables) > 0
        
        # 2. Get table layout
        layout = RestaurantService.get_table_layout()
        assert len(layout) == len(tables)
        
        # 3. Get table status summary
        summary = RestaurantService.get_table_status_summary()
        assert summary['total'] == len(tables)
        assert summary['available'] == len(tables)
        assert summary['occupied'] == 0
        
        # 4. Seat parties at different tables
        RestaurantService.seat_party("T1", party_size=2)
        RestaurantService.seat_party("T2", party_size=4)
        
        # 5. Reserve a table
        RestaurantService.reserve_table("T3", customer_name="Smith Party", phone="555-9999")
        
        # 6. Check updated summary
        updated_summary = RestaurantService.get_table_status_summary()
        assert updated_summary['occupied'] == 2
        assert updated_summary['reserved'] == 1
        assert updated_summary['available'] == len(tables) - 3
        
        # 7. Get occupied tables with duration
        occupied_tables = RestaurantService.get_occupied_tables_with_duration()
        assert len(occupied_tables) == 2
        
        for table_info in occupied_tables:
            assert 'duration_minutes' in table_info
            assert 'is_overdue' in table_info
            assert table_info['duration_minutes'] >= 0
    
    def test_restaurant_analytics(self, app_context, tenant, user, products):
        """Test restaurant analytics functionality."""
        burger, fries = products
        
        # Create and complete several orders
        for i in range(3):
            order = RestaurantService.create_order(
                order_type="dine_in" if i % 2 == 0 else "takeout",
                user_id=user.id
            )
            
            RestaurantService.add_item_to_order(order.id, burger.id, 1)
            if i > 0:
                RestaurantService.add_item_to_order(order.id, fries.id, 1)
            
            # Complete the order
            RestaurantService.start_order_preparation(order.id)
            RestaurantService.mark_order_ready(order.id)
            RestaurantService.serve_order(order.id)
        
        # Get analytics
        analytics = RestaurantService.get_restaurant_analytics()
        
        assert analytics['total_orders'] == 3
        assert 'average_preparation_time' in analytics
        assert 'orders_by_type' in analytics
        assert analytics['orders_by_type']['dine_in'] == 2
        assert analytics['orders_by_type']['takeout'] == 1
    
    def test_error_handling(self, app_context, tenant, user):
        """Test error handling in restaurant service."""
        # Test seating party at non-existent table
        with pytest.raises(ValueError, match="not found"):
            RestaurantService.seat_party("T99", party_size=2)
        
        # Test creating order without user
        with pytest.raises(ValueError, match="User ID is required"):
            RestaurantService.create_order(order_type="dine_in")
        
        # Test adding item to non-existent order
        with pytest.raises(ValueError, match="Order not found"):
            RestaurantService.add_item_to_order(999, 1, 1)
        
        # Test operations without tenant context
        clear_current_tenant()
        with pytest.raises(ValueError, match="No tenant context"):
            RestaurantService.create_table("T1")
    
    def teardown_method(self):
        """Clean up after each test."""
        clear_current_tenant()