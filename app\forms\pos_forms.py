"""POS forms for SaaS POS System."""

from flask_wtf import FlaskForm
from wtforms import StringField, FloatField, IntegerField, SelectField, TextAreaField
from wtforms.validators import DataRequired, Optional, NumberRange
from app.utils.validators import (
    validate_email, validate_phone_number, validate_positive_number,
    validate_discount_value, validate_price
)


class CustomerForm(FlaskForm):
    """Customer information form for transactions."""
    name = StringField('Customer Name', validators=[Optional()])
    email = StringField('Email', validators=[
        Optional(),
        validate_email
    ])
    phone = StringField('Phone', validators=[
        Optional(),
        validate_phone_number
    ])
    table_number = StringField('Table Number', validators=[Optional()])
    order_type = SelectField('Order Type', choices=[
        ('dine_in', 'Dine In'),
        ('takeout', 'Takeout'),
        ('delivery', 'Delivery'),
        ('pickup', 'Pickup')
    ], default='dine_in')


class AddItemForm(FlaskForm):
    """Add item to cart form."""
    product_id = IntegerField('Product ID', validators=[
        DataRequired(message='Product ID is required'),
        NumberRange(min=1, message='Invalid product ID')
    ])
    quantity = IntegerField('Quantity', validators=[
        DataRequired(message='Quantity is required'),
        NumberRange(min=1, max=999, message='Quantity must be between 1 and 999')
    ], default=1)
    unit_price = FloatField('Unit Price', validators=[
        Optional(),
        validate_positive_number,
        validate_price
    ])


class UpdateQuantityForm(FlaskForm):
    """Update item quantity form."""
    product_id = IntegerField('Product ID', validators=[
        DataRequired(message='Product ID is required'),
        NumberRange(min=1, message='Invalid product ID')
    ])
    quantity = IntegerField('Quantity', validators=[
        DataRequired(message='Quantity is required'),
        NumberRange(min=0, max=999, message='Quantity must be between 0 and 999')
    ])


class DiscountForm(FlaskForm):
    """Apply discount form."""
    discount_type = SelectField('Discount Type', choices=[
        ('percentage', 'Percentage'),
        ('fixed', 'Fixed Amount')
    ], validators=[DataRequired(message='Discount type is required')])
    discount_value = FloatField('Discount Value', validators=[
        DataRequired(message='Discount value is required'),
        validate_positive_number,
        validate_discount_value
    ])
    reason = TextAreaField('Reason', validators=[Optional()])


class PaymentForm(FlaskForm):
    """Payment processing form."""
    payment_method = SelectField('Payment Method', choices=[
        ('cash', 'Cash'),
        ('credit_card', 'Credit Card'),
        ('debit_card', 'Debit Card'),
        ('mobile_payment', 'Mobile Payment'),
        ('gift_card', 'Gift Card'),
        ('store_credit', 'Store Credit'),
        ('check', 'Check')
    ], validators=[DataRequired(message='Payment method is required')])
    amount_paid = FloatField('Amount Paid', validators=[
        DataRequired(message='Amount paid is required'),
        validate_positive_number,
        validate_price
    ])


class CancelTransactionForm(FlaskForm):
    """Cancel transaction form."""
    reason = TextAreaField('Cancellation Reason', validators=[Optional()])