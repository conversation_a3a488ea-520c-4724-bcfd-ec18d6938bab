"""Inventory forms for SaaS POS System."""

from flask_wtf import FlaskForm
from wtforms import <PERSON>Field, TextAreaField, IntegerField, FloatField, BooleanField, SelectField
from wtforms.validators import DataRequired, Optional, NumberRange
from app.utils.validators import (
    validate_product_name, validate_sku, validate_barcode, validate_price,
    validate_stock_quantity, validate_tax_rate, validate_url, validate_color_hex,
    validate_non_negative_number, validate_positive_number
)


class ProductForm(FlaskForm):
    """Product creation/edit form."""
    name = StringField('Product Name', validators=[
        DataRequired(message='Product name is required'),
        validate_product_name
    ])
    description = TextAreaField('Description', validators=[Optional()])
    sku = StringField('SKU', validators=[
        Optional(),
        validate_sku
    ])
    barcode = StringField('Barcode', validators=[
        Optional(),
        validate_barcode
    ])
    category_id = SelectField('Category', coerce=int, validators=[Optional()])
    cost_price = FloatField('Cost Price', validators=[
        Optional(),
        validate_non_negative_number,
        validate_price
    ], default=0.0)
    selling_price = FloatField('Selling Price', validators=[
        DataRequired(message='Selling price is required'),
        validate_positive_number,
        validate_price
    ])
    current_stock = IntegerField('Current Stock', validators=[
        Optional(),
        validate_stock_quantity
    ], default=0)
    minimum_stock = IntegerField('Minimum Stock', validators=[
        Optional(),
        validate_stock_quantity
    ], default=0)
    maximum_stock = IntegerField('Maximum Stock', validators=[
        Optional(),
        validate_stock_quantity
    ])
    track_inventory = BooleanField('Track Inventory', default=True)
    allow_negative_stock = BooleanField('Allow Negative Stock', default=False)
    unit_of_measure = SelectField('Unit of Measure', choices=[
        ('each', 'Each'),
        ('kg', 'Kilogram'),
        ('g', 'Gram'),
        ('lb', 'Pound'),
        ('oz', 'Ounce'),
        ('l', 'Liter'),
        ('ml', 'Milliliter'),
        ('m', 'Meter'),
        ('cm', 'Centimeter'),
        ('ft', 'Foot'),
        ('in', 'Inch')
    ], default='each')
    weight = FloatField('Weight', validators=[
        Optional(),
        validate_non_negative_number
    ])
    tax_rate = FloatField('Tax Rate (%)', validators=[
        Optional(),
        validate_tax_rate
    ], default=0.0)
    tax_inclusive = BooleanField('Tax Inclusive', default=False)
    is_active = BooleanField('Active', default=True)
    is_featured = BooleanField('Featured', default=False)
    image_url = StringField('Image URL', validators=[
        Optional(),
        validate_url
    ])
    color = StringField('Color', validators=[
        Optional(),
        validate_color_hex
    ])
    sort_order = IntegerField('Sort Order', validators=[
        Optional(),
        NumberRange(min=0, max=9999)
    ], default=0)


class CategoryForm(FlaskForm):
    """Category creation/edit form."""
    name = StringField('Category Name', validators=[
        DataRequired(message='Category name is required'),
        validate_product_name  # Reuse product name validation
    ])
    description = TextAreaField('Description', validators=[Optional()])
    parent_id = SelectField('Parent Category', coerce=int, validators=[Optional()])
    color = StringField('Color', validators=[
        Optional(),
        validate_color_hex
    ])
    icon = StringField('Icon', validators=[Optional()])
    sort_order = IntegerField('Sort Order', validators=[
        Optional(),
        NumberRange(min=0, max=9999)
    ], default=0)
    is_active = BooleanField('Active', default=True)


class StockAdjustmentForm(FlaskForm):
    """Stock adjustment form."""
    quantity = IntegerField('Quantity Adjustment', validators=[
        DataRequired(message='Quantity is required'),
        NumberRange(min=-999999, max=999999, message='Invalid quantity range')
    ])
    reason = SelectField('Reason', choices=[
        ('manual_adjustment', 'Manual Adjustment'),
        ('inventory_count', 'Inventory Count'),
        ('damaged', 'Damaged'),
        ('expired', 'Expired'),
        ('theft', 'Theft'),
        ('supplier_return', 'Supplier Return'),
        ('customer_return', 'Customer Return'),
        ('other', 'Other')
    ], default='manual_adjustment')
    notes = TextAreaField('Notes', validators=[Optional()])