{% extends "base.html" %}

{% block title %}POS Dashboard - SaaS POS System{% endblock %}

{% block content %}
<!-- Mobile-first responsive POS layout -->
<div class="flex flex-col lg:flex-row min-h-screen bg-gray-100">
    <!-- Mobile cart toggle button -->
    <button id="mobile-cart-toggle" class="lg:hidden fixed bottom-4 right-4 z-30 bg-blue-600 text-white p-3 rounded-full shadow-lg touch-target">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5-6m0 0h15M17 21a2 2 0 100-4 2 2 0 000 4zM9 21a2 2 0 100-4 2 2 0 000 4z"></path>
        </svg>
        <span id="cart-badge" class="hidden absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-6 w-6 flex items-center justify-center">0</span>
    </button>

    <!-- Products Section -->
    <div class="flex-1 lg:w-2/3 bg-white shadow-lg order-2 lg:order-1">
        <!-- Header -->
        <div class="bg-blue-600 text-white p-4">
            <div class="flex justify-between items-center">
                <h1 class="text-lg sm:text-xl font-bold">POS System</h1>
                <div class="flex items-center space-x-2 sm:space-x-4">
                    <span class="text-sm hidden sm:inline">{{ current_user.get_full_name() }}</span>
                    <!-- Quick actions for mobile -->
                    <div class="flex space-x-2">
                        <button id="quick-search-btn" class="lg:hidden bg-blue-700 hover:bg-blue-800 p-2 rounded touch-target">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Search Bar -->
        <div class="p-4 border-b">
            <div class="relative">
                <input type="text" id="product-search" placeholder="Search products..." 
                       class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center">
                    <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                </div>
            </div>
        </div>

        <!-- Categories -->
        <div class="p-4 border-b">
            <div class="flex flex-wrap gap-2">
                <button class="category-btn bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors" 
                        data-category-id="">All Products</button>
                {% for category in categories %}
                <button class="category-btn bg-gray-200 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-300 transition-colors" 
                        data-category-id="{{ category.id }}">{{ category.name }}</button>
                {% endfor %}
            </div>
        </div>

        <!-- Products Grid -->
        <div class="flex-1 overflow-y-auto p-4">
            <div id="products-grid" class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                <!-- Featured products will be loaded here initially -->
                {% for product in featured_products %}
                <div class="product-card bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer"
                     data-product-id="{{ product.id }}"
                     data-product-name="{{ product.name }}"
                     data-product-price="{{ product.selling_price }}"
                     data-product-stock="{{ product.current_stock }}"
                     data-track-inventory="{{ product.track_inventory }}">
                    {% if product.image_url %}
                    <img src="{{ product.image_url }}" alt="{{ product.name }}" class="w-full h-24 object-cover rounded mb-2">
                    {% else %}
                    <div class="w-full h-24 bg-gray-200 rounded mb-2 flex items-center justify-center">
                        <span class="text-gray-400 text-xs">No Image</span>
                    </div>
                    {% endif %}
                    <h3 class="font-semibold text-sm mb-1 truncate">{{ product.name }}</h3>
                    <p class="text-blue-600 font-bold">${{ "%.2f"|format(product.selling_price) }}</p>
                    {% if product.track_inventory %}
                    <p class="text-xs text-gray-500">Stock: {{ product.current_stock }}</p>
                    {% endif %}
                    {% if product.is_low_stock() %}
                    <span class="inline-block bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded mt-1">Low Stock</span>
                    {% endif %}
                </div>
                {% endfor %}
            </div>
            
            <!-- Loading indicator -->
            <div id="loading-indicator" class="hidden text-center py-4">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
        </div>
    </div>

    <!-- Cart Section - Mobile overlay / Desktop sidebar -->
    <div id="cart-section" class="lg:w-1/3 bg-white shadow-lg border-l order-1 lg:order-2 
                                  lg:relative fixed inset-y-0 right-0 z-40 transform translate-x-full lg:translate-x-0 
                                  transition-transform duration-300 ease-in-out w-full max-w-sm lg:max-w-none">
        <!-- Mobile cart header with close button -->
        <div class="lg:hidden bg-gray-50 p-4 border-b flex justify-between items-center">
            <h2 class="text-lg font-semibold">Shopping Cart</h2>
            <button id="close-cart-btn" class="p-2 text-gray-400 hover:text-gray-600 touch-target">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        
        <!-- Desktop cart header -->
        <div class="hidden lg:block bg-gray-50 p-4 border-b">
            <div class="flex justify-between items-center">
                <h2 class="text-lg font-semibold">Current Order</h2>
                <button id="new-transaction-btn" class="bg-green-500 hover:bg-green-600 text-white px-3 py-1 rounded text-sm touch-target">
                    New Order
                </button>
            </div>
        </div>

        <!-- Pending Transactions -->
        {% if pending_transactions %}
        <div class="p-4 border-b bg-yellow-50">
            <h3 class="text-sm font-semibold mb-2">Pending Orders</h3>
            <div class="space-y-2">
                {% for transaction in pending_transactions %}
                <div class="flex justify-between items-center bg-white p-2 rounded border cursor-pointer hover:bg-gray-50 transaction-item"
                     data-transaction-id="{{ transaction.id }}">
                    <div>
                        <span class="text-sm font-medium">#{{ transaction.transaction_number }}</span>
                        {% if transaction.customer_name %}
                        <p class="text-xs text-gray-600">{{ transaction.customer_name }}</p>
                        {% endif %}
                    </div>
                    <span class="text-sm font-bold">${{ "%.2f"|format(transaction.total_amount) }}</span>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}

        <!-- Current Transaction -->
        <div id="current-transaction" class="flex-1 flex flex-col">
            <!-- Transaction Info -->
            <div id="transaction-info" class="hidden p-4 bg-blue-50 border-b">
                <div class="text-sm">
                    <span class="font-semibold">Order #</span>
                    <span id="transaction-number"></span>
                </div>
            </div>

            <!-- Cart Items -->
            <div class="flex-1 overflow-y-auto">
                <div id="cart-items" class="p-4 space-y-2">
                    <div class="text-center text-gray-500 py-8">
                        <p>No items in cart</p>
                        <p class="text-sm">Click on products to add them</p>
                    </div>
                </div>
            </div>

            <!-- Cart Summary -->
            <div id="cart-summary" class="hidden border-t bg-gray-50 p-4">
                <div class="space-y-2 text-sm">
                    <div class="flex justify-between">
                        <span>Subtotal:</span>
                        <span id="subtotal">$0.00</span>
                    </div>
                    <div class="flex justify-between">
                        <span>Tax:</span>
                        <span id="tax-amount">$0.00</span>
                    </div>
                    <div class="flex justify-between">
                        <span>Discount:</span>
                        <span id="discount-amount">$0.00</span>
                    </div>
                    <div class="flex justify-between font-bold text-lg border-t pt-2">
                        <span>Total:</span>
                        <span id="total-amount">$0.00</span>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div id="cart-actions" class="hidden p-4 border-t space-y-2">
                <button id="apply-discount-btn" class="w-full bg-yellow-500 hover:bg-yellow-600 text-white py-2 rounded">
                    Apply Discount
                </button>
                <button id="checkout-btn" class="w-full bg-green-500 hover:bg-green-600 text-white py-2 rounded font-semibold">
                    Checkout
                </button>
                <button id="cancel-transaction-btn" class="w-full bg-red-500 hover:bg-red-600 text-white py-2 rounded">
                    Cancel Order
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Discount Modal -->
<div id="discount-modal" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg p-6 w-96">
        <h3 class="text-lg font-semibold mb-4">Apply Discount</h3>
        <form id="discount-form">
            <div class="mb-4">
                <label class="block text-sm font-medium mb-2">Discount Type</label>
                <select id="discount-type" class="w-full border border-gray-300 rounded px-3 py-2">
                    <option value="percentage">Percentage (%)</option>
                    <option value="fixed_amount">Fixed Amount ($)</option>
                </select>
            </div>
            <div class="mb-4">
                <label class="block text-sm font-medium mb-2">Discount Value</label>
                <input type="number" id="discount-value" step="0.01" min="0" 
                       class="w-full border border-gray-300 rounded px-3 py-2" required>
            </div>
            <div class="mb-4">
                <label class="block text-sm font-medium mb-2">Reason (Optional)</label>
                <input type="text" id="discount-reason" 
                       class="w-full border border-gray-300 rounded px-3 py-2" 
                       placeholder="e.g., Customer discount, Promotion">
            </div>
            <div class="flex space-x-2">
                <button type="submit" class="flex-1 bg-blue-500 hover:bg-blue-600 text-white py-2 rounded">
                    Apply
                </button>
                <button type="button" id="cancel-discount-btn" class="flex-1 bg-gray-500 hover:bg-gray-600 text-white py-2 rounded">
                    Cancel
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Customer Info Modal -->
<div id="customer-modal" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg p-6 w-96">
        <h3 class="text-lg font-semibold mb-4">Customer Information</h3>
        <form id="customer-form">
            <div class="mb-4">
                <label class="block text-sm font-medium mb-2">Customer Name</label>
                <input type="text" id="customer-name" 
                       class="w-full border border-gray-300 rounded px-3 py-2">
            </div>
            <div class="mb-4">
                <label class="block text-sm font-medium mb-2">Email</label>
                <input type="email" id="customer-email" 
                       class="w-full border border-gray-300 rounded px-3 py-2">
            </div>
            <div class="mb-4">
                <label class="block text-sm font-medium mb-2">Phone</label>
                <input type="tel" id="customer-phone" 
                       class="w-full border border-gray-300 rounded px-3 py-2">
            </div>
            <div class="mb-4">
                <label class="block text-sm font-medium mb-2">Table Number</label>
                <input type="text" id="table-number" 
                       class="w-full border border-gray-300 rounded px-3 py-2">
            </div>
            <div class="flex space-x-2">
                <button type="submit" class="flex-1 bg-blue-500 hover:bg-blue-600 text-white py-2 rounded">
                    Save
                </button>
                <button type="button" id="cancel-customer-btn" class="flex-1 bg-gray-500 hover:bg-gray-600 text-white py-2 rounded">
                    Cancel
                </button>
            </div>
        </form>
    </div>
</div>

<script>
// POS Dashboard JavaScript
let currentTransactionId = null;
let currentTransaction = null;

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    initializeEventListeners();
    initializeFeaturedProducts();
    loadProducts();
});

function initializeEventListeners() {
    // Mobile cart toggle
    const mobileCartToggle = document.getElementById('mobile-cart-toggle');
    const cartSection = document.getElementById('cart-section');
    const closeCartBtn = document.getElementById('close-cart-btn');
    
    if (mobileCartToggle && cartSection) {
        mobileCartToggle.addEventListener('click', function() {
            cartSection.classList.remove('translate-x-full');
            document.body.classList.add('overflow-hidden'); // Prevent background scroll
        });
    }
    
    if (closeCartBtn && cartSection) {
        closeCartBtn.addEventListener('click', function() {
            cartSection.classList.add('translate-x-full');
            document.body.classList.remove('overflow-hidden');
        });
    }
    
    // Quick search toggle for mobile
    const quickSearchBtn = document.getElementById('quick-search-btn');
    const productSearch = document.getElementById('product-search');
    
    if (quickSearchBtn && productSearch) {
        quickSearchBtn.addEventListener('click', function() {
            productSearch.focus();
            productSearch.scrollIntoView({ behavior: 'smooth' });
        });
    }

    // Product search
    document.getElementById('product-search').addEventListener('input', debounce(function(e) {
        loadProducts(null, e.target.value);
    }, 300));

    // Category buttons
    document.querySelectorAll('.category-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            // Update active category
            document.querySelectorAll('.category-btn').forEach(b => {
                b.classList.remove('bg-blue-500', 'text-white');
                b.classList.add('bg-gray-200', 'text-gray-700');
            });
            this.classList.remove('bg-gray-200', 'text-gray-700');
            this.classList.add('bg-blue-500', 'text-white');

            // Load products for category
            const categoryId = this.dataset.categoryId || null;
            loadProducts(categoryId);
        });
    });

    // New transaction button
    document.getElementById('new-transaction-btn').addEventListener('click', createNewTransaction);

    // Pending transaction items
    document.querySelectorAll('.transaction-item').forEach(item => {
        item.addEventListener('click', function() {
            const transactionId = parseInt(this.dataset.transactionId);
            loadTransaction(transactionId);
        });
    });

    // Cart action buttons
    document.getElementById('apply-discount-btn').addEventListener('click', showDiscountModal);
    document.getElementById('checkout-btn').addEventListener('click', proceedToCheckout);
    document.getElementById('cancel-transaction-btn').addEventListener('click', cancelCurrentTransaction);

    // Modal event listeners
    setupModalEventListeners();
    
    // Keyboard shortcuts for POS
    setupPOSKeyboardShortcuts();
}

function setupModalEventListeners() {
    // Discount modal
    document.getElementById('discount-form').addEventListener('submit', function(e) {
        e.preventDefault();
        applyDiscount();
    });
    document.getElementById('cancel-discount-btn').addEventListener('click', hideDiscountModal);

    // Customer modal
    document.getElementById('customer-form').addEventListener('submit', function(e) {
        e.preventDefault();
        saveCustomerInfo();
    });
    document.getElementById('cancel-customer-btn').addEventListener('click', hideCustomerModal);
}

function hideCustomerModal() {
    document.getElementById('customer-modal').classList.add('hidden');
}

function initializeFeaturedProducts() {
    // Add click events to pre-loaded featured products
    document.querySelectorAll('.product-card[data-product-id]').forEach(card => {
        const productId = card.dataset.productId;
        const productName = card.dataset.productName;
        const productPrice = parseFloat(card.dataset.productPrice);
        const productStock = parseInt(card.dataset.productStock) || 0;
        const trackInventory = card.dataset.trackInventory === 'True';

        // Create product object from data attributes
        const product = {
            id: parseInt(productId),
            name: productName,
            selling_price: productPrice,
            current_stock: productStock,
            track_inventory: trackInventory
        };

        // Check if product can be sold
        const canSell = trackInventory ? productStock > 0 : true;

        if (canSell) {
            card.addEventListener('click', () => addProductToCart(product));
        }
    });
}

async function loadProducts(categoryId = null, search = '') {
    try {
        showLoading();
        
        const params = new URLSearchParams();
        if (categoryId) params.append('category_id', categoryId);
        if (search) params.append('search', search);
        
        const response = await fetch(`/pos/products?${params}`);
        const data = await response.json();
        
        if (response.ok) {
            displayProducts(data.products);
        } else {
            showError('Error loading products: ' + data.error);
        }
    } catch (error) {
        showError('Error loading products: ' + error.message);
    } finally {
        hideLoading();
    }
}

function displayProducts(products) {
    const grid = document.getElementById('products-grid');
    grid.innerHTML = '';
    
    products.forEach(product => {
        const productCard = createProductCard(product);
        grid.appendChild(productCard);
    });
}

function createProductCard(product) {
    const card = document.createElement('div');
    card.className = 'product-card bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer';
    card.dataset.productId = product.id;
    
    const trackInventory = product.track_inventory || false;
    const currentStock = product.current_stock || 0;
    const canSell = trackInventory ? (currentStock > 0 || product.allow_negative_stock) : true;
    
    if (!canSell) {
        card.classList.add('opacity-50', 'cursor-not-allowed');
    }
    
    card.innerHTML = `
        ${product.image_url ? 
            `<img src="${product.image_url}" alt="${product.name}" class="w-full h-24 object-cover rounded mb-2">` :
            `<div class="w-full h-24 bg-gray-200 rounded mb-2 flex items-center justify-center">
                <span class="text-gray-400 text-xs">No Image</span>
            </div>`
        }
        <h3 class="font-semibold text-sm mb-1 truncate">${product.name || ''}</h3>
        <p class="text-blue-600 font-bold">$${(product.selling_price || 0).toFixed(2)}</p>
        ${trackInventory ? `<p class="text-xs text-gray-500">Stock: ${currentStock}</p>` : ''}
        ${trackInventory && currentStock <= (product.low_stock_threshold || 5) ? '<span class="inline-block bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded mt-1">Low Stock</span>' : ''}
        ${!canSell ? '<span class="inline-block bg-red-100 text-red-800 text-xs px-2 py-1 rounded mt-1">Out of Stock</span>' : ''}
    `;
    
    if (canSell) {
        card.addEventListener('click', () => addProductToCart(product));
    }
    
    return card;
}

async function createNewTransaction() {
    try {
        const response = await fetch('/pos/transaction/create', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({})
        });
        
        const data = await response.json();
        
        if (response.ok) {
            currentTransactionId = data.transaction.id;
            currentTransaction = data.transaction;
            updateTransactionDisplay();
            showSuccess('New order created');
        } else {
            showError('Error creating transaction: ' + data.error);
        }
    } catch (error) {
        showError('Error creating transaction: ' + error.message);
    }
}

async function loadTransaction(transactionId) {
    try {
        const response = await fetch(`/pos/transaction/${transactionId}`);
        const data = await response.json();
        
        if (response.ok) {
            currentTransactionId = transactionId;
            currentTransaction = data.transaction;
            updateTransactionDisplay();
        } else {
            showError('Error loading transaction: ' + data.error);
        }
    } catch (error) {
        showError('Error loading transaction: ' + error.message);
    }
}

async function addProductToCart(product) {
    if (!product || !product.id) {
        showError('Invalid product data');
        return;
    }

    if (!currentTransactionId) {
        await createNewTransaction();
        if (!currentTransactionId) return;
    }

    try {
        const response = await fetch(`/pos/transaction/${currentTransactionId}/add_item`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                product_id: product.id,
                quantity: 1
            })
        });

        const data = await response.json();

        if (response.ok) {
            currentTransaction = data.transaction;
            updateTransactionDisplay();
            // Use product name if available, otherwise use a default message
            const productName = product.name || 'Product';
            showSuccess(`Added ${productName} to cart`);
        } else {
            showError('Error adding item: ' + data.error);
        }
    } catch (error) {
        showError('Error adding item: ' + error.message);
    }
}

function updateTransactionDisplay() {
    if (!currentTransaction) {
        // Hide transaction elements
        document.getElementById('transaction-info').classList.add('hidden');
        document.getElementById('cart-summary').classList.add('hidden');
        document.getElementById('cart-actions').classList.add('hidden');
        document.getElementById('cart-items').innerHTML = `
            <div class="text-center text-gray-500 py-8">
                <p>No items in cart</p>
                <p class="text-sm">Click on products to add them</p>
            </div>
        `;
        return;
    }
    
    // Show transaction info
    document.getElementById('transaction-info').classList.remove('hidden');
    document.getElementById('transaction-number').textContent = currentTransaction.transaction_number;
    
    // Update cart items
    const cartItems = document.getElementById('cart-items');
    if (currentTransaction.items && currentTransaction.items.length > 0) {
        cartItems.innerHTML = '';
        currentTransaction.items.forEach(item => {
            const itemElement = createCartItemElement(item);
            cartItems.appendChild(itemElement);
        });
        
        // Show summary and actions
        document.getElementById('cart-summary').classList.remove('hidden');
        document.getElementById('cart-actions').classList.remove('hidden');
        
        // Update totals
        updateCartTotals();
    } else {
        cartItems.innerHTML = `
            <div class="text-center text-gray-500 py-8">
                <p>No items in cart</p>
                <p class="text-sm">Click on products to add them</p>
            </div>
        `;
        document.getElementById('cart-summary').classList.add('hidden');
        document.getElementById('cart-actions').classList.add('hidden');
    }
}

function createCartItemElement(item) {
    const div = document.createElement('div');
    div.className = 'flex items-center justify-between bg-gray-50 p-3 rounded border';
    div.innerHTML = `
        <div class="flex-1">
            <h4 class="font-medium text-sm">${item.product_name}</h4>
            <p class="text-xs text-gray-600">$${item.unit_price.toFixed(2)} each</p>
        </div>
        <div class="flex items-center space-x-2">
            <button class="quantity-btn bg-gray-200 hover:bg-gray-300 w-8 h-8 rounded flex items-center justify-center text-sm" 
                    onclick="updateQuantity(${item.product_id}, ${item.quantity - 1})">-</button>
            <span class="w-8 text-center text-sm">${item.quantity}</span>
            <button class="quantity-btn bg-gray-200 hover:bg-gray-300 w-8 h-8 rounded flex items-center justify-center text-sm" 
                    onclick="updateQuantity(${item.product_id}, ${item.quantity + 1})">+</button>
            <button class="remove-btn bg-red-500 hover:bg-red-600 text-white w-8 h-8 rounded flex items-center justify-center text-sm" 
                    onclick="removeItem(${item.product_id})">×</button>
        </div>
        <div class="ml-2 text-right">
            <p class="font-semibold text-sm">$${item.line_total.toFixed(2)}</p>
        </div>
    `;
    return div;
}

async function updateQuantity(productId, newQuantity) {
    if (!currentTransactionId) return;
    
    try {
        const response = await fetch(`/pos/transaction/${currentTransactionId}/update_quantity`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                product_id: productId,
                quantity: newQuantity
            })
        });
        
        const data = await response.json();
        
        if (response.ok) {
            currentTransaction = data.transaction;
            updateTransactionDisplay();
        } else {
            showError('Error updating quantity: ' + data.error);
        }
    } catch (error) {
        showError('Error updating quantity: ' + error.message);
    }
}

async function removeItem(productId) {
    if (!currentTransactionId) return;
    
    try {
        const response = await fetch(`/pos/transaction/${currentTransactionId}/remove_item`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                product_id: productId
            })
        });
        
        const data = await response.json();
        
        if (response.ok) {
            currentTransaction = data.transaction;
            updateTransactionDisplay();
            showSuccess('Item removed from cart');
        } else {
            showError('Error removing item: ' + data.error);
        }
    } catch (error) {
        showError('Error removing item: ' + error.message);
    }
}

function updateCartTotals() {
    if (!currentTransaction) return;
    
    document.getElementById('subtotal').textContent = `$${currentTransaction.subtotal.toFixed(2)}`;
    document.getElementById('tax-amount').textContent = `$${currentTransaction.tax_amount.toFixed(2)}`;
    document.getElementById('discount-amount').textContent = `$${currentTransaction.discount_amount.toFixed(2)}`;
    document.getElementById('total-amount').textContent = `$${currentTransaction.total_amount.toFixed(2)}`;
}

function showDiscountModal() {
    document.getElementById('discount-modal').classList.remove('hidden');
}

function hideDiscountModal() {
    document.getElementById('discount-modal').classList.add('hidden');
    document.getElementById('discount-form').reset();
}

async function applyDiscount() {
    if (!currentTransactionId) return;
    
    const discountType = document.getElementById('discount-type').value;
    const discountValue = parseFloat(document.getElementById('discount-value').value);
    const reason = document.getElementById('discount-reason').value;
    
    try {
        const response = await fetch(`/pos/transaction/${currentTransactionId}/apply_discount`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                discount_type: discountType,
                discount_value: discountValue,
                reason: reason
            })
        });
        
        const data = await response.json();
        
        if (response.ok) {
            currentTransaction = data.transaction;
            updateTransactionDisplay();
            hideDiscountModal();
            showSuccess('Discount applied');
        } else {
            showError('Error applying discount: ' + data.error);
        }
    } catch (error) {
        showError('Error applying discount: ' + error.message);
    }
}

function proceedToCheckout() {
    if (!currentTransactionId) return;
    window.location.href = `/pos/checkout/${currentTransactionId}`;
}

async function cancelCurrentTransaction() {
    if (!currentTransactionId) return;
    
    if (!confirm('Are you sure you want to cancel this order?')) return;
    
    try {
        const response = await fetch(`/pos/transaction/${currentTransactionId}/cancel`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                reason: 'Cancelled by user'
            })
        });
        
        const data = await response.json();
        
        if (response.ok) {
            currentTransactionId = null;
            currentTransaction = null;
            updateTransactionDisplay();
            showSuccess('Order cancelled');
            // Reload page to update pending transactions
            setTimeout(() => window.location.reload(), 1000);
        } else {
            showError('Error cancelling transaction: ' + data.error);
        }
    } catch (error) {
        showError('Error cancelling transaction: ' + error.message);
    }
}

function setupPOSKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // F1 - New Transaction
        if (e.key === 'F1') {
            e.preventDefault();
            createNewTransaction();
        }
        
        // F2 - Focus search
        if (e.key === 'F2') {
            e.preventDefault();
            document.getElementById('product-search').focus();
        }
        
        // F3 - Apply discount
        if (e.key === 'F3' && currentTransactionId) {
            e.preventDefault();
            showDiscountModal();
        }
        
        // F4 - Checkout
        if (e.key === 'F4' && currentTransactionId) {
            e.preventDefault();
            proceedToCheckout();
        }
        
        // Ctrl + Enter - Quick checkout
        if (e.ctrlKey && e.key === 'Enter' && currentTransactionId) {
            e.preventDefault();
            proceedToCheckout();
        }
        
        // Numbers 1-9 for quick category selection
        if (e.key >= '1' && e.key <= '9' && !e.ctrlKey && !e.altKey) {
            const categoryIndex = parseInt(e.key) - 1;
            const categoryButtons = document.querySelectorAll('.category-btn');
            if (categoryButtons[categoryIndex]) {
                categoryButtons[categoryIndex].click();
            }
        }
    });
}

function updateCartBadge() {
    const badge = document.getElementById('cart-badge');
    if (currentTransaction && currentTransaction.items && currentTransaction.items.length > 0) {
        const totalItems = currentTransaction.items.reduce((sum, item) => sum + item.quantity, 0);
        badge.textContent = totalItems;
        badge.classList.remove('hidden');
    } else {
        badge.classList.add('hidden');
    }
}

// Update the updateTransactionDisplay function to include cart badge update
const originalUpdateTransactionDisplay = updateTransactionDisplay;
updateTransactionDisplay = function() {
    originalUpdateTransactionDisplay();
    updateCartBadge();
};

// Utility functions
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function showLoading() {
    document.getElementById('loading-indicator').classList.remove('hidden');
}

function hideLoading() {
    document.getElementById('loading-indicator').classList.add('hidden');
}

function showSuccess(message) {
    // Create a temporary flash message
    const flashContainer = document.getElementById('flash-messages');
    const flashMessage = document.createElement('div');
    flashMessage.className = 'flash-message rounded-md p-4 shadow-lg max-w-md mx-auto bg-green-50 text-green-800 border border-green-200';
    flashMessage.innerHTML = `
        <div class="flex items-center justify-between">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium">${message}</p>
                </div>
            </div>
            <button type="button" class="ml-4 text-gray-400 hover:text-gray-600" onclick="this.parentElement.parentElement.remove()">
                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
    `;
    
    flashContainer.appendChild(flashMessage);
    
    // Auto-remove after 3 seconds
    setTimeout(() => {
        flashMessage.style.transition = 'opacity 0.5s ease-out';
        flashMessage.style.opacity = '0';
        setTimeout(() => flashMessage.remove(), 500);
    }, 3000);
}

function showError(message) {
    // Create a temporary flash message
    const flashContainer = document.getElementById('flash-messages');
    const flashMessage = document.createElement('div');
    flashMessage.className = 'flash-message rounded-md p-4 shadow-lg max-w-md mx-auto bg-red-50 text-red-800 border border-red-200';
    flashMessage.innerHTML = `
        <div class="flex items-center justify-between">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium">${message}</p>
                </div>
            </div>
            <button type="button" class="ml-4 text-gray-400 hover:text-gray-600" onclick="this.parentElement.parentElement.remove()">
                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
    `;
    
    flashContainer.appendChild(flashMessage);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        flashMessage.style.transition = 'opacity 0.5s ease-out';
        flashMessage.style.opacity = '0';
        setTimeout(() => flashMessage.remove(), 500);
    }, 5000);
}
</script>
{% endblock %}