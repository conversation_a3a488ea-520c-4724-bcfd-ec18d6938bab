/* Responsive Design Utilities for SaaS POS System */

/* Base responsive breakpoints following Tailwind CSS conventions */
/* sm: 640px, md: 768px, lg: 1024px, xl: 1280px, 2xl: 1536px */

/* Touch-friendly interactions */
.touch-target {
    min-height: 44px;
    min-width: 44px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

/* Mobile-first responsive utilities */
@media (max-width: 639px) {
    .mobile-hidden {
        display: none !important;
    }
    
    .mobile-full {
        width: 100% !important;
    }
    
    .mobile-stack {
        flex-direction: column !important;
    }
    
    .mobile-center {
        text-align: center !important;
    }
    
    /* Mobile-specific POS layout */
    .pos-mobile-layout {
        flex-direction: column;
    }
    
    .pos-mobile-products {
        order: 2;
        height: calc(100vh - 200px);
        overflow-y: auto;
    }
    
    .pos-mobile-cart {
        order: 1;
        position: fixed;
        top: 0;
        right: 0;
        bottom: 0;
        width: 100%;
        max-width: 400px;
        z-index: 50;
        transform: translateX(100%);
        transition: transform 0.3s ease-in-out;
    }
    
    .pos-mobile-cart.open {
        transform: translateX(0);
    }
    
    /* Mobile product grid */
    .product-grid-mobile {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.75rem;
    }
    
    /* Mobile form improvements */
    .form-mobile input,
    .form-mobile select,
    .form-mobile textarea {
        font-size: 16px; /* Prevents zoom on iOS */
        padding: 12px;
    }
    
    /* Mobile navigation */
    .nav-mobile {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background: white;
        border-top: 1px solid #e5e7eb;
        padding: 8px;
        z-index: 40;
    }
    
    .nav-mobile-item {
        flex: 1;
        text-align: center;
        padding: 8px 4px;
        font-size: 0.75rem;
    }
}

/* Tablet optimizations */
@media (min-width: 640px) and (max-width: 1023px) {
    .tablet-grid-3 {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .tablet-sidebar {
        width: 300px;
    }
    
    /* POS tablet layout */
    .pos-tablet-layout {
        display: flex;
        flex-direction: row;
    }
    
    .pos-tablet-products {
        flex: 2;
    }
    
    .pos-tablet-cart {
        flex: 1;
        min-width: 300px;
    }
}

/* Desktop optimizations */
@media (min-width: 1024px) {
    .desktop-grid-4 {
        grid-template-columns: repeat(4, 1fr);
    }
    
    .desktop-grid-5 {
        grid-template-columns: repeat(5, 1fr);
    }
    
    /* POS desktop layout */
    .pos-desktop-layout {
        display: flex;
        height: 100vh;
    }
    
    .pos-desktop-products {
        flex: 2;
        min-width: 600px;
    }
    
    .pos-desktop-cart {
        flex: 1;
        min-width: 400px;
        max-width: 500px;
    }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .high-dpi-image {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* Print styles */
@media print {
    .no-print {
        display: none !important;
    }
    
    .print-full-width {
        width: 100% !important;
    }
    
    /* Receipt printing */
    .receipt-print {
        width: 80mm;
        font-family: 'Courier New', monospace;
        font-size: 12px;
        line-height: 1.2;
    }
    
    .receipt-print .header {
        text-align: center;
        font-weight: bold;
        margin-bottom: 10px;
    }
    
    .receipt-print .item-line {
        display: flex;
        justify-content: space-between;
        margin: 2px 0;
    }
    
    .receipt-print .total-line {
        border-top: 1px dashed #000;
        margin-top: 5px;
        padding-top: 5px;
        font-weight: bold;
    }
}

/* Animation utilities */
.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

.slide-in-right {
    animation: slideInRight 0.3s ease-in-out;
}

.slide-out-right {
    animation: slideOutRight 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideInRight {
    from { transform: translateX(100%); }
    to { transform: translateX(0); }
}

@keyframes slideOutRight {
    from { transform: translateX(0); }
    to { transform: translateX(100%); }
}

/* Loading states */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f4f6;
    border-top: 4px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Focus styles for accessibility */
.focus-visible:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .nav-link {
        border-width: 2px;
    }
    
    .button {
        border-width: 2px;
    }
    
    .card {
        border-width: 2px;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Dark mode support (future enhancement) */
@media (prefers-color-scheme: dark) {
    .dark-mode-support {
        background-color: #1f2937;
        color: #f9fafb;
    }
    
    .dark-mode-support .card {
        background-color: #374151;
        border-color: #4b5563;
    }
    
    .dark-mode-support .nav {
        background-color: #111827;
        border-color: #374151;
    }
}

/* Keyboard navigation improvements */
.keyboard-nav:focus-within {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

/* Screen reader only content */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Skip links for accessibility */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: #000;
    color: #fff;
    padding: 8px;
    text-decoration: none;
    z-index: 100;
}

.skip-link:focus {
    top: 6px;
}

/* Responsive tables */
@media (max-width: 767px) {
    .responsive-table {
        display: block;
        overflow-x: auto;
        white-space: nowrap;
    }
    
    .responsive-table table {
        min-width: 600px;
    }
}

/* Responsive images */
.responsive-image {
    max-width: 100%;
    height: auto;
}

/* Container queries support (future enhancement) */
@supports (container-type: inline-size) {
    .container-responsive {
        container-type: inline-size;
    }
    
    @container (max-width: 400px) {
        .container-responsive .compact-layout {
            flex-direction: column;
        }
    }
}

/* Utility classes for common responsive patterns */
.flex-wrap-mobile {
    flex-wrap: wrap;
}

@media (min-width: 768px) {
    .flex-wrap-mobile {
        flex-wrap: nowrap;
    }
}

.text-responsive {
    font-size: 0.875rem;
}

@media (min-width: 640px) {
    .text-responsive {
        font-size: 1rem;
    }
}

@media (min-width: 1024px) {
    .text-responsive {
        font-size: 1.125rem;
    }
}

/* Grid responsive utilities */
.grid-responsive {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1rem;
}

@media (min-width: 640px) {
    .grid-responsive {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 768px) {
    .grid-responsive {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (min-width: 1024px) {
    .grid-responsive {
        grid-template-columns: repeat(4, 1fr);
    }
}