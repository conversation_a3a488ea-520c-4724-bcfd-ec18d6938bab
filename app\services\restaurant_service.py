"""Restaurant service for table management and order tracking."""

from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from app import db
from app.models.restaurant import Table, Order, OrderItem, TableStatus, OrderStatus
from app.models.transaction import Transaction, TransactionStatus
from app.models.business import BusinessSettings
from app.models.base import get_current_tenant


class RestaurantService:
    """Service class for restaurant operations."""
    
    @staticmethod
    def create_table(table_number: str, capacity: int = 4, **kwargs) -> Table:
        """Create a new table."""
        tenant_id = get_current_tenant()
        if not tenant_id:
            raise ValueError("No tenant context set")
        
        # Check if table number already exists
        existing_table = Table.query.filter_by(
            tenant_id=tenant_id,
            table_number=table_number
        ).first()
        
        if existing_table:
            raise ValueError(f"Table {table_number} already exists")
        
        table = Table.create_table(
            tenant_id=tenant_id,
            table_number=table_number,
            capacity=capacity,
            **kwargs
        )
        
        return table
    
    @staticmethod
    def get_table_layout(section: Optional[str] = None) -> List[Table]:
        """Get table layout for display."""
        tenant_id = get_current_tenant()
        if not tenant_id:
            raise ValueError("No tenant context set")
        
        query = Table.query.filter_by(tenant_id=tenant_id)
        if section:
            query = query.filter_by(section=section)
        
        return query.order_by(Table.table_number).all()
    
    @staticmethod
    def get_table_status_summary() -> Dict[str, int]:
        """Get summary of table statuses."""
        tenant_id = get_current_tenant()
        if not tenant_id:
            raise ValueError("No tenant context set")
        
        tables = Table.query.filter_by(tenant_id=tenant_id).all()
        
        summary = {
            'available': 0,
            'occupied': 0,
            'reserved': 0,
            'cleaning': 0,
            'out_of_order': 0,
            'total': len(tables)
        }
        
        for table in tables:
            summary[table.status.value] += 1
        
        return summary
    
    @staticmethod
    def seat_party(table_number: str, party_size: int, estimated_duration: Optional[int] = None) -> Table:
        """Seat a party at a table."""
        tenant_id = get_current_tenant()
        if not tenant_id:
            raise ValueError("No tenant context set")
        
        table = Table.query.filter_by(
            tenant_id=tenant_id,
            table_number=table_number
        ).first()
        
        if not table:
            raise ValueError(f"Table {table_number} not found")
        
        if party_size > table.capacity:
            raise ValueError(f"Party size ({party_size}) exceeds table capacity ({table.capacity})")
        
        return table.occupy_table(party_size, estimated_duration)
    
    @staticmethod
    def clear_table(table_number: str) -> Table:
        """Clear a table and make it available."""
        tenant_id = get_current_tenant()
        if not tenant_id:
            raise ValueError("No tenant context set")
        
        table = Table.query.filter_by(
            tenant_id=tenant_id,
            table_number=table_number
        ).first()
        
        if not table:
            raise ValueError(f"Table {table_number} not found")
        
        return table.clear_table()
    
    @staticmethod
    def reserve_table(table_number: str, customer_name: str, phone: Optional[str] = None, 
                     notes: Optional[str] = None, reservation_time: Optional[datetime] = None) -> Table:
        """Reserve a table."""
        tenant_id = get_current_tenant()
        if not tenant_id:
            raise ValueError("No tenant context set")
        
        table = Table.query.filter_by(
            tenant_id=tenant_id,
            table_number=table_number
        ).first()
        
        if not table:
            raise ValueError(f"Table {table_number} not found")
        
        return table.reserve_table(customer_name, phone, notes, reservation_time)
    
    @staticmethod
    def create_order(table_number: Optional[str] = None, order_type: str = 'dine_in',
                    customer_name: Optional[str] = None, customer_phone: Optional[str] = None,
                    user_id: Optional[int] = None, special_instructions: Optional[str] = None) -> Order:
        """Create a new order."""
        tenant_id = get_current_tenant()
        if not tenant_id:
            raise ValueError("No tenant context set")
        
        if not user_id:
            raise ValueError("User ID is required")
        
        # Get table if specified
        table = None
        if table_number:
            table = Table.query.filter_by(
                tenant_id=tenant_id,
                table_number=table_number
            ).first()
            
            if not table:
                raise ValueError(f"Table {table_number} not found")
        
        # Generate order number
        order_number = Order.generate_order_number(tenant_id)
        
        order = Order(
            tenant_id=tenant_id,
            order_number=order_number,
            table_id=table.id if table else None,
            user_id=user_id,
            order_type=order_type,
            customer_name=customer_name,
            customer_phone=customer_phone,
            special_instructions=special_instructions
        )
        
        return order.save()
    
    @staticmethod
    def add_item_to_order(order_id: int, product_id: int, quantity: int = 1,
                         special_instructions: Optional[str] = None) -> OrderItem:
        """Add an item to an order."""
        tenant_id = get_current_tenant()
        if not tenant_id:
            raise ValueError("No tenant context set")
        
        from app.models.product import Product
        
        order = Order.query.filter_by(tenant_id=tenant_id, id=order_id).first()
        if not order:
            raise ValueError("Order not found")
        
        product = Product.query.filter_by(tenant_id=tenant_id, id=product_id).first()
        if not product:
            raise ValueError("Product not found")
        
        return order.add_item(product, quantity, special_instructions)
    
    @staticmethod
    def start_order_preparation(order_id: int) -> Order:
        """Start preparation of an order."""
        tenant_id = get_current_tenant()
        if not tenant_id:
            raise ValueError("No tenant context set")
        
        order = Order.query.filter_by(tenant_id=tenant_id, id=order_id).first()
        if not order:
            raise ValueError("Order not found")
        
        return order.start_preparation()
    
    @staticmethod
    def mark_order_ready(order_id: int) -> Order:
        """Mark an order as ready."""
        tenant_id = get_current_tenant()
        if not tenant_id:
            raise ValueError("No tenant context set")
        
        order = Order.query.filter_by(tenant_id=tenant_id, id=order_id).first()
        if not order:
            raise ValueError("Order not found")
        
        return order.mark_ready()
    
    @staticmethod
    def serve_order(order_id: int) -> Order:
        """Mark an order as served."""
        tenant_id = get_current_tenant()
        if not tenant_id:
            raise ValueError("No tenant context set")
        
        order = Order.query.filter_by(tenant_id=tenant_id, id=order_id).first()
        if not order:
            raise ValueError("Order not found")
        
        return order.mark_served()
    
    @staticmethod
    def get_kitchen_display_orders(limit: int = 20) -> List[Order]:
        """Get orders for kitchen display."""
        tenant_id = get_current_tenant()
        if not tenant_id:
            raise ValueError("No tenant context set")
        
        return Order.get_kitchen_display_orders(tenant_id, limit)
    
    @staticmethod
    def get_table_orders(table_number: str) -> List[Order]:
        """Get all orders for a specific table."""
        tenant_id = get_current_tenant()
        if not tenant_id:
            raise ValueError("No tenant context set")
        
        table = Table.query.filter_by(
            tenant_id=tenant_id,
            table_number=table_number
        ).first()
        
        if not table:
            raise ValueError(f"Table {table_number} not found")
        
        return Order.get_orders_by_table(tenant_id, table.id)
    
    @staticmethod
    def get_overdue_orders() -> List[Order]:
        """Get orders that are overdue."""
        tenant_id = get_current_tenant()
        if not tenant_id:
            raise ValueError("No tenant context set")
        
        orders = Order.query.filter(
            Order.tenant_id == tenant_id,
            Order.status.in_([OrderStatus.PENDING, OrderStatus.PREPARING])
        ).all()
        
        return [order for order in orders if order.is_overdue()]
    
    @staticmethod
    def get_occupied_tables_with_duration() -> List[Dict[str, Any]]:
        """Get occupied tables with occupancy duration."""
        tenant_id = get_current_tenant()
        if not tenant_id:
            raise ValueError("No tenant context set")
        
        occupied_tables = Table.get_occupied_tables(tenant_id)
        
        result = []
        for table in occupied_tables:
            result.append({
                'table': table,
                'duration_minutes': table.get_occupancy_duration(),
                'is_overdue': table.is_overdue(),
                'estimated_available': table.get_estimated_available_time(),
                'active_orders': len(table.get_current_orders())
            })
        
        return result
    
    @staticmethod
    def create_transaction_from_order(order_id: int, user_id: int) -> Transaction:
        """Create a transaction from a restaurant order."""
        tenant_id = get_current_tenant()
        if not tenant_id:
            raise ValueError("No tenant context set")
        
        order = Order.query.filter_by(tenant_id=tenant_id, id=order_id).first()
        if not order:
            raise ValueError("Order not found")
        
        # Create transaction
        transaction = Transaction(
            tenant_id=tenant_id,
            user_id=user_id,
            customer_name=order.customer_name,
            customer_phone=order.customer_phone,
            table_number=order.table.table_number if order.table else None,
            order_type=order.order_type,
            notes=order.special_instructions
        )
        transaction.save()
        
        # Add order items to transaction
        from app.models.product import Product
        for order_item in order.items:
            product = Product.query.get(order_item.product_id)
            if product:
                transaction.add_item(product, order_item.quantity)
        
        # Link order to transaction
        order.transaction_id = transaction.id
        order.save()
        
        return transaction
    
    @staticmethod
    def setup_restaurant_tables(business_settings: BusinessSettings) -> List[Table]:
        """Set up default tables for a restaurant based on business settings."""
        tenant_id = get_current_tenant()
        if not tenant_id:
            raise ValueError("No tenant context set")
        
        restaurant_settings = business_settings.get_restaurant_settings()
        max_tables = restaurant_settings.get('max_tables', 20)
        table_prefix = restaurant_settings.get('table_prefix', 'T')
        
        tables = []
        
        # Create default tables
        for i in range(1, min(max_tables + 1, 21)):  # Limit to 20 for initial setup
            table_number = f"{table_prefix}{i}"
            
            # Check if table already exists
            existing_table = Table.query.filter_by(
                tenant_id=tenant_id,
                table_number=table_number
            ).first()
            
            if not existing_table:
                table = Table.create_table(
                    tenant_id=tenant_id,
                    table_number=table_number,
                    capacity=4,  # Default capacity
                    section="Main Dining"
                )
                tables.append(table)
        
        return tables
    
    @staticmethod
    def get_restaurant_analytics(date: Optional[datetime] = None) -> Dict[str, Any]:
        """Get restaurant analytics for a specific date."""
        tenant_id = get_current_tenant()
        if not tenant_id:
            raise ValueError("No tenant context set")
        
        if date is None:
            date = datetime.now().date()
        
        start_date = datetime.combine(date, datetime.min.time())
        end_date = datetime.combine(date, datetime.max.time())
        
        # Get completed orders for the date
        orders = Order.query.filter(
            Order.tenant_id == tenant_id,
            Order.status == OrderStatus.SERVED,
            Order.served_at.between(start_date, end_date)
        ).all()
        
        # Calculate metrics
        total_orders = len(orders)
        total_prep_time = sum(order.get_preparation_time() or 0 for order in orders)
        avg_prep_time = total_prep_time / total_orders if total_orders > 0 else 0
        
        # Table turnover
        table_sessions = db.session.query(Table).filter(
            Table.tenant_id == tenant_id,
            Table.occupied_since.between(start_date, end_date)
        ).count()
        
        return {
            'date': date,
            'total_orders': total_orders,
            'average_preparation_time': round(avg_prep_time, 1),
            'table_sessions': table_sessions,
            'orders_by_type': {
                'dine_in': len([o for o in orders if o.order_type == 'dine_in']),
                'takeout': len([o for o in orders if o.order_type == 'takeout']),
                'delivery': len([o for o in orders if o.order_type == 'delivery'])
            }
        }