"""Unit tests for business configuration models."""

import pytest
from app import create_app, db
from app.models import Tenant, BusinessSettings, BusinessTypeTemplate


@pytest.fixture
def app():
    """Create application for testing."""
    from config import TestingConfig
    app = create_app(TestingConfig)
    with app.app_context():
        db.create_all()
        yield app
        db.drop_all()


@pytest.fixture
def client(app):
    """Create test client."""
    return app.test_client()


@pytest.fixture
def tenant(app):
    """Create test tenant."""
    tenant = Tenant(
        name='Test Business',
        business_type='retail',
        email='<EMAIL>'
    )
    return tenant.save()


class TestBusinessSettings:
    """Test BusinessSettings model."""
    
    def test_create_business_settings(self, app, tenant):
        """Test creating business settings."""
        settings = BusinessSettings(
            tenant_id=tenant.id,
            business_name='Test Store',
            business_type='retail'
        )
        settings.save()
        
        assert settings.id is not None
        assert settings.business_name == 'Test Store'
        assert settings.business_type == 'retail'
        assert settings.enable_inventory_alerts is True  # Default
        assert settings.enable_table_management is False  # Default
    
    def test_business_type_display(self, app, tenant):
        """Test business type display names."""
        settings = BusinessSettings(
            tenant_id=tenant.id,
            business_name='Test Store',
            business_type='restaurant'
        )
        
        assert settings.get_business_type_display() == 'Restaurant'
        
        settings.business_type = 'retail'
        assert settings.get_business_type_display() == 'Retail Store'
        
        settings.business_type = 'service'
        assert settings.get_business_type_display() == 'Service Business'
    
    def test_business_type_checks(self, app, tenant):
        """Test business type checking methods."""
        settings = BusinessSettings(
            tenant_id=tenant.id,
            business_name='Test Store'
        )
        
        # Test restaurant type
        settings.business_type = 'restaurant'
        assert settings.is_restaurant_type() is True
        assert settings.is_retail_type() is False
        assert settings.is_service_type() is False
        
        # Test retail type
        settings.business_type = 'retail'
        assert settings.is_restaurant_type() is False
        assert settings.is_retail_type() is True
        assert settings.is_service_type() is False
        
        # Test service type
        settings.business_type = 'service'
        assert settings.is_restaurant_type() is False
        assert settings.is_retail_type() is False
        assert settings.is_service_type() is True
    
    def test_enabled_features(self, app, tenant):
        """Test getting enabled features."""
        settings = BusinessSettings(
            tenant_id=tenant.id,
            business_name='Test Store',
            enable_table_management=True,
            enable_inventory_alerts=True,
            enable_barcode_scanning=True
        )
        
        features = settings.get_enabled_features()
        assert 'Table Management' in features
        assert 'Inventory Alerts' in features
        assert 'Barcode Scanning' in features
        assert 'Kitchen Display' not in features
    
    def test_apply_restaurant_template(self, app, tenant):
        """Test applying restaurant business type template."""
        settings = BusinessSettings(
            tenant_id=tenant.id,
            business_name='Test Restaurant'
        )
        
        settings.apply_business_type_template('restaurant')
        
        assert settings.business_type == 'restaurant'
        assert settings.enable_table_management is True
        assert settings.enable_kitchen_display is True
        assert settings.enable_order_tracking is True
        assert settings.enable_barcode_scanning is False
        assert settings.enable_inventory_alerts is True  # Always enabled
        
        # Check restaurant settings
        restaurant_settings = settings.get_restaurant_settings()
        assert restaurant_settings['max_tables'] == 50
        assert restaurant_settings['table_prefix'] == 'T'
        assert restaurant_settings['order_timeout_minutes'] == 30
    
    def test_apply_retail_template(self, app, tenant):
        """Test applying retail business type template."""
        settings = BusinessSettings(
            tenant_id=tenant.id,
            business_name='Test Store'
        )
        
        settings.apply_business_type_template('retail')
        
        assert settings.business_type == 'retail'
        assert settings.enable_barcode_scanning is True
        assert settings.enable_loyalty_program is True
        assert settings.enable_table_management is False
        assert settings.enable_inventory_alerts is True  # Always enabled
        
        # Check retail settings
        retail_settings = settings.get_retail_settings()
        assert retail_settings['enable_product_variants'] is True
        assert retail_settings['barcode_format'] == 'UPC'
    
    def test_apply_service_template(self, app, tenant):
        """Test applying service business type template."""
        settings = BusinessSettings(
            tenant_id=tenant.id,
            business_name='Test Service'
        )
        
        settings.apply_business_type_template('service')
        
        assert settings.business_type == 'service'
        assert settings.enable_appointment_scheduling is True
        assert settings.enable_service_tracking is True
        assert settings.enable_table_management is False
        assert settings.enable_inventory_alerts is True  # Always enabled
        
        # Check service settings
        service_settings = settings.get_service_settings()
        assert service_settings['appointment_duration_minutes'] == 60
        assert service_settings['advance_booking_days'] == 30
    
    def test_update_restaurant_settings(self, app, tenant):
        """Test updating restaurant-specific settings."""
        settings = BusinessSettings(
            tenant_id=tenant.id,
            business_name='Test Restaurant'
        )
        settings.apply_business_type_template('restaurant')
        
        # Update settings
        settings.update_restaurant_settings(
            max_tables=30,
            enable_table_qr_codes=True
        )
        
        restaurant_settings = settings.get_restaurant_settings()
        assert restaurant_settings['max_tables'] == 30
        assert restaurant_settings['enable_table_qr_codes'] is True
        assert restaurant_settings['table_prefix'] == 'T'  # Should keep existing
    
    def test_create_for_tenant(self, app, tenant):
        """Test creating business settings for tenant."""
        settings = BusinessSettings.create_for_tenant(
            tenant.id,
            'My Restaurant',
            'restaurant'
        )
        
        assert settings.tenant_id == tenant.id
        assert settings.business_name == 'My Restaurant'
        assert settings.business_type == 'restaurant'
        assert settings.enable_table_management is True
        assert settings.id is not None  # Should be saved
    
    def test_get_for_tenant(self, app, tenant):
        """Test getting business settings for tenant."""
        # Create settings
        original_settings = BusinessSettings.create_for_tenant(
            tenant.id,
            'My Store',
            'retail'
        )
        
        # Retrieve settings
        retrieved_settings = BusinessSettings.get_for_tenant(tenant.id)
        
        assert retrieved_settings is not None
        assert retrieved_settings.id == original_settings.id
        assert retrieved_settings.business_name == 'My Store'
    
    def test_get_or_create_for_tenant(self, app, tenant):
        """Test get or create business settings for tenant."""
        # First call should create
        settings1 = BusinessSettings.get_or_create_for_tenant(
            tenant.id,
            'My Business',
            'service'
        )
        
        assert settings1.business_name == 'My Business'
        assert settings1.business_type == 'service'
        
        # Second call should retrieve existing
        settings2 = BusinessSettings.get_or_create_for_tenant(tenant.id)
        
        assert settings2.id == settings1.id
        assert settings2.business_name == 'My Business'


class TestBusinessTypeTemplate:
    """Test BusinessTypeTemplate class."""
    
    def test_get_all_templates(self):
        """Test getting all business type templates."""
        templates = BusinessTypeTemplate.get_all_templates()
        
        assert len(templates) == 4
        template_codes = [t['code'] for t in templates]
        assert 'restaurant' in template_codes
        assert 'cafe' in template_codes
        assert 'retail' in template_codes
        assert 'service' in template_codes
    
    def test_get_restaurant_template(self):
        """Test getting restaurant template."""
        template = BusinessTypeTemplate.get_template('restaurant')
        
        assert template is not None
        assert template['name'] == 'Restaurant'
        assert template['code'] == 'restaurant'
        assert 'enable_table_management' in template['features']
        assert 'enable_kitchen_display' in template['features']
        assert template['settings']['max_tables'] == 50
    
    def test_get_retail_template(self):
        """Test getting retail template."""
        template = BusinessTypeTemplate.get_template('retail')
        
        assert template is not None
        assert template['name'] == 'Retail Store'
        assert template['code'] == 'retail'
        assert 'enable_barcode_scanning' in template['features']
        assert template['settings']['barcode_format'] == 'UPC'
    
    def test_get_service_template(self):
        """Test getting service template."""
        template = BusinessTypeTemplate.get_template('service')
        
        assert template is not None
        assert template['name'] == 'Service Business'
        assert template['code'] == 'service'
        assert 'enable_appointment_scheduling' in template['features']
        assert template['settings']['appointment_duration_minutes'] == 60
    
    def test_get_nonexistent_template(self):
        """Test getting non-existent template."""
        template = BusinessTypeTemplate.get_template('nonexistent')
        assert template is None