"""Authentication routes for SaaS POS System."""

from flask import Blueprint, render_template, request, redirect, url_for, flash, session, jsonify
from flask_login import login_required, current_user
from urllib.parse import urlparse

from app.services.auth_service import (
    AuthService, 
    AuthenticationError, 
    InvalidCredentialsError,
    AccountLockedError,
    TenantInactiveError
)
from app.forms.auth_forms import (
    LoginForm, RegisterForm, ForgotPasswordForm, ResetPasswordForm,
    ChangePasswordForm, ProfileForm
)
from app.utils.decorators import validate_form_request, rate_limit

bp = Blueprint('auth', __name__)


@bp.route('/login', methods=['GET', 'POST'])
@rate_limit(max_requests=10, window=300)  # 10 attempts per 5 minutes
def login():
    """User login route."""
    if current_user.is_authenticated:
        return redirect(url_for('main.dashboard'))
    
    form = LoginForm()
    
    if form.validate_on_submit():
        try:
            result = AuthService.login(
                form.email.data, 
                form.password.data, 
                form.business_name.data, 
                form.remember_me.data
            )
            
            if result['success']:
                flash(f'Welcome back, {result["user"]["full_name"]}!', 'success')
                
                # Redirect to next page or dashboard
                next_page = request.args.get('next')
                if not next_page or urlparse(next_page).netloc != '':
                    next_page = url_for('main.dashboard')
                return redirect(next_page)
            
        except InvalidCredentialsError:
            flash('Invalid email or password', 'error')
        except AccountLockedError:
            flash('Account is temporarily locked due to failed login attempts. Please try again later.', 'error')
        except TenantInactiveError:
            flash('Account is inactive or subscription has expired. Please contact support.', 'error')
        except AuthenticationError:
            flash('Login failed. Please try again.', 'error')
        except Exception:
            flash('An unexpected error occurred. Please try again.', 'error')
    
    return render_template('auth/login.html', form=form)


@bp.route('/logout')
@login_required
def logout():
    """User logout route."""
    result = AuthService.logout()
    
    if result['success']:
        flash('You have been logged out successfully.', 'info')
    else:
        flash('Logout failed. Please try again.', 'error')
    
    return redirect(url_for('auth.login'))


@bp.route('/register', methods=['GET', 'POST'])
def register():
    """User registration route."""
    if current_user.is_authenticated:
        return redirect(url_for('main.dashboard'))
    
    form = RegisterForm()
    
    if form.validate_on_submit():
        # Attempt registration
        result = AuthService.register(
            email=form.email.data,
            password=form.password.data,
            first_name=form.first_name.data,
            last_name=form.last_name.data,
            tenant_name=form.tenant_name.data,
            business_type=form.business_type.data
        )
        
        if result['success']:
            flash('Registration successful! Please log in with your new account.', 'success')
            # Store tenant_id in session for convenience
            session['registration_tenant_id'] = result['tenant']['id']
            return redirect(url_for('auth.login'))
        else:
            flash(result['error'], 'error')
    
    return render_template('auth/register.html', form=form)


@bp.route('/verify-email/<token>')
def verify_email(token):
    """Email verification route."""
    result = AuthService.verify_email(token)
    
    if result['success']:
        flash('Email verified successfully! You can now log in.', 'success')
    else:
        flash('Email verification failed. The link may be invalid or expired.', 'error')
    
    return redirect(url_for('auth.login'))


@bp.route('/forgot-password', methods=['GET', 'POST'])
def forgot_password():
    """Forgot password route."""
    if current_user.is_authenticated:
        return redirect(url_for('main.dashboard'))
    
    form = ForgotPasswordForm()
    
    if form.validate_on_submit():
        result = AuthService.request_password_reset(form.email.data, form.business_name.data)
        
        # Always show success message for security (don't reveal if email exists)
        flash('If your email is registered, you will receive password reset instructions.', 'info')
        return redirect(url_for('auth.login'))
    
    return render_template('auth/forgot_password.html', form=form)


@bp.route('/reset-password/<token>', methods=['GET', 'POST'])
def reset_password(token):
    """Password reset route."""
    if current_user.is_authenticated:
        return redirect(url_for('main.dashboard'))
    
    form = ResetPasswordForm()
    
    if form.validate_on_submit():
        result = AuthService.reset_password(token, form.password.data)
        
        if result['success']:
            flash('Password reset successfully! You can now log in with your new password.', 'success')
            return redirect(url_for('auth.login'))
        else:
            flash(result['error'], 'error')
    
    return render_template('auth/reset_password.html', form=form, token=token)


@bp.route('/change-password', methods=['GET', 'POST'])
@login_required
def change_password():
    """Change password route for authenticated users."""
    form = ChangePasswordForm()
    
    if form.validate_on_submit():
        result = AuthService.change_password(form.current_password.data, form.new_password.data)
        
        if result['success']:
            flash('Password changed successfully!', 'success')
            return redirect(url_for('main.dashboard'))
        else:
            flash(result['error'], 'error')
    
    return render_template('auth/change_password.html', form=form)


@bp.route('/profile')
@login_required
def profile():
    """User profile route."""
    user_info = AuthService.get_current_user_info()
    return render_template('auth/profile.html', user=user_info)


# API endpoints for AJAX requests
@bp.route('/api/check-email', methods=['POST'])
def check_email():
    """API endpoint to check if email is available."""
    data = request.get_json()
    email = data.get('email', '').strip()
    
    if not email:
        return jsonify({'available': False, 'message': 'Email is required'})
    
    # This is a simplified check - in a real app you might want more sophisticated validation
    from app.models.user import User
    existing_user = User.query.filter_by(email=email).first()
    
    if existing_user:
        return jsonify({'available': False, 'message': 'Email is already registered'})
    
    return jsonify({'available': True, 'message': 'Email is available'})


@bp.route('/api/user-info')
@login_required
def api_user_info():
    """API endpoint to get current user information."""
    user_info = AuthService.get_current_user_info()
    return jsonify(user_info)