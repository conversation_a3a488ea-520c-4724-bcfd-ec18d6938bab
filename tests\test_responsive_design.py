"""
Tests for responsive design functionality
"""
import pytest
from flask import url_for
import re


class TestResponsiveDesign:
    """Test responsive design template rendering"""
    
    def test_responsive_css_included(self, client):
        """Test that responsive CSS is included in templates"""
        response = client.get('/login')
        html = response.get_data(as_text=True)
        
        # Check that responsive CSS is linked
        assert 'responsive.css' in html, "Responsive CSS should be included"
        
    def test_navigation_responsive_classes(self, client, auth_user):
        """Test navigation has responsive classes"""
        with client.session_transaction() as sess:
            sess['user_id'] = auth_user.id
            sess['tenant_id'] = auth_user.tenant_id
        
        response = client.get('/dashboard')
        html = response.get_data(as_text=True)
        
        # Check for responsive navigation elements
        assert 'mobile-menu-button' in html, "Should have mobile menu button"
        assert 'md:hidden' in html, "Should have mobile-hidden classes"
        assert 'hidden md:flex' in html or 'md:ml-6 md:flex' in html, "Should have desktop-visible classes"
        
    def test_pos_mobile_elements(self, client, auth_user):
        """Test POS has mobile-specific elements"""
        with client.session_transaction() as sess:
            sess['user_id'] = auth_user.id
            sess['tenant_id'] = auth_user.tenant_id
        
        response = client.get('/pos')
        if response.status_code == 200:  # Only test if POS is accessible
            html = response.get_data(as_text=True)
            
            # Check for mobile cart toggle
            assert 'mobile-cart-toggle' in html, "Should have mobile cart toggle"
            assert 'lg:hidden' in html, "Should have desktop-hidden elements"
            assert 'translate-x-full' in html, "Should have mobile slide animations"
    
    def test_responsive_grid_classes(self, client, auth_user):
        """Test responsive grid classes are present"""
        with client.session_transaction() as sess:
            sess['user_id'] = auth_user.id
            sess['tenant_id'] = auth_user.tenant_id
        
        response = client.get('/dashboard')
        html = response.get_data(as_text=True)
        
        # Check for responsive grid classes
        assert re.search(r'grid-cols-\d+', html), "Should have grid column classes"
        assert re.search(r'md:grid-cols-\d+', html), "Should have tablet grid classes"
        assert re.search(r'lg:grid-cols-\d+', html), "Should have desktop grid classes"
    
    def test_touch_target_classes(self, client, auth_user):
        """Test touch target classes are applied"""
        with client.session_transaction() as sess:
            sess['user_id'] = auth_user.id
            sess['tenant_id'] = auth_user.tenant_id
        
        response = client.get('/dashboard')
        html = response.get_data(as_text=True)
        
        # Check for touch target classes
        assert 'touch-target' in html, "Should have touch target classes"


class TestKeyboardShortcuts:
    """Test keyboard shortcuts functionality"""
    
    def test_global_shortcuts(self, client, auth_user):
        """Test global keyboard shortcuts are properly defined"""
        with client.session_transaction() as sess:
            sess['user_id'] = auth_user.id
            sess['tenant_id'] = auth_user.tenant_id
        
        response = client.get('/dashboard')
        assert response.status_code == 200
        
        # Check that keyboard shortcut JavaScript is included
        html = response.get_data(as_text=True)
        assert 'Alt+H' in html, "Alt+H shortcut should be documented"
        assert 'Alt+P' in html, "Alt+P shortcut should be documented"
        assert 'Alt+I' in html, "Alt+I shortcut should be documented"
        assert 'Alt+R' in html, "Alt+R shortcut should be documented"
    
    def test_pos_shortcuts(self, client, auth_user):
        """Test POS-specific keyboard shortcuts"""
        with client.session_transaction() as sess:
            sess['user_id'] = auth_user.id
            sess['tenant_id'] = auth_user.tenant_id
        
        response = client.get('/pos')
        assert response.status_code == 200
        
        html = response.get_data(as_text=True)
        # Check POS shortcuts are defined
        assert 'F1' in html, "F1 shortcut should be available"
        assert 'F2' in html, "F2 shortcut should be available"
        assert 'F3' in html, "F3 shortcut should be available"
        assert 'F4' in html, "F4 shortcut should be available"


class TestAccessibility:
    """Test accessibility features"""
    
    def test_aria_labels(self, client, auth_user):
        """Test ARIA labels are present"""
        with client.session_transaction() as sess:
            sess['user_id'] = auth_user.id
            sess['tenant_id'] = auth_user.tenant_id
        
        response = client.get('/dashboard')
        html = response.get_data(as_text=True)
        
        # Check for ARIA attributes
        assert 'aria-expanded' in html, "Should have aria-expanded attributes"
        assert 'aria-haspopup' in html, "Should have aria-haspopup attributes"
        assert 'role=' in html, "Should have role attributes"
    
    def test_semantic_html(self, client, auth_user):
        """Test semantic HTML structure"""
        with client.session_transaction() as sess:
            sess['user_id'] = auth_user.id
            sess['tenant_id'] = auth_user.tenant_id
        
        response = client.get('/dashboard')
        html = response.get_data(as_text=True)
        
        # Check for semantic elements
        assert '<nav' in html, "Should have nav element"
        assert '<main' in html, "Should have main element"
        assert '<button' in html, "Should have button elements"
        assert 'sr-only' in html, "Should have screen reader only text"
    
    def test_focus_management(self, client, auth_user):
        """Test focus management in JavaScript"""
        with client.session_transaction() as sess:
            sess['user_id'] = auth_user.id
            sess['tenant_id'] = auth_user.tenant_id
        
        response = client.get('/pos')
        html = response.get_data(as_text=True)
        
        # Check focus management code
        assert 'focus()' in html, "Should have focus management"
        assert 'focus:' in html, "Should have focus styles"