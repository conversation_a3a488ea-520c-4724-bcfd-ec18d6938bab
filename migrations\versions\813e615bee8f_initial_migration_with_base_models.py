"""Initial migration with base models

Revision ID: 813e615bee8f
Revises: 
Create Date: 2025-07-16 10:08:16.728658

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '813e615bee8f'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('tenant',
    sa.Column('name', sa.String(length=100), nullable=False),
    sa.Column('business_type', sa.String(length=50), nullable=False),
    sa.Column('subscription_status', sa.String(length=20), nullable=False),
    sa.Column('email', sa.String(length=120), nullable=True),
    sa.Column('phone', sa.String(length=20), nullable=True),
    sa.Column('address', sa.Text(), nullable=True),
    sa.Column('subscription_plan', sa.String(length=50), nullable=False),
    sa.Column('subscription_expires', sa.DateTime(), nullable=True),
    sa.Column('timezone', sa.String(length=50), nullable=False),
    sa.Column('currency', sa.String(length=3), nullable=False),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('business_settings',
    sa.Column('business_type', sa.String(length=50), nullable=False),
    sa.Column('business_name', sa.String(length=100), nullable=False),
    sa.Column('enable_table_management', sa.Boolean(), nullable=False),
    sa.Column('enable_kitchen_display', sa.Boolean(), nullable=False),
    sa.Column('enable_order_tracking', sa.Boolean(), nullable=False),
    sa.Column('enable_barcode_scanning', sa.Boolean(), nullable=False),
    sa.Column('enable_appointment_scheduling', sa.Boolean(), nullable=False),
    sa.Column('enable_service_tracking', sa.Boolean(), nullable=False),
    sa.Column('enable_inventory_alerts', sa.Boolean(), nullable=False),
    sa.Column('enable_loyalty_program', sa.Boolean(), nullable=False),
    sa.Column('restaurant_settings', postgresql.JSON(astext_type=Text()), nullable=True),
    sa.Column('retail_settings', postgresql.JSON(astext_type=Text()), nullable=True),
    sa.Column('service_settings', postgresql.JSON(astext_type=Text()), nullable=True),
    sa.Column('default_tax_rate', sa.Numeric(precision=5, scale=4), nullable=False),
    sa.Column('currency', sa.String(length=3), nullable=False),
    sa.Column('timezone', sa.String(length=50), nullable=False),
    sa.Column('receipt_header', sa.Text(), nullable=True),
    sa.Column('receipt_footer', sa.Text(), nullable=True),
    sa.Column('auto_print_receipts', sa.Boolean(), nullable=False),
    sa.Column('low_stock_threshold', sa.Integer(), nullable=False),
    sa.Column('tenant_id', sa.Integer(), nullable=False),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['tenant_id'], ['tenant.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('business_settings', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_business_settings_tenant_id'), ['tenant_id'], unique=False)

    op.create_table('category',
    sa.Column('name', sa.String(length=100), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('parent_id', sa.Integer(), nullable=True),
    sa.Column('color', sa.String(length=7), nullable=True),
    sa.Column('icon', sa.String(length=50), nullable=True),
    sa.Column('sort_order', sa.Integer(), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('tenant_id', sa.Integer(), nullable=False),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['parent_id'], ['category.id'], ),
    sa.ForeignKeyConstraint(['tenant_id'], ['tenant.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('category', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_category_tenant_id'), ['tenant_id'], unique=False)

    op.create_table('user',
    sa.Column('email', sa.String(length=120), nullable=False),
    sa.Column('password_hash', sa.String(length=255), nullable=False),
    sa.Column('first_name', sa.String(length=50), nullable=False),
    sa.Column('last_name', sa.String(length=50), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('role', sa.String(length=50), nullable=False),
    sa.Column('tenant_id', sa.Integer(), nullable=False),
    sa.Column('last_login', sa.DateTime(), nullable=True),
    sa.Column('login_count', sa.Integer(), nullable=False),
    sa.Column('failed_login_attempts', sa.Integer(), nullable=False),
    sa.Column('locked_until', sa.DateTime(), nullable=True),
    sa.Column('email_verified', sa.Boolean(), nullable=False),
    sa.Column('email_verification_token', sa.String(length=100), nullable=True),
    sa.Column('password_reset_token', sa.String(length=100), nullable=True),
    sa.Column('password_reset_expires', sa.DateTime(), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['tenant_id'], ['tenant.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('email', 'tenant_id', name='unique_email_per_tenant')
    )
    with op.batch_alter_table('user', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_user_email'), ['email'], unique=False)
        batch_op.create_index(batch_op.f('ix_user_tenant_id'), ['tenant_id'], unique=False)

    op.create_table('product',
    sa.Column('name', sa.String(length=200), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('sku', sa.String(length=100), nullable=True),
    sa.Column('barcode', sa.String(length=100), nullable=True),
    sa.Column('category_id', sa.Integer(), nullable=True),
    sa.Column('cost_price', sa.Numeric(precision=10, scale=2), nullable=False),
    sa.Column('selling_price', sa.Numeric(precision=10, scale=2), nullable=False),
    sa.Column('current_stock', sa.Integer(), nullable=False),
    sa.Column('minimum_stock', sa.Integer(), nullable=False),
    sa.Column('maximum_stock', sa.Integer(), nullable=True),
    sa.Column('track_inventory', sa.Boolean(), nullable=False),
    sa.Column('allow_negative_stock', sa.Boolean(), nullable=False),
    sa.Column('unit_of_measure', sa.String(length=20), nullable=False),
    sa.Column('weight', sa.Numeric(precision=8, scale=3), nullable=True),
    sa.Column('tax_rate', sa.Numeric(precision=5, scale=4), nullable=False),
    sa.Column('tax_inclusive', sa.Boolean(), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('is_featured', sa.Boolean(), nullable=False),
    sa.Column('image_url', sa.String(length=500), nullable=True),
    sa.Column('color', sa.String(length=7), nullable=True),
    sa.Column('sort_order', sa.Integer(), nullable=False),
    sa.Column('tenant_id', sa.Integer(), nullable=False),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['category_id'], ['category.id'], ),
    sa.ForeignKeyConstraint(['tenant_id'], ['tenant.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('product', schema=None) as batch_op:
        batch_op.create_index('idx_product_barcode_tenant', ['barcode', 'tenant_id'], unique=False)
        batch_op.create_index('idx_product_sku_tenant', ['sku', 'tenant_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_product_barcode'), ['barcode'], unique=False)
        batch_op.create_index(batch_op.f('ix_product_sku'), ['sku'], unique=False)
        batch_op.create_index(batch_op.f('ix_product_tenant_id'), ['tenant_id'], unique=False)

    op.create_table('transaction',
    sa.Column('transaction_number', sa.String(length=50), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('customer_name', sa.String(length=100), nullable=True),
    sa.Column('customer_email', sa.String(length=120), nullable=True),
    sa.Column('customer_phone', sa.String(length=20), nullable=True),
    sa.Column('status', sa.Enum('PENDING', 'COMPLETED', 'CANCELLED', 'REFUNDED', 'PARTIALLY_REFUNDED', name='transactionstatus'), nullable=False),
    sa.Column('transaction_date', sa.DateTime(), nullable=False),
    sa.Column('completed_at', sa.DateTime(), nullable=True),
    sa.Column('subtotal', sa.Numeric(precision=10, scale=2), nullable=False),
    sa.Column('tax_amount', sa.Numeric(precision=10, scale=2), nullable=False),
    sa.Column('discount_amount', sa.Numeric(precision=10, scale=2), nullable=False),
    sa.Column('total_amount', sa.Numeric(precision=10, scale=2), nullable=False),
    sa.Column('payment_method', sa.Enum('CASH', 'CARD', 'DIGITAL_WALLET', 'BANK_TRANSFER', 'STORE_CREDIT', 'OTHER', name='paymentmethod'), nullable=True),
    sa.Column('amount_paid', sa.Numeric(precision=10, scale=2), nullable=False),
    sa.Column('change_given', sa.Numeric(precision=10, scale=2), nullable=False),
    sa.Column('discount_type', sa.String(length=20), nullable=True),
    sa.Column('discount_value', sa.Numeric(precision=10, scale=2), nullable=True),
    sa.Column('discount_reason', sa.String(length=200), nullable=True),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.Column('receipt_number', sa.String(length=50), nullable=True),
    sa.Column('table_number', sa.String(length=20), nullable=True),
    sa.Column('order_type', sa.String(length=20), nullable=True),
    sa.Column('tenant_id', sa.Integer(), nullable=False),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['tenant_id'], ['tenant.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('transaction', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_transaction_tenant_id'), ['tenant_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_transaction_transaction_number'), ['transaction_number'], unique=True)

    op.create_table('inventory_movement',
    sa.Column('product_id', sa.Integer(), nullable=False),
    sa.Column('movement_type', sa.String(length=50), nullable=False),
    sa.Column('quantity', sa.Integer(), nullable=False),
    sa.Column('old_stock', sa.Integer(), nullable=False),
    sa.Column('new_stock', sa.Integer(), nullable=False),
    sa.Column('reason', sa.String(length=200), nullable=True),
    sa.Column('reference_id', sa.String(length=100), nullable=True),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.Column('user_id', sa.Integer(), nullable=True),
    sa.Column('tenant_id', sa.Integer(), nullable=False),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['product_id'], ['product.id'], ),
    sa.ForeignKeyConstraint(['tenant_id'], ['tenant.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('inventory_movement', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_inventory_movement_tenant_id'), ['tenant_id'], unique=False)

    op.create_table('transaction_item',
    sa.Column('transaction_id', sa.Integer(), nullable=False),
    sa.Column('product_id', sa.Integer(), nullable=False),
    sa.Column('quantity', sa.Integer(), nullable=False),
    sa.Column('unit_price', sa.Numeric(precision=10, scale=2), nullable=False),
    sa.Column('line_total', sa.Numeric(precision=10, scale=2), nullable=False),
    sa.Column('tax_rate', sa.Numeric(precision=5, scale=4), nullable=False),
    sa.Column('tax_amount', sa.Numeric(precision=10, scale=2), nullable=False),
    sa.Column('discount_amount', sa.Numeric(precision=10, scale=2), nullable=False),
    sa.Column('product_name', sa.String(length=200), nullable=False),
    sa.Column('product_sku', sa.String(length=100), nullable=True),
    sa.Column('cost_price', sa.Numeric(precision=10, scale=2), nullable=False),
    sa.Column('tenant_id', sa.Integer(), nullable=False),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['product_id'], ['product.id'], ),
    sa.ForeignKeyConstraint(['tenant_id'], ['tenant.id'], ),
    sa.ForeignKeyConstraint(['transaction_id'], ['transaction.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('transaction_item', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_transaction_item_tenant_id'), ['tenant_id'], unique=False)

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('transaction_item', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_transaction_item_tenant_id'))

    op.drop_table('transaction_item')
    with op.batch_alter_table('inventory_movement', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_inventory_movement_tenant_id'))

    op.drop_table('inventory_movement')
    with op.batch_alter_table('transaction', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_transaction_transaction_number'))
        batch_op.drop_index(batch_op.f('ix_transaction_tenant_id'))

    op.drop_table('transaction')
    with op.batch_alter_table('product', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_product_tenant_id'))
        batch_op.drop_index(batch_op.f('ix_product_sku'))
        batch_op.drop_index(batch_op.f('ix_product_barcode'))
        batch_op.drop_index('idx_product_sku_tenant')
        batch_op.drop_index('idx_product_barcode_tenant')

    op.drop_table('product')
    with op.batch_alter_table('user', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_user_tenant_id'))
        batch_op.drop_index(batch_op.f('ix_user_email'))

    op.drop_table('user')
    with op.batch_alter_table('category', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_category_tenant_id'))

    op.drop_table('category')
    with op.batch_alter_table('business_settings', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_business_settings_tenant_id'))

    op.drop_table('business_settings')
    op.drop_table('tenant')
    # ### end Alembic commands ###
