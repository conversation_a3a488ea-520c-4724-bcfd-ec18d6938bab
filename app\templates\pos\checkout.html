{% extends "base.html" %}

{% block title %}Checkout - SaaS POS System{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-100 py-8">
    <div class="max-w-4xl mx-auto px-4">
        <!-- Header -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Checkout</h1>
                    <p class="text-gray-600">Order #{{ transaction.transaction_number }}</p>
                </div>
                <a href="{{ url_for('pos.dashboard') }}" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded">
                    Back to POS
                </a>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Order Summary -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold mb-4">Order Summary</h2>
                
                <!-- Items -->
                <div class="space-y-3 mb-6">
                    {% for item in transaction.items %}
                    <div class="flex justify-between items-center py-2 border-b">
                        <div class="flex-1">
                            <h3 class="font-medium">{{ item.product_name }}</h3>
                            <p class="text-sm text-gray-600">{{ item.quantity }} × ${{ "%.2f"|format(item.unit_price) }}</p>
                        </div>
                        <div class="text-right">
                            <p class="font-semibold">${{ "%.2f"|format(item.line_total) }}</p>
                            {% if item.discount_amount > 0 %}
                            <p class="text-sm text-green-600">-${{ "%.2f"|format(item.discount_amount) }}</p>
                            {% endif %}
                        </div>
                    </div>
                    {% endfor %}
                </div>

                <!-- Totals -->
                <div class="space-y-2 border-t pt-4">
                    <div class="flex justify-between">
                        <span>Subtotal:</span>
                        <span>${{ "%.2f"|format(transaction.subtotal) }}</span>
                    </div>
                    {% if transaction.discount_amount > 0 %}
                    <div class="flex justify-between text-green-600">
                        <span>Discount:</span>
                        <span>-${{ "%.2f"|format(transaction.discount_amount) }}</span>
                    </div>
                    {% endif %}
                    <div class="flex justify-between">
                        <span>Tax:</span>
                        <span>${{ "%.2f"|format(transaction.tax_amount) }}</span>
                    </div>
                    <div class="flex justify-between font-bold text-lg border-t pt-2">
                        <span>Total:</span>
                        <span>${{ "%.2f"|format(transaction.total_amount) }}</span>
                    </div>
                </div>
            </div>

            <!-- Payment -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold mb-4">Payment</h2>
                
                <form id="payment-form">
                    <!-- Payment Method -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium mb-3">Payment Method</label>
                        <div class="grid grid-cols-2 gap-3">
                            <button type="button" class="payment-method-btn bg-blue-500 text-white p-4 rounded-lg hover:bg-blue-600 transition-colors" 
                                    data-method="cash">
                                <div class="text-center">
                                    <div class="text-2xl mb-2">💵</div>
                                    <div class="font-semibold">Cash</div>
                                </div>
                            </button>
                            <button type="button" class="payment-method-btn bg-gray-200 text-gray-700 p-4 rounded-lg hover:bg-gray-300 transition-colors" 
                                    data-method="card">
                                <div class="text-center">
                                    <div class="text-2xl mb-2">💳</div>
                                    <div class="font-semibold">Card</div>
                                </div>
                            </button>
                            <button type="button" class="payment-method-btn bg-gray-200 text-gray-700 p-4 rounded-lg hover:bg-gray-300 transition-colors" 
                                    data-method="digital_wallet">
                                <div class="text-center">
                                    <div class="text-2xl mb-2">📱</div>
                                    <div class="font-semibold">Digital</div>
                                </div>
                            </button>
                            <button type="button" class="payment-method-btn bg-gray-200 text-gray-700 p-4 rounded-lg hover:bg-gray-300 transition-colors" 
                                    data-method="other">
                                <div class="text-center">
                                    <div class="text-2xl mb-2">💰</div>
                                    <div class="font-semibold">Other</div>
                                </div>
                            </button>
                        </div>
                    </div>

                    <!-- Amount Received -->
                    <div class="mb-6">
                        <label for="amount-paid" class="block text-sm font-medium mb-2">Amount Received</label>
                        <div class="relative">
                            <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">$</span>
                            <input type="number" id="amount-paid" step="0.01" min="{{ transaction.total_amount }}" 
                                   value="{{ transaction.total_amount }}"
                                   class="w-full pl-8 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-lg">
                        </div>
                    </div>

                    <!-- Change Due -->
                    <div class="mb-6 p-4 bg-green-50 rounded-lg">
                        <div class="flex justify-between items-center">
                            <span class="text-lg font-medium">Change Due:</span>
                            <span id="change-due" class="text-2xl font-bold text-green-600">$0.00</span>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="space-y-3">
                        <button type="submit" id="complete-payment-btn" 
                                class="w-full bg-green-500 hover:bg-green-600 text-white py-4 rounded-lg font-semibold text-lg disabled:opacity-50 disabled:cursor-not-allowed"
                                disabled>
                            Complete Payment
                        </button>
                        <button type="button" id="print-receipt-btn" 
                                class="w-full bg-blue-500 hover:bg-blue-600 text-white py-3 rounded-lg font-semibold hidden">
                            Print Receipt
                        </button>
                        <a href="{{ url_for('pos.dashboard') }}" 
                           class="block w-full bg-gray-500 hover:bg-gray-600 text-white py-3 rounded-lg font-semibold text-center">
                            Back to POS
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Success Modal -->
<div id="success-modal" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg p-8 max-w-md w-full mx-4">
        <div class="text-center">
            <div class="text-6xl text-green-500 mb-4">✓</div>
            <h3 class="text-2xl font-bold text-gray-900 mb-2">Payment Successful!</h3>
            <p class="text-gray-600 mb-6">Order #<span id="success-order-number"></span> has been completed.</p>
            <div class="space-y-3">
                <button id="view-receipt-btn" class="w-full bg-blue-500 hover:bg-blue-600 text-white py-3 rounded-lg font-semibold">
                    View Receipt
                </button>
                <button id="new-order-btn" class="w-full bg-green-500 hover:bg-green-600 text-white py-3 rounded-lg font-semibold">
                    New Order
                </button>
            </div>
        </div>
    </div>
</div>

<script>
let selectedPaymentMethod = null;
const transactionId = {{ transaction.id }};
const totalAmount = {{ transaction.total_amount }};

document.addEventListener('DOMContentLoaded', function() {
    initializeCheckout();
});

function initializeCheckout() {
    // Payment method selection
    document.querySelectorAll('.payment-method-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            selectPaymentMethod(this.dataset.method);
        });
    });

    // Amount paid input
    document.getElementById('amount-paid').addEventListener('input', calculateChange);

    // Payment form submission
    document.getElementById('payment-form').addEventListener('submit', function(e) {
        e.preventDefault();
        processPayment();
    });

    // Modal buttons
    document.getElementById('view-receipt-btn').addEventListener('click', function() {
        window.open(`/pos/transaction/${transactionId}/receipt`, '_blank');
    });

    document.getElementById('new-order-btn').addEventListener('click', function() {
        window.location.href = '/pos/dashboard';
    });

    // Initialize with cash selected
    selectPaymentMethod('cash');
    calculateChange();
}

function selectPaymentMethod(method) {
    selectedPaymentMethod = method;
    
    // Update button styles
    document.querySelectorAll('.payment-method-btn').forEach(btn => {
        if (btn.dataset.method === method) {
            btn.classList.remove('bg-gray-200', 'text-gray-700');
            btn.classList.add('bg-blue-500', 'text-white');
        } else {
            btn.classList.remove('bg-blue-500', 'text-white');
            btn.classList.add('bg-gray-200', 'text-gray-700');
        }
    });

    // Enable payment button
    document.getElementById('complete-payment-btn').disabled = false;

    // For non-cash payments, set exact amount
    if (method !== 'cash') {
        document.getElementById('amount-paid').value = totalAmount.toFixed(2);
        calculateChange();
    }
}

function calculateChange() {
    const amountPaid = parseFloat(document.getElementById('amount-paid').value) || 0;
    const changeDue = Math.max(0, amountPaid - totalAmount);
    
    document.getElementById('change-due').textContent = `$${changeDue.toFixed(2)}`;
    
    // Update button state
    const completeBtn = document.getElementById('complete-payment-btn');
    if (amountPaid >= totalAmount && selectedPaymentMethod) {
        completeBtn.disabled = false;
        completeBtn.classList.remove('opacity-50', 'cursor-not-allowed');
    } else {
        completeBtn.disabled = true;
        completeBtn.classList.add('opacity-50', 'cursor-not-allowed');
    }
}

async function processPayment() {
    if (!selectedPaymentMethod) {
        alert('Please select a payment method');
        return;
    }

    const amountPaid = parseFloat(document.getElementById('amount-paid').value);
    
    if (amountPaid < totalAmount) {
        alert('Amount paid is less than the total amount');
        return;
    }

    try {
        // Disable button to prevent double submission
        const btn = document.getElementById('complete-payment-btn');
        btn.disabled = true;
        btn.textContent = 'Processing...';

        const response = await fetch(`/pos/transaction/${transactionId}/process_payment`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                payment_method: selectedPaymentMethod,
                amount_paid: amountPaid
            })
        });

        const data = await response.json();

        if (response.ok) {
            // Show success modal
            document.getElementById('success-order-number').textContent = data.transaction.transaction_number;
            document.getElementById('success-modal').classList.remove('hidden');
            
            // Show print receipt button
            document.getElementById('print-receipt-btn').classList.remove('hidden');
        } else {
            alert('Payment failed: ' + data.error);
            btn.disabled = false;
            btn.textContent = 'Complete Payment';
        }
    } catch (error) {
        alert('Payment failed: ' + error.message);
        const btn = document.getElementById('complete-payment-btn');
        btn.disabled = false;
        btn.textContent = 'Complete Payment';
    }
}

// Print receipt functionality
document.getElementById('print-receipt-btn').addEventListener('click', async function() {
    try {
        const response = await fetch(`/pos/transaction/${transactionId}/receipt/print`);
        const data = await response.json();
        
        if (response.ok) {
            // Open receipt in new window for printing
            const receiptWindow = window.open('', '_blank');
            receiptWindow.document.write(generatePrintableReceipt(data.receipt));
            receiptWindow.document.close();
            receiptWindow.print();
        } else {
            alert('Error generating receipt: ' + data.error);
        }
    } catch (error) {
        alert('Error generating receipt: ' + error.message);
    }
});

function generatePrintableReceipt(receipt) {
    return `
        <!DOCTYPE html>
        <html>
        <head>
            <title>Receipt - ${receipt.transaction_number}</title>
            <style>
                body { font-family: monospace; font-size: 12px; margin: 20px; }
                .center { text-align: center; }
                .right { text-align: right; }
                .bold { font-weight: bold; }
                .line { border-top: 1px dashed #000; margin: 10px 0; }
                table { width: 100%; border-collapse: collapse; }
                td { padding: 2px 0; }
            </style>
        </head>
        <body>
            <div class="center bold">
                <h2>${receipt.tenant.name}</h2>
                ${receipt.tenant.address ? `<p>${receipt.tenant.address}</p>` : ''}
                ${receipt.tenant.phone ? `<p>Phone: ${receipt.tenant.phone}</p>` : ''}
            </div>
            
            <div class="line"></div>
            
            <table>
                <tr><td>Receipt #:</td><td class="right">${receipt.receipt_number}</td></tr>
                <tr><td>Date:</td><td class="right">${receipt.date}</td></tr>
                ${receipt.customer.name ? `<tr><td>Customer:</td><td class="right">${receipt.customer.name}</td></tr>` : ''}
            </table>
            
            <div class="line"></div>
            
            <table>
                ${receipt.items.map(item => `
                    <tr>
                        <td colspan="2">${item.name}</td>
                    </tr>
                    <tr>
                        <td>${item.quantity} x $${item.unit_price.toFixed(2)}</td>
                        <td class="right">$${item.line_total.toFixed(2)}</td>
                    </tr>
                `).join('')}
            </table>
            
            <div class="line"></div>
            
            <table>
                <tr><td>Subtotal:</td><td class="right">$${receipt.totals.subtotal.toFixed(2)}</td></tr>
                ${receipt.totals.discount_amount > 0 ? `<tr><td>Discount:</td><td class="right">-$${receipt.totals.discount_amount.toFixed(2)}</td></tr>` : ''}
                <tr><td>Tax:</td><td class="right">$${receipt.totals.tax_amount.toFixed(2)}</td></tr>
                <tr class="bold"><td>Total:</td><td class="right">$${receipt.totals.total_amount.toFixed(2)}</td></tr>
                <tr><td>Paid:</td><td class="right">$${receipt.totals.amount_paid.toFixed(2)}</td></tr>
                <tr><td>Change:</td><td class="right">$${receipt.totals.change_given.toFixed(2)}</td></tr>
            </table>
            
            <div class="line"></div>
            
            <div class="center">
                <p>Thank you for your business!</p>
                <p>Payment Method: ${receipt.payment.method.replace('_', ' ').toUpperCase()}</p>
            </div>
        </body>
        </html>
    `;
}
</script>
{% endblock %}