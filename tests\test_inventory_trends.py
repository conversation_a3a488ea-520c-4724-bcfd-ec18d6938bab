"""Unit tests for InventoryService inventory trends functionality."""

import pytest
from datetime import datetime, timedelta
from decimal import Decimal
from unittest.mock import patch, MagicMock

from app import create_app, db
from app.models.user import User, Tenant
from app.models.product import Product, Category, InventoryMovement
from app.models.transaction import Transaction, TransactionItem
from app.services.inventory_service import InventoryService
from config import TestingConfig


@pytest.fixture
def app():
    """Create test application."""
    app = create_app(TestingConfig)
    return app


@pytest.fixture
def app_context(app):
    """Create application context."""
    with app.app_context():
        db.create_all()
        yield app
        db.session.remove()
        db.drop_all()


class TestInventoryTrends:
    """Test cases for InventoryService inventory trends functionality."""
    
    @pytest.fixture
    def sample_tenant(self, app_context):
        """Create a sample tenant for testing."""
        tenant = Tenant.create(
            name="Test Business",
            business_type="retail",
            email="<EMAIL>"
        )
        return tenant
    
    @pytest.fixture
    def sample_user(self, app_context, sample_tenant):
        """Create a sample user for testing."""
        user = User.create_user(
            email="<EMAIL>",
            password="password123",
            first_name="Test",
            last_name="User",
            tenant_id=sample_tenant.id,
            role="manager"
        )
        return user
    
    @pytest.fixture
    def sample_products(self, app_context, sample_tenant, sample_category):
        """Create sample products for testing."""
        with patch('app.models.base.get_current_tenant', return_value=sample_tenant.id):
            products = []
            for i in range(3):
                product = Product.create(
                    tenant_id=sample_tenant.id,
                    name=f"Test Product {i+1}",
                    sku=f"TEST{i+1:03d}",
                    barcode=f"12345{i+1}",
                    category_id=sample_category.id,
                    cost_price=Decimal(f'{5.00 + i}'),
                    selling_price=Decimal(f'{10.00 + i*2}'),
                    current_stock=50 - i*10,
                    minimum_stock=10
                )
                products.append(product)
            return products
    
    @pytest.fixture
    def sample_category(self, app_context, sample_tenant):
        """Create a sample category for testing."""
        with patch('app.models.base.get_current_tenant', return_value=sample_tenant.id):
            category = Category.create(
                tenant_id=sample_tenant.id,
                name="Electronics",
                description="Electronic products"
            )
        return category
    
    @pytest.fixture
    def sample_inventory_movements(self, app_context, sample_tenant, sample_products):
        """Create sample inventory movements for testing."""
        with patch('app.models.base.get_current_tenant', return_value=sample_tenant.id):
            movements = []
            
            # Create movements for each product
            for i, product in enumerate(sample_products):
                # Initial stock
                movement1 = InventoryMovement.create(
                    tenant_id=sample_tenant.id,
                    product_id=product.id,
                    movement_type='initial_stock',
                    quantity=50,
                    old_stock=0,
                    new_stock=50,
                    reason='Initial stock entry'
                )
                
                # Sale movement
                movement2 = InventoryMovement.create(
                    tenant_id=sample_tenant.id,
                    product_id=product.id,
                    movement_type='sale',
                    quantity=-(5 + i*2),
                    old_stock=50,
                    new_stock=45 - i*2,
                    reason='Sale'
                )
                
                # Restock movement
                movement3 = InventoryMovement.create(
                    tenant_id=sample_tenant.id,
                    product_id=product.id,
                    movement_type='restock',
                    quantity=10,
                    old_stock=45 - i*2,
                    new_stock=55 - i*2,
                    reason='Restock'
                )
                
                movements.extend([movement1, movement2, movement3])
            
            return movements
    
    @pytest.fixture
    def sample_transactions(self, app_context, sample_tenant, sample_user, sample_products):
        """Create sample transactions for testing."""
        from app.models.transaction import TransactionStatus, PaymentMethod
        
        with patch('app.models.base.get_current_tenant', return_value=sample_tenant.id):
            transactions = []
            
            # Create a few transactions
            for i in range(3):
                transaction = Transaction.create(
                    tenant_id=sample_tenant.id,
                    user_id=sample_user.id,
                    transaction_date=datetime.now() - timedelta(days=i),
                    status=TransactionStatus.COMPLETED,
                    payment_method=PaymentMethod.CASH,
                    subtotal=Decimal('50.00'),
                    tax_amount=Decimal('5.00'),
                    total_amount=Decimal('55.00'),
                    notes=f'Test transaction {i+1}'
                )
                
                # Add items to transaction
                for j, product in enumerate(sample_products):
                    if j <= i:  # Add different products to different transactions
                        item = TransactionItem.create(
                            transaction_id=transaction.id,
                            product_id=product.id,
                            tenant_id=sample_tenant.id,
                            quantity=2,
                            unit_price=float(product.selling_price),
                            tax_rate=0.1,
                            line_total=float(product.selling_price) * 2,
                            tax_amount=float(product.selling_price) * 2 * 0.1,
                            product_name=product.name,
                            product_sku=product.sku,
                            cost_price=float(product.cost_price),
                            discount_amount=0.0
                        )
                
                transactions.append(transaction)
            
            return transactions
    
    @patch('app.models.base.get_current_tenant')
    def test_get_inventory_trends_success(self, mock_get_tenant, app_context, sample_tenant, 
                                         sample_inventory_movements, sample_transactions):
        """Test successful retrieval of inventory trends."""
        mock_get_tenant.return_value = sample_tenant.id
        
        result = InventoryService.get_inventory_trends(days=30)
        
        assert result['success'] is True
        assert 'date_range' in result
        assert 'daily_data' in result
        assert 'top_products' in result
        assert 'summary' in result
        
        # Check date range
        assert result['date_range']['days'] == 30
        
        # Check daily data
        assert 'movements' in result['daily_data']
        assert 'sales' in result['daily_data']
        
        # Check top products
        assert 'by_sales' in result['top_products']
        assert 'by_movement' in result['top_products']
        
        # Check summary statistics
        assert 'total_inflow' in result['summary']
        assert 'total_outflow' in result['summary']
        assert 'total_adjustment' in result['summary']
        assert 'total_sold' in result['summary']
        assert 'total_sales' in result['summary']
        assert 'avg_daily_sales' in result['summary']
        assert 'avg_daily_revenue' in result['summary']
        assert 'net_inventory_change' in result['summary']
    
    @patch('app.models.base.get_current_tenant')
    def test_get_inventory_trends_no_tenant(self, mock_get_tenant, app_context):
        """Test inventory trends with no tenant context."""
        mock_get_tenant.return_value = None
        
        result = InventoryService.get_inventory_trends()
        
        assert result['success'] is False
        assert 'No tenant context' in result['error']
    
    @patch('app.models.base.get_current_tenant')
    @patch('app.db.session.query')
    def test_get_inventory_trends_empty_data(self, mock_query, mock_get_tenant, app_context, sample_tenant):
        """Test inventory trends with empty data."""
        mock_get_tenant.return_value = sample_tenant.id
        
        # Mock empty query results
        mock_query.return_value.filter.return_value.group_by.return_value.order_by.return_value.all.return_value = []
        mock_query.return_value.join.return_value.filter.return_value.group_by.return_value.order_by.return_value.all.return_value = []
        
        result = InventoryService.get_inventory_trends()
        
        assert result['success'] is True
        assert len(result['daily_data']['movements']) == 0
        assert len(result['daily_data']['sales']) == 0
        assert len(result['top_products']['by_sales']) == 0
        assert len(result['top_products']['by_movement']) == 0
        assert result['summary']['total_inflow'] == 0
        assert result['summary']['total_outflow'] == 0
        assert result['summary']['total_sold'] == 0
    
    @patch('app.models.base.get_current_tenant')
    @patch('app.services.inventory_service.current_app.logger')
    def test_get_inventory_trends_error_handling(self, mock_logger, mock_get_tenant, app_context, sample_tenant):
        """Test error handling in inventory trends."""
        mock_get_tenant.return_value = sample_tenant.id
        
        # Force an exception
        with patch('app.db.session.query', side_effect=Exception("Database error")):
            result = InventoryService.get_inventory_trends()
            
            assert result['success'] is False
            assert 'error occurred' in result['error']
            mock_logger.error.assert_called()
    
    @patch('app.models.base.get_current_tenant')
    def test_get_inventory_trends_custom_days(self, mock_get_tenant, app_context, sample_tenant,
                                             sample_inventory_movements, sample_transactions):
        """Test inventory trends with custom days parameter."""
        mock_get_tenant.return_value = sample_tenant.id
        
        result = InventoryService.get_inventory_trends(days=7)
        
        assert result['success'] is True
        assert result['date_range']['days'] == 7