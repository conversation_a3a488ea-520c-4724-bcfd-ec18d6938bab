"""Decorators for automatic audit logging."""

from functools import wraps
from flask import request, g
from flask_login import current_user
from app.services.audit_service import AuditService
from app.models.audit import AuditAction, AuditSeverity


def audit_action(action: AuditAction, description: str = None, 
                severity: AuditSeverity = AuditSeverity.LOW,
                resource_type: str = None):
    """
    Decorator to automatically log audit events for route functions.
    
    Args:
        action: The audit action to log
        description: Description template (can use {result} placeholder)
        severity: Severity level of the action
        resource_type: Type of resource being affected
    
    Example:
        @audit_action(AuditAction.PRODUCT_CREATED, "Product {result.name} created", AuditSeverity.MEDIUM, "product")
        def create_product():
            # ... create product logic
            return product
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # Execute the original function
            try:
                result = f(*args, **kwargs)
                success = True
                error_message = None
                
                # Determine resource ID from result
                resource_id = None
                if hasattr(result, 'id'):
                    resource_id = result.id
                elif isinstance(result, dict) and 'id' in result:
                    resource_id = result['id']
                
                # Format description with result if template provided
                final_description = description
                if description and result:
                    try:
                        if hasattr(result, '__dict__'):
                            final_description = description.format(result=result)
                        elif isinstance(result, dict):
                            final_description = description.format(**result)
                    except (KeyError, AttributeError):
                        # Use original description if formatting fails
                        pass
                
                # Log the successful action
                AuditService.log(
                    action=action,
                    description=final_description or f"{action.value} completed successfully",
                    severity=severity,
                    resource_type=resource_type,
                    resource_id=resource_id,
                    success=success,
                    error_message=error_message
                )
                
                return result
                
            except Exception as e:
                # Log the failed action
                AuditService.log(
                    action=action,
                    description=f"{action.value} failed: {str(e)}",
                    severity=AuditSeverity.HIGH,
                    resource_type=resource_type,
                    success=False,
                    error_message=str(e)
                )
                
                # Re-raise the exception
                raise
        
        return decorated_function
    return decorator


def audit_data_change(action: AuditAction, resource_type: str, 
                     get_resource_id=None, get_old_values=None):
    """
    Decorator for logging data changes with before/after values.
    
    Args:
        action: The audit action to log
        resource_type: Type of resource being changed
        get_resource_id: Function to extract resource ID from function args/result
        get_old_values: Function to get old values before change
    
    Example:
        @audit_data_change(
            AuditAction.PRODUCT_UPDATED, 
            "product",
            get_resource_id=lambda args, kwargs, result: args[0],  # product_id from first arg
            get_old_values=lambda args, kwargs: Product.query.get(args[0]).to_dict()
        )
        def update_product(product_id, **updates):
            # ... update logic
            return updated_product
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # Get old values before change
            old_values = None
            if get_old_values:
                try:
                    old_values = get_old_values(args, kwargs)
                except Exception:
                    # If we can't get old values, continue without them
                    pass
            
            try:
                result = f(*args, **kwargs)
                
                # Get resource ID
                resource_id = None
                if get_resource_id:
                    try:
                        resource_id = get_resource_id(args, kwargs, result)
                    except Exception:
                        pass
                
                # Get new values from result
                new_values = None
                if hasattr(result, 'to_dict'):
                    new_values = result.to_dict()
                elif hasattr(result, '__dict__'):
                    new_values = {k: v for k, v in result.__dict__.items() 
                                if not k.startswith('_')}
                elif isinstance(result, dict):
                    new_values = result
                
                # Log the data change
                AuditService.log_data_change(
                    action=action,
                    resource_type=resource_type,
                    resource_id=resource_id,
                    description=f"{resource_type} {action.value}",
                    old_values=old_values,
                    new_values=new_values
                )
                
                return result
                
            except Exception as e:
                # Log the failed change
                AuditService.log(
                    action=action,
                    description=f"{resource_type} {action.value} failed: {str(e)}",
                    severity=AuditSeverity.HIGH,
                    resource_type=resource_type,
                    success=False,
                    error_message=str(e)
                )
                
                raise
        
        return decorated_function
    return decorator


def audit_authentication(action: AuditAction):
    """
    Decorator for authentication-related actions.
    
    Example:
        @audit_authentication(AuditAction.LOGIN)
        def login():
            # ... login logic
            return user
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            try:
                result = f(*args, **kwargs)
                
                # Extract user email from result or form data
                user_email = None
                if hasattr(result, 'email'):
                    user_email = result.email
                elif request.form and 'email' in request.form:
                    user_email = request.form['email']
                elif request.json and 'email' in request.json:
                    user_email = request.json['email']
                
                # Log successful authentication
                AuditService.log_authentication(
                    action=action,
                    user_email=user_email or 'unknown',
                    success=True,
                    user_id=getattr(result, 'id', None),
                    tenant_id=getattr(result, 'tenant_id', None)
                )
                
                return result
                
            except Exception as e:
                # Extract user email from request for failed attempts
                user_email = None
                if request.form and 'email' in request.form:
                    user_email = request.form['email']
                elif request.json and 'email' in request.json:
                    user_email = request.json['email']
                
                # Log failed authentication
                AuditService.log_authentication(
                    action=action,
                    user_email=user_email or 'unknown',
                    success=False,
                    error_message=str(e)
                )
                
                raise
        
        return decorated_function
    return decorator


def audit_transaction(action: AuditAction):
    """
    Decorator for transaction-related actions.
    
    Example:
        @audit_transaction(AuditAction.TRANSACTION_CREATED)
        def create_transaction():
            # ... transaction logic
            return transaction
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            try:
                result = f(*args, **kwargs)
                
                # Extract transaction details
                transaction_id = getattr(result, 'id', None)
                amount = getattr(result, 'total_amount', 0.0)
                
                # Log transaction action
                AuditService.log_transaction(
                    action=action,
                    transaction_id=transaction_id,
                    amount=amount,
                    description=f"Transaction {action.value} - Amount: ${amount:.2f}"
                )
                
                return result
                
            except Exception as e:
                # Log failed transaction
                AuditService.log(
                    action=action,
                    description=f"Transaction {action.value} failed: {str(e)}",
                    severity=AuditSeverity.HIGH,
                    resource_type='transaction',
                    success=False,
                    error_message=str(e)
                )
                
                raise
        
        return decorated_function
    return decorator


def audit_security_event(action: AuditAction, severity: AuditSeverity = AuditSeverity.HIGH):
    """
    Decorator for security-related events.
    
    Example:
        @audit_security_event(AuditAction.UNAUTHORIZED_ACCESS, AuditSeverity.CRITICAL)
        def protected_endpoint():
            # ... protected logic
            return result
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            try:
                result = f(*args, **kwargs)
                return result
                
            except Exception as e:
                # Log security event on failure
                AuditService.log_security_event(
                    action=action,
                    description=f"Security event: {str(e)}",
                    severity=severity,
                    additional_data={
                        'function': f.__name__,
                        'args': str(args)[:200],  # Limit length
                        'kwargs': str(kwargs)[:200]
                    }
                )
                
                raise
        
        return decorated_function
    return decorator