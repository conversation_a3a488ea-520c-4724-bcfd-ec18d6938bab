"""Tests for restaurant service."""

import pytest
from datetime import datetime, timedelta
from app import create_app, db
from app.models.user import User, Tenant
from app.models.product import Product, Category
from app.models.restaurant import Table, Order, TableStatus, OrderStatus
from app.models.business import BusinessSettings
from app.models.base import set_current_tenant, clear_current_tenant
from app.services.restaurant_service import RestaurantService
from config import TestingConfig


@pytest.fixture
def app():
    """Create application for testing."""
    app = create_app(TestingConfig)
    return app


@pytest.fixture
def app_context(app):
    """Create application context."""
    with app.app_context():
        db.create_all()
        yield app
        db.session.remove()
        db.drop_all()


@pytest.fixture
def client(app):
    """Create test client."""
    return app.test_client()


@pytest.fixture
def tenant(app_context):
    """Create test tenant."""
    tenant = Tenant(
        name="Test Restaurant",
        business_type="restaurant"
    )
    tenant.save()
    set_current_tenant(tenant.id)
    return tenant


@pytest.fixture
def user(app_context, tenant):
    """Create test user."""
    user = User.create_user(
        email="<EMAIL>",
        password="password123",
        first_name="Test",
        last_name="User",
        tenant_id=tenant.id,
        role="manager"
    )
    return user


@pytest.fixture
def business_settings(app_context, tenant):
    """Create business settings."""
    settings = BusinessSettings.create_for_tenant(
        tenant_id=tenant.id,
        business_name="Test Restaurant",
        business_type="restaurant"
    )
    return settings


@pytest.fixture
def category(app_context, tenant):
    """Create test category."""
    category = Category(
        tenant_id=tenant.id,
        name="Food",
        description="Food items"
    )
    return category.save()


@pytest.fixture
def product(app_context, tenant, category):
    """Create test product."""
    product = Product(
        tenant_id=tenant.id,
        name="Burger",
        sku="BURGER001",
        category_id=category.id,
        selling_price=12.99,
        cost_price=6.50,
        stock_quantity=100
    )
    return product.save()


class TestRestaurantService:
    """Test RestaurantService class."""
    
    def test_create_table(self, app, tenant):
        """Test creating a table through service."""
        table = RestaurantService.create_table(
            table_number="T1",
            capacity=4,
            section="Main Dining"
        )
        
        assert table.table_number == "T1"
        assert table.capacity == 4
        assert table.section == "Main Dining"
        assert table.status == TableStatus.AVAILABLE
    
    def test_create_duplicate_table(self, app, tenant):
        """Test creating duplicate table raises error."""
        RestaurantService.create_table(table_number="T1")
        
        with pytest.raises(ValueError, match="already exists"):
            RestaurantService.create_table(table_number="T1")
    
    def test_get_table_layout(self, app, tenant):
        """Test getting table layout."""
        RestaurantService.create_table("T1", section="Main")
        RestaurantService.create_table("T2", section="Patio")
        RestaurantService.create_table("T3", section="Main")
        
        # Get all tables
        all_tables = RestaurantService.get_table_layout()
        assert len(all_tables) == 3
        
        # Get tables by section
        main_tables = RestaurantService.get_table_layout(section="Main")
        assert len(main_tables) == 2
    
    def test_get_table_status_summary(self, app, tenant):
        """Test getting table status summary."""
        table1 = RestaurantService.create_table("T1")
        table2 = RestaurantService.create_table("T2")
        table3 = RestaurantService.create_table("T3")
        
        # Occupy one table
        table2.occupy_table(party_size=2)
        
        # Reserve one table
        table3.reserve_table(customer_name="John Doe")
        
        summary = RestaurantService.get_table_status_summary()
        
        assert summary['total'] == 3
        assert summary['available'] == 1
        assert summary['occupied'] == 1
        assert summary['reserved'] == 1
        assert summary['cleaning'] == 0
        assert summary['out_of_order'] == 0
    
    def test_seat_party(self, app, tenant):
        """Test seating a party."""
        RestaurantService.create_table("T1", capacity=4)
        
        table = RestaurantService.seat_party("T1", party_size=2, estimated_duration=60)
        
        assert table.status == TableStatus.OCCUPIED
        assert table.current_party_size == 2
        assert table.estimated_duration == 60
    
    def test_seat_party_exceeds_capacity(self, app, tenant):
        """Test seating party that exceeds table capacity."""
        RestaurantService.create_table("T1", capacity=2)
        
        with pytest.raises(ValueError, match="exceeds table capacity"):
            RestaurantService.seat_party("T1", party_size=4)
    
    def test_seat_party_table_not_found(self, app, tenant):
        """Test seating party at non-existent table."""
        with pytest.raises(ValueError, match="not found"):
            RestaurantService.seat_party("T99", party_size=2)
    
    def test_clear_table(self, app, tenant):
        """Test clearing a table."""
        table = RestaurantService.create_table("T1")
        table.occupy_table(party_size=2)
        
        cleared_table = RestaurantService.clear_table("T1")
        
        assert cleared_table.status == TableStatus.AVAILABLE
        assert cleared_table.current_party_size is None
    
    def test_reserve_table(self, app, tenant):
        """Test reserving a table."""
        RestaurantService.create_table("T1")
        
        reservation_time = datetime.utcnow() + timedelta(hours=2)
        table = RestaurantService.reserve_table(
            table_number="T1",
            customer_name="John Doe",
            phone="555-1234",
            notes="Birthday dinner",
            reservation_time=reservation_time
        )
        
        assert table.status == TableStatus.RESERVED
        assert table.reserved_for == "John Doe"
        assert table.reservation_phone == "555-1234"
    
    def test_create_order(self, app, tenant, user):
        """Test creating an order."""
        table = RestaurantService.create_table("T1")
        
        order = RestaurantService.create_order(
            table_number="T1",
            order_type="dine_in",
            customer_name="John Doe",
            user_id=user.id
        )
        
        assert order.table_id == table.id
        assert order.order_type == "dine_in"
        assert order.customer_name == "John Doe"
        assert order.status == OrderStatus.PENDING
    
    def test_create_order_without_table(self, app, tenant, user):
        """Test creating an order without a table (takeout)."""
        order = RestaurantService.create_order(
            order_type="takeout",
            customer_name="Jane Doe",
            user_id=user.id
        )
        
        assert order.table_id is None
        assert order.order_type == "takeout"
        assert order.customer_name == "Jane Doe"
    
    def test_add_item_to_order(self, app, tenant, user, product):
        """Test adding item to order."""
        order = RestaurantService.create_order(
            order_type="takeout",
            user_id=user.id
        )
        
        order_item = RestaurantService.add_item_to_order(
            order_id=order.id,
            product_id=product.id,
            quantity=2,
            special_instructions="No onions"
        )
        
        assert order_item.quantity == 2
        assert order_item.product_name == "Burger"
        assert order_item.special_instructions == "No onions"
    
    def test_order_workflow(self, app, tenant, user):
        """Test complete order workflow."""
        order = RestaurantService.create_order(
            order_type="takeout",
            user_id=user.id
        )
        
        # Start preparation
        order = RestaurantService.start_order_preparation(order.id)
        assert order.status == OrderStatus.PREPARING
        
        # Mark ready
        order = RestaurantService.mark_order_ready(order.id)
        assert order.status == OrderStatus.READY
        
        # Serve order
        order = RestaurantService.serve_order(order.id)
        assert order.status == OrderStatus.SERVED
    
    def test_get_kitchen_display_orders(self, app, tenant, user):
        """Test getting kitchen display orders."""
        # Create orders with different statuses
        order1 = RestaurantService.create_order(order_type="dine_in", user_id=user.id)
        order2 = RestaurantService.create_order(order_type="takeout", user_id=user.id)
        order3 = RestaurantService.create_order(order_type="delivery", user_id=user.id)
        
        # Start preparation on one order
        RestaurantService.start_order_preparation(order2.id)
        
        # Mark one as ready
        RestaurantService.start_order_preparation(order3.id)
        RestaurantService.mark_order_ready(order3.id)
        
        kitchen_orders = RestaurantService.get_kitchen_display_orders()
        
        # Should include pending, preparing, and ready orders
        assert len(kitchen_orders) == 3
        statuses = [o.status for o in kitchen_orders]
        assert OrderStatus.PENDING in statuses
        assert OrderStatus.PREPARING in statuses
        assert OrderStatus.READY in statuses
    
    def test_get_table_orders(self, app, tenant, user):
        """Test getting orders for a specific table."""
        table = RestaurantService.create_table("T1")
        
        # Create orders for the table
        order1 = RestaurantService.create_order(table_number="T1", user_id=user.id)
        order2 = RestaurantService.create_order(table_number="T1", user_id=user.id)
        
        # Create order for different table
        RestaurantService.create_table("T2")
        RestaurantService.create_order(table_number="T2", user_id=user.id)
        
        table_orders = RestaurantService.get_table_orders("T1")
        
        assert len(table_orders) == 2
        for order in table_orders:
            assert order.table_id == table.id
    
    def test_get_occupied_tables_with_duration(self, app, tenant):
        """Test getting occupied tables with duration info."""
        table1 = RestaurantService.create_table("T1")
        table2 = RestaurantService.create_table("T2")
        
        # Occupy one table
        RestaurantService.seat_party("T1", party_size=2, estimated_duration=60)
        
        occupied_tables = RestaurantService.get_occupied_tables_with_duration()
        
        assert len(occupied_tables) == 1
        table_info = occupied_tables[0]
        assert table_info['table'].table_number == "T1"
        assert 'duration_minutes' in table_info
        assert 'is_overdue' in table_info
        assert 'estimated_available' in table_info
    
    def test_create_transaction_from_order(self, app, tenant, user, product):
        """Test creating transaction from order."""
        order = RestaurantService.create_order(
            order_type="dine_in",
            customer_name="John Doe",
            user_id=user.id
        )
        
        # Add item to order
        RestaurantService.add_item_to_order(
            order_id=order.id,
            product_id=product.id,
            quantity=2
        )
        
        transaction = RestaurantService.create_transaction_from_order(
            order_id=order.id,
            user_id=user.id
        )
        
        assert transaction.customer_name == "John Doe"
        assert len(transaction.items) == 1
        assert transaction.items[0].quantity == 2
        
        # Refresh order to check link
        db.session.refresh(order)
        assert order.transaction_id == transaction.id
    
    def test_setup_restaurant_tables(self, app, tenant, business_settings):
        """Test setting up default restaurant tables."""
        tables = RestaurantService.setup_restaurant_tables(business_settings)
        
        # Should create default tables based on settings
        assert len(tables) > 0
        
        # Check that tables were created with correct prefix
        restaurant_settings = business_settings.get_restaurant_settings()
        table_prefix = restaurant_settings.get('table_prefix', 'T')
        
        for table in tables:
            assert table.table_number.startswith(table_prefix)
            assert table.section == "Main Dining"
    
    def test_get_restaurant_analytics(self, app, tenant, user, product):
        """Test getting restaurant analytics."""
        # Create and complete some orders
        order1 = RestaurantService.create_order(order_type="dine_in", user_id=user.id)
        order2 = RestaurantService.create_order(order_type="takeout", user_id=user.id)
        
        # Add items and complete orders
        RestaurantService.add_item_to_order(order1.id, product.id, 1)
        RestaurantService.add_item_to_order(order2.id, product.id, 2)
        
        # Complete the orders
        RestaurantService.start_order_preparation(order1.id)
        RestaurantService.mark_order_ready(order1.id)
        RestaurantService.serve_order(order1.id)
        
        RestaurantService.start_order_preparation(order2.id)
        RestaurantService.mark_order_ready(order2.id)
        RestaurantService.serve_order(order2.id)
        
        analytics = RestaurantService.get_restaurant_analytics()
        
        assert analytics['total_orders'] == 2
        assert 'average_preparation_time' in analytics
        assert 'orders_by_type' in analytics
        assert analytics['orders_by_type']['dine_in'] == 1
        assert analytics['orders_by_type']['takeout'] == 1
    
    def test_no_tenant_context_raises_error(self, app):
        """Test that operations without tenant context raise errors."""
        clear_current_tenant()
        
        with pytest.raises(ValueError, match="No tenant context"):
            RestaurantService.create_table("T1")
        
        with pytest.raises(ValueError, match="No tenant context"):
            RestaurantService.get_table_layout()
    
    def teardown_method(self):
        """Clean up after each test."""
        clear_current_tenant()