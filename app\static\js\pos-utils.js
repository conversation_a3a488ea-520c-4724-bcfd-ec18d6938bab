/**
 * POS System Utilities
 * Common JavaScript utilities for the POS system
 */

class POSUtils {
    constructor() {
        this.currency = 'USD';
        this.locale = 'en-US';
        this.init();
    }
    
    init() {
        this.setupEventDelegation();
        this.setupKeyboardShortcuts();
        this.setupNotifications();
    }
    
    // Event Delegation
    setupEventDelegation() {
        document.addEventListener('click', (e) => {
            // Handle quantity buttons
            if (e.target.matches('.quantity-btn')) {
                this.handleQuantityChange(e.target);
            }
            
            // Handle product selection
            if (e.target.matches('.product-card, .product-card *')) {
                const productCard = e.target.closest('.product-card');
                if (productCard && !productCard.classList.contains('product-unavailable')) {
                    this.handleProductSelection(productCard);
                }
            }
            
            // Handle modal triggers
            if (e.target.matches('[data-modal-target]')) {
                this.openModal(e.target.dataset.modalTarget);
            }
            
            // Handle modal close
            if (e.target.matches('.modal-close, .modal-backdrop')) {
                this.closeModal(e.target.closest('.modal'));
            }
        });
        
        // Handle input changes
        document.addEventListener('input', (e) => {
            if (e.target.matches('.price-input')) {
                this.formatPriceInput(e.target);
            }
            
            if (e.target.matches('.quantity-input')) {
                this.validateQuantityInput(e.target);
            }
        });
    }
    
    // Keyboard Shortcuts
    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Global shortcuts
            if (e.altKey) {
                switch (e.key.toLowerCase()) {
                    case 'h':
                        e.preventDefault();
                        this.navigateTo('/dashboard');
                        break;
                    case 'p':
                        e.preventDefault();
                        this.navigateTo('/pos');
                        break;
                    case 'i':
                        e.preventDefault();
                        this.navigateTo('/inventory');
                        break;
                    case 'r':
                        e.preventDefault();
                        this.navigateTo('/reports');
                        break;
                }
            }
            
            // POS-specific shortcuts
            if (this.isPOSPage()) {
                this.handlePOSShortcuts(e);
            }
            
            // Modal shortcuts
            if (e.key === 'Escape') {
                this.closeAllModals();
            }
        });
    }
    
    handlePOSShortcuts(e) {
        switch (e.key) {
            case 'F1':
                e.preventDefault();
                this.triggerNewTransaction();
                break;
            case 'F2':
                e.preventDefault();
                this.focusSearch();
                break;
            case 'F3':
                e.preventDefault();
                this.openDiscountModal();
                break;
            case 'F4':
                e.preventDefault();
                this.proceedToCheckout();
                break;
            case 'Enter':
                if (e.ctrlKey) {
                    e.preventDefault();
                    this.proceedToCheckout();
                }
                break;
        }
        
        // Number keys for category selection
        if (e.key >= '1' && e.key <= '9' && !e.ctrlKey && !e.altKey) {
            const categoryIndex = parseInt(e.key) - 1;
            this.selectCategory(categoryIndex);
        }
    }
    
    // Notification System
    setupNotifications() {
        this.notificationContainer = this.createNotificationContainer();
    }
    
    createNotificationContainer() {
        let container = document.getElementById('notification-container');
        if (!container) {
            container = document.createElement('div');
            container.id = 'notification-container';
            container.className = 'fixed top-4 right-4 z-50 space-y-2';
            document.body.appendChild(container);
        }
        return container;
    }
    
    showNotification(message, type = 'info', duration = 5000) {
        const notification = document.createElement('div');
        notification.className = `notification max-w-sm w-full bg-white shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden transform transition-all duration-300 translate-x-full`;
        
        const bgColor = {
            success: 'bg-green-50 border-green-200',
            error: 'bg-red-50 border-red-200',
            warning: 'bg-yellow-50 border-yellow-200',
            info: 'bg-blue-50 border-blue-200'
        }[type] || 'bg-gray-50 border-gray-200';
        
        const iconColor = {
            success: 'text-green-400',
            error: 'text-red-400',
            warning: 'text-yellow-400',
            info: 'text-blue-400'
        }[type] || 'text-gray-400';
        
        const icon = {
            success: 'M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z',
            error: 'M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z',
            warning: 'M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z',
            info: 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
        }[type] || 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z';
        
        notification.innerHTML = `
            <div class="p-4 ${bgColor} border">
                <div class="flex items-start">
                    <div class="flex-shrink-0">
                        <svg class="h-6 w-6 ${iconColor}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="${icon}" />
                        </svg>
                    </div>
                    <div class="ml-3 w-0 flex-1 pt-0.5">
                        <p class="text-sm font-medium text-gray-900">${message}</p>
                    </div>
                    <div class="ml-4 flex-shrink-0 flex">
                        <button class="notification-close bg-white rounded-md inline-flex text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            <span class="sr-only">Close</span>
                            <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        this.notificationContainer.appendChild(notification);
        
        // Animate in
        setTimeout(() => {
            notification.classList.remove('translate-x-full');
        }, 100);
        
        // Auto-dismiss
        const dismissTimer = setTimeout(() => {
            this.dismissNotification(notification);
        }, duration);
        
        // Manual dismiss
        notification.querySelector('.notification-close').addEventListener('click', () => {
            clearTimeout(dismissTimer);
            this.dismissNotification(notification);
        });
        
        return notification;
    }
    
    dismissNotification(notification) {
        notification.classList.add('translate-x-full');
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }
    
    // Utility Functions
    formatCurrency(amount, currency = this.currency) {
        return new Intl.NumberFormat(this.locale, {
            style: 'currency',
            currency: currency
        }).format(amount);
    }
    
    formatNumber(number, decimals = 2) {
        return new Intl.NumberFormat(this.locale, {
            minimumFractionDigits: decimals,
            maximumFractionDigits: decimals
        }).format(number);
    }
    
    formatPriceInput(input) {
        let value = input.value.replace(/[^\d.]/g, '');
        
        // Ensure only one decimal point
        const parts = value.split('.');
        if (parts.length > 2) {
            value = parts[0] + '.' + parts.slice(1).join('');
        }
        
        // Limit decimal places to 2
        if (parts[1] && parts[1].length > 2) {
            value = parts[0] + '.' + parts[1].substring(0, 2);
        }
        
        input.value = value;
    }
    
    validateQuantityInput(input) {
        let value = input.value.replace(/[^\d]/g, '');
        
        // Ensure positive integer
        const num = parseInt(value) || 0;
        input.value = Math.max(0, num).toString();
    }
    
    // Modal Management
    openModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.remove('hidden');
            modal.classList.add('modal');
            
            // Focus first input
            const firstInput = modal.querySelector('input, select, textarea, button');
            if (firstInput) {
                setTimeout(() => firstInput.focus(), 100);
            }
            
            // Prevent body scroll
            document.body.style.overflow = 'hidden';
        }
    }
    
    closeModal(modal) {
        if (modal) {
            modal.classList.add('hidden');
            modal.classList.remove('modal');
            
            // Restore body scroll
            document.body.style.overflow = '';
        }
    }
    
    closeAllModals() {
        const modals = document.querySelectorAll('.modal:not(.hidden)');
        modals.forEach(modal => this.closeModal(modal));
    }
    
    // Navigation
    navigateTo(url) {
        window.location.href = url;
    }
    
    isPOSPage() {
        return window.location.pathname.includes('/pos');
    }
    
    // POS-specific functions
    handleQuantityChange(button) {
        const input = button.parentNode.querySelector('.quantity-input');
        if (!input) return;
        
        const currentValue = parseInt(input.value) || 0;
        const change = button.classList.contains('quantity-increase') ? 1 : -1;
        const newValue = Math.max(0, currentValue + change);
        
        input.value = newValue;
        input.dispatchEvent(new Event('change', { bubbles: true }));
    }
    
    handleProductSelection(productCard) {
        const productId = productCard.dataset.productId;
        const productName = productCard.dataset.productName;
        
        if (productId) {
            // Dispatch custom event for product selection
            document.dispatchEvent(new CustomEvent('productSelected', {
                detail: {
                    productId: productId,
                    productName: productName,
                    element: productCard
                }
            }));
            
            this.showNotification(`Added ${productName} to cart`, 'success', 2000);
        }
    }
    
    triggerNewTransaction() {
        const newTransactionBtn = document.getElementById('new-transaction-btn');
        if (newTransactionBtn) {
            newTransactionBtn.click();
        }
    }
    
    focusSearch() {
        const searchInput = document.getElementById('product-search');
        if (searchInput) {
            searchInput.focus();
            searchInput.select();
        }
    }
    
    openDiscountModal() {
        this.openModal('discount-modal');
    }
    
    proceedToCheckout() {
        const checkoutBtn = document.getElementById('checkout-btn');
        if (checkoutBtn && !checkoutBtn.disabled) {
            checkoutBtn.click();
        }
    }
    
    selectCategory(index) {
        const categoryButtons = document.querySelectorAll('.category-btn');
        if (categoryButtons[index]) {
            categoryButtons[index].click();
        }
    }
    
    // API Helpers
    async apiRequest(url, options = {}) {
        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        };
        
        const mergedOptions = {
            ...defaultOptions,
            ...options,
            headers: {
                ...defaultOptions.headers,
                ...options.headers
            }
        };
        
        try {
            const response = await fetch(url, mergedOptions);
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const contentType = response.headers.get('content-type');
            if (contentType && contentType.includes('application/json')) {
                return await response.json();
            } else {
                return await response.text();
            }
        } catch (error) {
            this.showNotification(`Request failed: ${error.message}`, 'error');
            throw error;
        }
    }
    
    // Local Storage Helpers
    saveToStorage(key, data) {
        try {
            localStorage.setItem(key, JSON.stringify(data));
        } catch (error) {
            console.warn('Failed to save to localStorage:', error);
        }
    }
    
    loadFromStorage(key, defaultValue = null) {
        try {
            const item = localStorage.getItem(key);
            return item ? JSON.parse(item) : defaultValue;
        } catch (error) {
            console.warn('Failed to load from localStorage:', error);
            return defaultValue;
        }
    }
    
    removeFromStorage(key) {
        try {
            localStorage.removeItem(key);
        } catch (error) {
            console.warn('Failed to remove from localStorage:', error);
        }
    }
    
    // Debounce utility
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
    
    // Throttle utility
    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }
}

// Auto-initialize POS utilities
document.addEventListener('DOMContentLoaded', function() {
    window.posUtils = new POSUtils();
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = POSUtils;
}

// Global access
window.POSUtils = POSUtils;