"""Audit service for SaaS POS System."""

from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any, Union
from flask import request, session, g
from flask_login import current_user
from app import db
from app.models.audit import AuditLog, AuditAction, AuditSeverity
from app.models.base import get_current_tenant


class AuditService:
    """Service for handling audit logging and security monitoring."""
    
    @staticmethod
    def log(action: AuditAction, description: str, 
            severity: AuditSeverity = AuditSeverity.LOW,
            user_id: Optional[int] = None,
            tenant_id: Optional[int] = None,
            old_values: Optional[Dict] = None,
            new_values: Optional[Dict] = None,
            resource_type: Optional[str] = None,
            resource_id: Optional[Union[str, int]] = None,
            success: bool = True,
            error_message: Optional[str] = None,
            correlation_id: Optional[str] = None) -> AuditLog:
        """
        Create an audit log entry with automatic context detection.
        
        Args:
            action: The audit action being performed
            description: Human-readable description of the action
            severity: Severity level of the action
            user_id: ID of the user performing the action (auto-detected if None)
            tenant_id: ID of the tenant (auto-detected if None)
            old_values: Previous state for update operations
            new_values: New state for update operations
            resource_type: Type of resource being affected
            resource_id: ID of the resource being affected
            success: Whether the action was successful
            error_message: Error message if action failed
            correlation_id: ID to correlate related operations
        
        Returns:
            Created AuditLog instance
        """
        
        # Auto-detect user and tenant if not provided
        if user_id is None and hasattr(current_user, 'id') and current_user.is_authenticated:
            user_id = current_user.id
        
        if tenant_id is None:
            # Try to get from current tenant context
            tenant_id = get_current_tenant()
            
            # Fallback to user's tenant
            if tenant_id is None and hasattr(current_user, 'tenant_id') and current_user.is_authenticated:
                tenant_id = current_user.tenant_id
        
        # Extract request information
        ip_address = None
        user_agent = None
        request_method = None
        request_url = None
        session_id = None
        
        if request:
            ip_address = AuditService._get_client_ip()
            user_agent = request.headers.get('User-Agent')
            request_method = request.method
            request_url = request.url
        
        if session:
            session_id = session.get('_id')
        
        return AuditLog.log_action(
            action=action,
            tenant_id=tenant_id or 0,  # Use 0 for system-wide actions
            description=description,
            user_id=user_id,
            severity=severity,
            ip_address=ip_address,
            user_agent=user_agent,
            request_method=request_method,
            request_url=request_url,
            old_values=old_values,
            new_values=new_values,
            resource_type=resource_type,
            resource_id=str(resource_id) if resource_id else None,
            success=success,
            error_message=error_message,
            session_id=session_id,
            correlation_id=correlation_id
        )
    
    @staticmethod
    def _get_client_ip() -> Optional[str]:
        """Get client IP address, handling proxies."""
        
        # Check for forwarded IP (behind proxy/load balancer)
        forwarded_ips = request.headers.get('X-Forwarded-For')
        if forwarded_ips:
            # Take the first IP (client IP)
            return forwarded_ips.split(',')[0].strip()
        
        # Check for real IP header
        real_ip = request.headers.get('X-Real-IP')
        if real_ip:
            return real_ip
        
        # Fallback to remote address
        return request.remote_addr
    
    @staticmethod
    def log_authentication(action: AuditAction, user_email: str, 
                          success: bool = True, error_message: Optional[str] = None,
                          user_id: Optional[int] = None, tenant_id: Optional[int] = None) -> AuditLog:
        """Log authentication-related events."""
        
        severity = AuditSeverity.MEDIUM if success else AuditSeverity.HIGH
        
        description = f"Authentication {action.value} for user {user_email}"
        if not success and error_message:
            description += f": {error_message}"
        
        return AuditService.log(
            action=action,
            description=description,
            severity=severity,
            user_id=user_id,
            tenant_id=tenant_id,
            success=success,
            error_message=error_message,
            resource_type='user',
            resource_id=str(user_id) if user_id else None,
            new_values={'email': user_email} if success else None
        )
    
    @staticmethod
    def log_data_change(action: AuditAction, resource_type: str, resource_id: Union[str, int],
                       description: str, old_values: Optional[Dict] = None, 
                       new_values: Optional[Dict] = None,
                       user_id: Optional[int] = None, tenant_id: Optional[int] = None) -> AuditLog:
        """Log data modification events."""
        
        return AuditService.log(
            action=action,
            description=description,
            severity=AuditSeverity.MEDIUM,
            user_id=user_id,
            tenant_id=tenant_id,
            old_values=old_values,
            new_values=new_values,
            resource_type=resource_type,
            resource_id=str(resource_id)
        )
    
    @staticmethod
    def log_transaction(action: AuditAction, transaction_id: int, amount: float,
                       description: Optional[str] = None,
                       user_id: Optional[int] = None, tenant_id: Optional[int] = None) -> AuditLog:
        """Log transaction-related events."""
        
        if not description:
            description = f"Transaction {action.value} for amount ${amount:.2f}"
        
        return AuditService.log(
            action=action,
            description=description,
            severity=AuditSeverity.MEDIUM,
            user_id=user_id,
            tenant_id=tenant_id,
            resource_type='transaction',
            resource_id=str(transaction_id),
            new_values={'amount': amount}
        )
    
    @staticmethod
    def log_security_event(action: AuditAction, description: str,
                          severity: AuditSeverity = AuditSeverity.HIGH,
                          user_id: Optional[int] = None, tenant_id: Optional[int] = None,
                          additional_data: Optional[Dict] = None) -> AuditLog:
        """Log security-related events."""
        
        return AuditService.log(
            action=action,
            description=description,
            severity=severity,
            user_id=user_id,
            tenant_id=tenant_id,
            success=False,  # Security events are typically failures
            new_values=additional_data
        )
    
    @staticmethod
    def log_system_event(action: AuditAction, description: str,
                        severity: AuditSeverity = AuditSeverity.LOW,
                        additional_data: Optional[Dict] = None) -> AuditLog:
        """Log system-level events."""
        
        return AuditService.log(
            action=action,
            description=description,
            severity=severity,
            tenant_id=0,  # System-wide
            new_values=additional_data
        )
    
    @staticmethod
    def get_audit_logs(tenant_id: int, limit: int = 100, offset: int = 0,
                      action: Optional[AuditAction] = None,
                      severity: Optional[AuditSeverity] = None,
                      start_date: Optional[datetime] = None,
                      end_date: Optional[datetime] = None,
                      user_id: Optional[int] = None,
                      resource_type: Optional[str] = None,
                      success_only: Optional[bool] = None) -> List[AuditLog]:
        """Get filtered audit logs for a tenant."""
        
        return AuditLog.get_logs_for_tenant(
            tenant_id=tenant_id,
            limit=limit,
            offset=offset,
            action=action,
            severity=severity,
            start_date=start_date,
            end_date=end_date,
            user_id=user_id
        )
    
    @staticmethod
    def get_security_events(tenant_id: Optional[int] = None, hours: int = 24) -> List[AuditLog]:
        """Get recent security events."""
        
        return AuditLog.get_security_events(tenant_id=tenant_id, hours=hours)
    
    @staticmethod
    def get_user_activity(user_id: int, tenant_id: int, days: int = 30) -> List[AuditLog]:
        """Get recent activity for a specific user."""
        
        since = datetime.utcnow() - timedelta(days=days)
        
        return AuditLog.query.filter(
            AuditLog.user_id == user_id,
            AuditLog.tenant_id == tenant_id,
            AuditLog.created_at >= since
        ).order_by(AuditLog.created_at.desc()).limit(100).all()
    
    @staticmethod
    def get_audit_summary(tenant_id: int, days: int = 30) -> Dict[str, Any]:
        """Get audit summary statistics for a tenant."""
        
        since = datetime.utcnow() - timedelta(days=days)
        
        # Total events
        total_events = AuditLog.query.filter(
            AuditLog.tenant_id == tenant_id,
            AuditLog.created_at >= since
        ).count()
        
        # Events by severity
        severity_counts = {}
        for severity in AuditSeverity:
            count = AuditLog.query.filter(
                AuditLog.tenant_id == tenant_id,
                AuditLog.severity == severity,
                AuditLog.created_at >= since
            ).count()
            severity_counts[severity.value] = count
        
        # Events by action type
        action_counts = {}
        for action in AuditAction:
            count = AuditLog.query.filter(
                AuditLog.tenant_id == tenant_id,
                AuditLog.action == action,
                AuditLog.created_at >= since
            ).count()
            if count > 0:
                action_counts[action.value] = count
        
        # Failed events
        failed_events = AuditLog.query.filter(
            AuditLog.tenant_id == tenant_id,
            AuditLog.success == False,
            AuditLog.created_at >= since
        ).count()
        
        # Unique users
        unique_users = db.session.query(AuditLog.user_id).filter(
            AuditLog.tenant_id == tenant_id,
            AuditLog.user_id.isnot(None),
            AuditLog.created_at >= since
        ).distinct().count()
        
        # Unique IP addresses
        unique_ips = db.session.query(AuditLog.ip_address).filter(
            AuditLog.tenant_id == tenant_id,
            AuditLog.ip_address.isnot(None),
            AuditLog.created_at >= since
        ).distinct().count()
        
        return {
            'period_days': days,
            'total_events': total_events,
            'failed_events': failed_events,
            'success_rate': round((total_events - failed_events) / total_events * 100, 2) if total_events > 0 else 100,
            'unique_users': unique_users,
            'unique_ip_addresses': unique_ips,
            'events_by_severity': severity_counts,
            'events_by_action': action_counts
        }
    
    @staticmethod
    def detect_suspicious_activity(tenant_id: int, hours: int = 24) -> List[Dict[str, Any]]:
        """Detect potentially suspicious activity patterns."""
        
        since = datetime.utcnow() - timedelta(hours=hours)
        suspicious_patterns = []
        
        # Multiple failed login attempts from same IP
        failed_logins = db.session.query(
            AuditLog.ip_address,
            db.func.count(AuditLog.id).label('count')
        ).filter(
            AuditLog.tenant_id == tenant_id,
            AuditLog.action == AuditAction.LOGIN_FAILED,
            AuditLog.created_at >= since,
            AuditLog.ip_address.isnot(None)
        ).group_by(AuditLog.ip_address).having(db.func.count(AuditLog.id) >= 5).all()
        
        for ip, count in failed_logins:
            suspicious_patterns.append({
                'type': 'multiple_failed_logins',
                'description': f'Multiple failed login attempts ({count}) from IP {ip}',
                'severity': 'high',
                'ip_address': ip,
                'count': count
            })
        
        # Unusual access times (outside business hours)
        unusual_times = AuditLog.query.filter(
            AuditLog.tenant_id == tenant_id,
            AuditLog.created_at >= since,
            db.or_(
                db.extract('hour', AuditLog.created_at) < 6,  # Before 6 AM
                db.extract('hour', AuditLog.created_at) > 22  # After 10 PM
            )
        ).count()
        
        if unusual_times > 10:  # Threshold for unusual activity
            suspicious_patterns.append({
                'type': 'unusual_access_times',
                'description': f'Unusual access times detected ({unusual_times} events)',
                'severity': 'medium',
                'count': unusual_times
            })
        
        # Multiple users from same IP (potential account sharing)
        shared_ips = db.session.query(
            AuditLog.ip_address,
            db.func.count(db.distinct(AuditLog.user_id)).label('user_count')
        ).filter(
            AuditLog.tenant_id == tenant_id,
            AuditLog.created_at >= since,
            AuditLog.ip_address.isnot(None),
            AuditLog.user_id.isnot(None)
        ).group_by(AuditLog.ip_address).having(
            db.func.count(db.distinct(AuditLog.user_id)) >= 5
        ).all()
        
        for ip, user_count in shared_ips:
            suspicious_patterns.append({
                'type': 'multiple_users_same_ip',
                'description': f'Multiple users ({user_count}) accessing from same IP {ip}',
                'severity': 'medium',
                'ip_address': ip,
                'user_count': user_count
            })
        
        # High volume of transactions from single user
        high_volume_users = db.session.query(
            AuditLog.user_id,
            db.func.count(AuditLog.id).label('transaction_count')
        ).filter(
            AuditLog.tenant_id == tenant_id,
            AuditLog.action == AuditAction.TRANSACTION_CREATED,
            AuditLog.created_at >= since,
            AuditLog.user_id.isnot(None)
        ).group_by(AuditLog.user_id).having(
            db.func.count(AuditLog.id) >= 100  # Threshold for high volume
        ).all()
        
        for user_id, count in high_volume_users:
            suspicious_patterns.append({
                'type': 'high_volume_transactions',
                'description': f'High volume of transactions ({count}) from user {user_id}',
                'severity': 'medium',
                'user_id': user_id,
                'count': count
            })
        
        return suspicious_patterns
    
    @staticmethod
    def cleanup_old_audit_logs(retention_days: int = 90) -> int:
        """Clean up old audit logs based on retention policy."""
        
        deleted_count = AuditLog.cleanup_old_logs(retention_days)
        
        # Log the cleanup action
        AuditService.log_system_event(
            action=AuditAction.SETTINGS_CHANGED,
            description=f"Cleaned up {deleted_count} old audit log records",
            severity=AuditSeverity.LOW,
            additional_data={
                'retention_days': retention_days,
                'deleted_count': deleted_count
            }
        )
        
        return deleted_count
    
    @staticmethod
    def export_audit_logs(tenant_id: int, start_date: datetime, end_date: datetime,
                         format: str = 'json') -> str:
        """Export audit logs for compliance or analysis."""
        
        logs = AuditLog.query.filter(
            AuditLog.tenant_id == tenant_id,
            AuditLog.created_at >= start_date,
            AuditLog.created_at <= end_date
        ).order_by(AuditLog.created_at.asc()).all()
        
        if format.lower() == 'json':
            import json
            
            export_data = {
                'export_info': {
                    'tenant_id': tenant_id,
                    'start_date': start_date.isoformat(),
                    'end_date': end_date.isoformat(),
                    'exported_at': datetime.utcnow().isoformat(),
                    'total_records': len(logs)
                },
                'audit_logs': []
            }
            
            for log in logs:
                export_data['audit_logs'].append({
                    'id': log.id,
                    'action': log.action.value,
                    'severity': log.severity.value,
                    'user_id': log.user_id,
                    'tenant_id': log.tenant_id,
                    'ip_address': log.ip_address,
                    'user_agent': log.user_agent,
                    'request_method': log.request_method,
                    'request_url': log.request_url,
                    'description': log.description,
                    'old_values': log.old_values,
                    'new_values': log.new_values,
                    'resource_type': log.resource_type,
                    'resource_id': log.resource_id,
                    'success': log.success,
                    'error_message': log.error_message,
                    'session_id': log.session_id,
                    'correlation_id': log.correlation_id,
                    'created_at': log.created_at.isoformat(),
                    'updated_at': log.updated_at.isoformat()
                })
            
            # Log the export action
            AuditService.log(
                action=AuditAction.DATA_EXPORTED,
                description=f"Exported {len(logs)} audit log records",
                severity=AuditSeverity.MEDIUM,
                tenant_id=tenant_id,
                resource_type='audit_log',
                new_values={
                    'start_date': start_date.isoformat(),
                    'end_date': end_date.isoformat(),
                    'record_count': len(logs),
                    'format': format
                }
            )
            
            return json.dumps(export_data, indent=2)
        
        else:
            raise ValueError(f"Unsupported export format: {format}")