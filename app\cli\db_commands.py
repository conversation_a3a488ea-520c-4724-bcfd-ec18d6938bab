"""Database CLI commands for optimization and maintenance."""

import click
from flask import current_app
from flask.cli import with_appcontext
from app import db
from app.utils.db_optimization import DatabaseOptimizer, QueryOptimizer
import logging

logger = logging.getLogger(__name__)


@click.group()
def db_optimize():
    """Database optimization commands."""
    pass


@db_optimize.command()
@with_appcontext
def analyze():
    """Analyze database performance and suggest optimizations."""
    click.echo("Analyzing database performance...")
    
    optimizations = DatabaseOptimizer.analyze_query_performance()
    
    if optimizations:
        click.echo("\nOptimization suggestions:")
        for suggestion in optimizations:
            click.echo(f"  • {suggestion}")
    else:
        click.echo("No optimization suggestions at this time.")
    
    # Show table statistics
    click.echo("\nTable statistics:")
    stats = DatabaseOptimizer.get_table_statistics()
    for table, data in stats.items():
        if 'error' in data:
            click.echo(f"  {table}: Error - {data['error']}")
        else:
            size_info = f" ({data.get('size', 'unknown size')})" if 'size' in data else ""
            click.echo(f"  {table}: {data['row_count']} rows{size_info}")


@db_optimize.command()
@with_appcontext
def create_views():
    """Create materialized views for reporting."""
    click.echo("Creating materialized views...")
    DatabaseOptimizer.create_materialized_views()
    click.echo("Materialized views created successfully.")


@db_optimize.command()
@with_appcontext
def refresh_views():
    """Refresh materialized views with latest data."""
    click.echo("Refreshing materialized views...")
    DatabaseOptimizer.refresh_materialized_views()
    click.echo("Materialized views refreshed successfully.")


@db_optimize.command()
@with_appcontext
def vacuum():
    """Run VACUUM ANALYZE on all tables (PostgreSQL only)."""
    click.echo("Running VACUUM ANALYZE on all tables...")
    DatabaseOptimizer.vacuum_analyze_tables()
    click.echo("VACUUM ANALYZE completed.")


@db_optimize.command()
@with_appcontext
def pool_info():
    """Show database connection pool information."""
    engine = db.engine
    
    click.echo("Database Connection Pool Information:")
    click.echo(f"  Engine: {engine.url.drivername}")
    click.echo(f"  Pool size: {getattr(engine.pool, 'size', lambda: 'N/A')()}")
    click.echo(f"  Max overflow: {getattr(engine.pool, 'max_overflow', lambda: 'N/A')()}")
    click.echo(f"  Pool timeout: {getattr(engine.pool, 'timeout', lambda: 'N/A')()}")
    click.echo(f"  Pool recycle: {getattr(engine.pool, 'recycle', lambda: 'N/A')()}")
    
    # Show pool recommendations
    recommendations = DatabaseOptimizer.optimize_connection_pool()
    if recommendations:
        click.echo("\nRecommendations:")
        for rec in recommendations:
            click.echo(f"  • {rec}")


@db_optimize.command()
@click.option('--tenant-id', type=int, required=True, help='Tenant ID for testing')
@with_appcontext
def test_queries(tenant_id):
    """Test optimized queries performance."""
    click.echo(f"Testing optimized queries for tenant {tenant_id}...")
    
    import time
    from datetime import datetime, timedelta
    
    # Test product query
    start_time = time.time()
    products = QueryOptimizer.get_optimized_product_query(tenant_id).limit(100).all()
    product_time = time.time() - start_time
    click.echo(f"  Product query: {len(products)} results in {product_time:.3f}s")
    
    # Test transaction query
    start_time = time.time()
    end_date = datetime.now()
    start_date = end_date - timedelta(days=30)
    transactions = QueryOptimizer.get_optimized_transaction_query(
        tenant_id, 
        {'date_from': start_date, 'date_to': end_date}
    ).limit(100).all()
    transaction_time = time.time() - start_time
    click.echo(f"  Transaction query: {len(transactions)} results in {transaction_time:.3f}s")
    
    # Test sales summary
    start_time = time.time()
    summary = QueryOptimizer.get_sales_summary_optimized(tenant_id, start_date, end_date)
    summary_time = time.time() - start_time
    click.echo(f"  Sales summary: {summary_time:.3f}s")
    click.echo(f"    Transactions: {summary['transaction_count']}")
    click.echo(f"    Total sales: ${summary['total_sales']:.2f}")
    
    # Test top products
    start_time = time.time()
    top_products = QueryOptimizer.get_top_products_optimized(tenant_id, start_date, end_date)
    top_products_time = time.time() - start_time
    click.echo(f"  Top products: {len(top_products)} results in {top_products_time:.3f}s")


def init_db_commands(app):
    """Initialize database CLI commands."""
    app.cli.add_command(db_optimize)