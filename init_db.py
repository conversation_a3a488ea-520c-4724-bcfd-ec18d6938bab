"""Database initialization script."""

from run import app
from app import db

def init_database():
    """Initialize the database."""
    with app.app_context():
        # Create all tables
        db.create_all()
        print("Database tables created successfully!")
        
        # Test database connection
        try:
            # Simple query to test connection
            result = db.session.execute(db.text("SELECT 1"))
            print("Database connection test successful!")
        except Exception as e:
            print(f"Database connection test failed: {e}")
        finally:
            db.session.close()

if __name__ == '__main__':
    print("Initializing database...")
    init_database()
    print("Database initialization complete!")