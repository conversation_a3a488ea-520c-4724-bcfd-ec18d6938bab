"""Add audit and backup tables

Revision ID: add_audit_backup_001
Revises: 
Create Date: 2024-01-01 00:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import sqlite

# revision identifiers
revision = 'add_audit_backup_001'
down_revision = None
depends_on = None


def upgrade():
    """Add audit and backup tables."""
    
    # Create audit_log table
    op.create_table('audit_log',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.Column('action', sa.Enum('LOGIN', 'LOGOUT', 'LOGIN_FAILED', 'PASSWORD_RESET', 'ACCOUNT_LOCKED', 
                                   'USER_CREATED', 'USER_UPDATED', 'USER_DELETED', 'USER_ROLE_CHANGED',
                                   'PRODUCT_CREATED', 'PRODUCT_UPDATED', 'PRODUCT_DELETED', 'INVENTORY_ADJUSTED',
                                   'TRANSACTION_CREATED', 'TRANSACTION_UPDATED', 'TRANSACTION_VOIDED', 'PAYMENT_PROCESSED',
                                   'BACKUP_CREATED', 'BACKUP_RESTORED', 'DATA_EXPORTED', 'SETTINGS_CHANGED',
                                   'UNAUTHORIZED_ACCESS', 'SUSPICIOUS_ACTIVITY', 'DATA_BREACH_ATTEMPT',
                                   name='auditaction'), nullable=False),
        sa.Column('severity', sa.Enum('LOW', 'MEDIUM', 'HIGH', 'CRITICAL', name='auditseverity'), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=True),
        sa.Column('tenant_id', sa.Integer(), nullable=False),
        sa.Column('ip_address', sa.String(length=45), nullable=True),
        sa.Column('user_agent', sa.Text(), nullable=True),
        sa.Column('request_method', sa.String(length=10), nullable=True),
        sa.Column('request_url', sa.Text(), nullable=True),
        sa.Column('description', sa.Text(), nullable=False),
        sa.Column('old_values', sa.JSON(), nullable=True),
        sa.Column('new_values', sa.JSON(), nullable=True),
        sa.Column('resource_type', sa.String(length=50), nullable=True),
        sa.Column('resource_id', sa.String(length=50), nullable=True),
        sa.Column('success', sa.Boolean(), nullable=False),
        sa.Column('error_message', sa.Text(), nullable=True),
        sa.Column('session_id', sa.String(length=100), nullable=True),
        sa.Column('correlation_id', sa.String(length=100), nullable=True),
        sa.ForeignKeyConstraint(['tenant_id'], ['tenant.id'], ),
        sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create indexes for audit_log
    op.create_index('idx_audit_tenant_action', 'audit_log', ['tenant_id', 'action'])
    op.create_index('idx_audit_tenant_created', 'audit_log', ['tenant_id', 'created_at'])
    op.create_index('idx_audit_user_action', 'audit_log', ['user_id', 'action'])
    op.create_index('idx_audit_severity_created', 'audit_log', ['severity', 'created_at'])
    op.create_index('idx_audit_resource', 'audit_log', ['resource_type', 'resource_id'])
    op.create_index('idx_audit_success_created', 'audit_log', ['success', 'created_at'])
    op.create_index('idx_audit_ip_created', 'audit_log', ['ip_address', 'created_at'])
    op.create_index(op.f('ix_audit_log_action'), 'audit_log', ['action'])
    op.create_index(op.f('ix_audit_log_severity'), 'audit_log', ['severity'])
    op.create_index(op.f('ix_audit_log_user_id'), 'audit_log', ['user_id'])
    op.create_index(op.f('ix_audit_log_tenant_id'), 'audit_log', ['tenant_id'])
    op.create_index(op.f('ix_audit_log_ip_address'), 'audit_log', ['ip_address'])
    op.create_index(op.f('ix_audit_log_resource_type'), 'audit_log', ['resource_type'])
    op.create_index(op.f('ix_audit_log_resource_id'), 'audit_log', ['resource_id'])
    op.create_index(op.f('ix_audit_log_success'), 'audit_log', ['success'])
    op.create_index(op.f('ix_audit_log_session_id'), 'audit_log', ['session_id'])
    op.create_index(op.f('ix_audit_log_correlation_id'), 'audit_log', ['correlation_id'])
    
    # Create backup_log table
    op.create_table('backup_log',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.Column('backup_type', sa.String(length=20), nullable=False),
        sa.Column('backup_status', sa.String(length=20), nullable=False),
        sa.Column('backup_filename', sa.String(length=255), nullable=False),
        sa.Column('backup_path', sa.Text(), nullable=False),
        sa.Column('backup_size', sa.BigInteger(), nullable=True),
        sa.Column('started_at', sa.DateTime(), nullable=False),
        sa.Column('completed_at', sa.DateTime(), nullable=True),
        sa.Column('duration_seconds', sa.Integer(), nullable=True),
        sa.Column('tenant_id', sa.Integer(), nullable=True),
        sa.Column('tables_backed_up', sa.JSON(), nullable=True),
        sa.Column('records_count', sa.Integer(), nullable=True),
        sa.Column('error_message', sa.Text(), nullable=True),
        sa.Column('error_details', sa.JSON(), nullable=True),
        sa.Column('checksum', sa.String(length=64), nullable=True),
        sa.Column('verified', sa.Boolean(), nullable=False),
        sa.Column('verification_date', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['tenant_id'], ['tenant.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create indexes for backup_log
    op.create_index('idx_backup_status_created', 'backup_log', ['backup_status', 'created_at'])
    op.create_index('idx_backup_type_created', 'backup_log', ['backup_type', 'created_at'])
    op.create_index('idx_backup_tenant_created', 'backup_log', ['tenant_id', 'created_at'])
    op.create_index('idx_backup_completed', 'backup_log', ['completed_at'])
    op.create_index(op.f('ix_backup_log_backup_type'), 'backup_log', ['backup_type'])
    op.create_index(op.f('ix_backup_log_backup_status'), 'backup_log', ['backup_status'])
    op.create_index(op.f('ix_backup_log_tenant_id'), 'backup_log', ['tenant_id'])


def downgrade():
    """Remove audit and backup tables."""
    
    # Drop backup_log table and indexes
    op.drop_index(op.f('ix_backup_log_tenant_id'), table_name='backup_log')
    op.drop_index(op.f('ix_backup_log_backup_status'), table_name='backup_log')
    op.drop_index(op.f('ix_backup_log_backup_type'), table_name='backup_log')
    op.drop_index('idx_backup_completed', table_name='backup_log')
    op.drop_index('idx_backup_tenant_created', table_name='backup_log')
    op.drop_index('idx_backup_type_created', table_name='backup_log')
    op.drop_index('idx_backup_status_created', table_name='backup_log')
    op.drop_table('backup_log')
    
    # Drop audit_log table and indexes
    op.drop_index(op.f('ix_audit_log_correlation_id'), table_name='audit_log')
    op.drop_index(op.f('ix_audit_log_session_id'), table_name='audit_log')
    op.drop_index(op.f('ix_audit_log_success'), table_name='audit_log')
    op.drop_index(op.f('ix_audit_log_resource_id'), table_name='audit_log')
    op.drop_index(op.f('ix_audit_log_resource_type'), table_name='audit_log')
    op.drop_index(op.f('ix_audit_log_ip_address'), table_name='audit_log')
    op.drop_index(op.f('ix_audit_log_tenant_id'), table_name='audit_log')
    op.drop_index(op.f('ix_audit_log_user_id'), table_name='audit_log')
    op.drop_index(op.f('ix_audit_log_severity'), table_name='audit_log')
    op.drop_index(op.f('ix_audit_log_action'), table_name='audit_log')
    op.drop_index('idx_audit_ip_created', table_name='audit_log')
    op.drop_index('idx_audit_success_created', table_name='audit_log')
    op.drop_index('idx_audit_resource', table_name='audit_log')
    op.drop_index('idx_audit_severity_created', table_name='audit_log')
    op.drop_index('idx_audit_user_action', table_name='audit_log')
    op.drop_index('idx_audit_tenant_created', table_name='audit_log')
    op.drop_index('idx_audit_tenant_action', table_name='audit_log')
    op.drop_table('audit_log')