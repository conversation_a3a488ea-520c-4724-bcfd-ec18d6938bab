"""Add performance indexes for better query optimization

Revision ID: add_performance_indexes
Revises: 
Create Date: 2025-01-16 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'add_performance_indexes'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    """Add indexes for better query performance."""
    
    # Product table indexes
    op.create_index('idx_product_tenant_active', 'product', ['tenant_id', 'is_active'])
    op.create_index('idx_product_tenant_category', 'product', ['tenant_id', 'category_id'])
    op.create_index('idx_product_tenant_featured', 'product', ['tenant_id', 'is_featured'])
    op.create_index('idx_product_stock_tracking', 'product', ['tenant_id', 'track_inventory', 'current_stock'])
    op.create_index('idx_product_low_stock', 'product', ['tenant_id', 'is_active', 'track_inventory', 'current_stock', 'minimum_stock'])
    op.create_index('idx_product_name_search', 'product', ['tenant_id', 'name'])
    op.create_index('idx_product_sort', 'product', ['tenant_id', 'sort_order', 'name'])
    
    # Category table indexes
    op.create_index('idx_category_tenant_active', 'category', ['tenant_id', 'is_active'])
    op.create_index('idx_category_parent', 'category', ['tenant_id', 'parent_id'])
    op.create_index('idx_category_sort', 'category', ['tenant_id', 'sort_order', 'name'])
    
    # Inventory movement table indexes
    op.create_index('idx_inventory_movement_product', 'inventory_movement', ['product_id', 'created_at'])
    op.create_index('idx_inventory_movement_tenant_type', 'inventory_movement', ['tenant_id', 'movement_type'])
    op.create_index('idx_inventory_movement_tenant_date', 'inventory_movement', ['tenant_id', 'created_at'])
    op.create_index('idx_inventory_movement_reference', 'inventory_movement', ['reference_id'])
    
    # Transaction table indexes (if exists)
    try:
        op.create_index('idx_transaction_tenant_status', 'transaction', ['tenant_id', 'status'])
        op.create_index('idx_transaction_tenant_date', 'transaction', ['tenant_id', 'created_at'])
        op.create_index('idx_transaction_user', 'transaction', ['user_id', 'created_at'])
    except:
        pass  # Table might not exist yet
    
    # Transaction item table indexes (if exists)
    try:
        op.create_index('idx_transaction_item_product', 'transaction_item', ['product_id'])
        op.create_index('idx_transaction_item_transaction', 'transaction_item', ['transaction_id'])
    except:
        pass  # Table might not exist yet
    
    # User table indexes
    try:
        op.create_index('idx_user_tenant', 'user', ['tenant_id'])
        op.create_index('idx_user_email_unique', 'user', ['email'], unique=True)
    except:
        pass  # Indexes might already exist
    
    # Tenant table indexes
    try:
        op.create_index('idx_tenant_business_type', 'tenant', ['business_type'])
        op.create_index('idx_tenant_subscription', 'tenant', ['subscription_status'])
    except:
        pass  # Table might not exist yet


def downgrade():
    """Remove performance indexes."""
    
    # Product table indexes
    op.drop_index('idx_product_tenant_active', table_name='product')
    op.drop_index('idx_product_tenant_category', table_name='product')
    op.drop_index('idx_product_tenant_featured', table_name='product')
    op.drop_index('idx_product_stock_tracking', table_name='product')
    op.drop_index('idx_product_low_stock', table_name='product')
    op.drop_index('idx_product_name_search', table_name='product')
    op.drop_index('idx_product_sort', table_name='product')
    
    # Category table indexes
    op.drop_index('idx_category_tenant_active', table_name='category')
    op.drop_index('idx_category_parent', table_name='category')
    op.drop_index('idx_category_sort', table_name='category')
    
    # Inventory movement table indexes
    op.drop_index('idx_inventory_movement_product', table_name='inventory_movement')
    op.drop_index('idx_inventory_movement_tenant_type', table_name='inventory_movement')
    op.drop_index('idx_inventory_movement_tenant_date', table_name='inventory_movement')
    op.drop_index('idx_inventory_movement_reference', table_name='inventory_movement')
    
    # Transaction table indexes
    try:
        op.drop_index('idx_transaction_tenant_status', table_name='transaction')
        op.drop_index('idx_transaction_tenant_date', table_name='transaction')
        op.drop_index('idx_transaction_user', table_name='transaction')
    except:
        pass
    
    # Transaction item table indexes
    try:
        op.drop_index('idx_transaction_item_product', table_name='transaction_item')
        op.drop_index('idx_transaction_item_transaction', table_name='transaction_item')
    except:
        pass
    
    # User table indexes
    try:
        op.drop_index('idx_user_tenant', table_name='user')
        op.drop_index('idx_user_email_unique', table_name='user')
    except:
        pass
    
    # Tenant table indexes
    try:
        op.drop_index('idx_tenant_business_type', table_name='tenant')
        op.drop_index('idx_tenant_subscription', table_name='tenant')
    except:
        pass