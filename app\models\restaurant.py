"""Restaurant-specific models for SaaS POS System."""

from datetime import datetime, timedelta
from enum import Enum
from app import db
from app.models.base import TenantAwareModel


class TableStatus(Enum):
    """Table status enumeration."""
    AVAILABLE = 'available'
    OCCUPIED = 'occupied'
    RESERVED = 'reserved'
    CLEANING = 'cleaning'
    OUT_OF_ORDER = 'out_of_order'


class OrderStatus(Enum):
    """Order status enumeration for kitchen display."""
    PENDING = 'pending'
    PREPARING = 'preparing'
    READY = 'ready'
    SERVED = 'served'
    CANCELLED = 'cancelled'


class Table(TenantAwareModel):
    """Table model for restaurant table management."""
    
    __tablename__ = 'restaurant_table'
    
    # Table identification
    table_number = db.Column(db.String(20), nullable=False)
    table_name = db.Column(db.String(50), nullable=True)  # Optional display name
    
    # Table properties
    capacity = db.Column(db.Integer, nullable=False, default=4)
    status = db.Column(db.Enum(TableStatus), nullable=False, default=TableStatus.AVAILABLE)
    
    # Location and grouping
    section = db.Column(db.String(50), nullable=True)  # e.g., "Patio", "Main Dining"
    floor = db.Column(db.String(20), nullable=True)
    position_x = db.Column(db.Integer, nullable=True)  # For visual layout
    position_y = db.Column(db.Integer, nullable=True)
    
    # Current occupancy
    current_party_size = db.Column(db.Integer, nullable=True)
    occupied_since = db.Column(db.DateTime, nullable=True)
    estimated_duration = db.Column(db.Integer, nullable=True)  # minutes
    
    # Reservation information
    reserved_for = db.Column(db.String(100), nullable=True)
    reserved_at = db.Column(db.DateTime, nullable=True)
    reservation_phone = db.Column(db.String(20), nullable=True)
    reservation_notes = db.Column(db.Text, nullable=True)
    
    # QR code for contactless ordering
    qr_code = db.Column(db.String(100), nullable=True)
    qr_code_enabled = db.Column(db.Boolean, nullable=False, default=False)
    
    # Unique constraint for table number within tenant
    __table_args__ = (
        db.UniqueConstraint('tenant_id', 'table_number', name='unique_table_per_tenant'),
    )
    
    def __repr__(self):
        return f'<Table {self.table_number} ({self.status.value})>'
    
    def is_available(self):
        """Check if table is available for seating."""
        return self.status == TableStatus.AVAILABLE
    
    def is_occupied(self):
        """Check if table is currently occupied."""
        return self.status == TableStatus.OCCUPIED
    
    def is_reserved(self):
        """Check if table is reserved."""
        return self.status == TableStatus.RESERVED
    
    def occupy_table(self, party_size, estimated_duration=None):
        """Mark table as occupied."""
        if not self.is_available():
            raise ValueError(f"Table {self.table_number} is not available")
        
        self.status = TableStatus.OCCUPIED
        self.current_party_size = party_size
        self.occupied_since = datetime.utcnow()
        self.estimated_duration = estimated_duration
        
        # Clear reservation info if it was reserved
        self.reserved_for = None
        self.reserved_at = None
        self.reservation_phone = None
        self.reservation_notes = None
        
        return self.save()
    
    def reserve_table(self, customer_name, phone=None, notes=None, reservation_time=None):
        """Reserve the table."""
        if not self.is_available():
            raise ValueError(f"Table {self.table_number} is not available for reservation")
        
        self.status = TableStatus.RESERVED
        self.reserved_for = customer_name
        self.reserved_at = reservation_time or datetime.utcnow()
        self.reservation_phone = phone
        self.reservation_notes = notes
        
        return self.save()
    
    def clear_table(self):
        """Clear table and make it available."""
        self.status = TableStatus.AVAILABLE
        self.current_party_size = None
        self.occupied_since = None
        self.estimated_duration = None
        self.reserved_for = None
        self.reserved_at = None
        self.reservation_phone = None
        self.reservation_notes = None
        
        return self.save()
    
    def set_cleaning(self):
        """Mark table as being cleaned."""
        self.status = TableStatus.CLEANING
        return self.save()
    
    def set_out_of_order(self):
        """Mark table as out of order."""
        self.status = TableStatus.OUT_OF_ORDER
        return self.save()
    
    def get_occupancy_duration(self):
        """Get how long the table has been occupied (in minutes)."""
        if not self.occupied_since:
            return 0
        
        duration = datetime.utcnow() - self.occupied_since
        return int(duration.total_seconds() / 60)
    
    def is_overdue(self):
        """Check if table occupancy is overdue based on estimated duration."""
        if not self.estimated_duration or not self.occupied_since:
            return False
        
        return self.get_occupancy_duration() > self.estimated_duration
    
    def get_estimated_available_time(self):
        """Get estimated time when table will be available."""
        if not self.is_occupied() or not self.estimated_duration:
            return None
        
        return self.occupied_since + timedelta(minutes=self.estimated_duration)
    
    def generate_qr_code(self):
        """Generate QR code for contactless ordering."""
        import secrets
        self.qr_code = secrets.token_urlsafe(16)
        self.qr_code_enabled = True
        return self.save()
    
    def disable_qr_code(self):
        """Disable QR code for this table."""
        self.qr_code_enabled = False
        return self.save()
    
    def get_current_orders(self):
        """Get current active orders for this table."""
        from app.models.restaurant import Order
        return Order.query.filter_by(
            tenant_id=self.tenant_id,
            table_id=self.id,
            status=OrderStatus.PENDING
        ).all()
    
    def get_active_transaction(self):
        """Get active transaction for this table."""
        from app.models.transaction import Transaction, TransactionStatus
        return Transaction.query.filter_by(
            tenant_id=self.tenant_id,
            table_number=self.table_number,
            status=TransactionStatus.PENDING
        ).first()
    
    @classmethod
    def get_available_tables(cls, tenant_id, capacity=None):
        """Get all available tables, optionally filtered by capacity."""
        query = cls.query.filter_by(tenant_id=tenant_id, status=TableStatus.AVAILABLE)
        if capacity:
            query = query.filter(cls.capacity >= capacity)
        return query.order_by(cls.table_number).all()
    
    @classmethod
    def get_occupied_tables(cls, tenant_id):
        """Get all occupied tables."""
        return cls.query.filter_by(
            tenant_id=tenant_id,
            status=TableStatus.OCCUPIED
        ).order_by(cls.table_number).all()
    
    @classmethod
    def get_reserved_tables(cls, tenant_id):
        """Get all reserved tables."""
        return cls.query.filter_by(
            tenant_id=tenant_id,
            status=TableStatus.RESERVED
        ).order_by(cls.reserved_at).all()
    
    @classmethod
    def create_table(cls, tenant_id, table_number, capacity=4, **kwargs):
        """Create a new table."""
        table = cls(
            tenant_id=tenant_id,
            table_number=table_number,
            capacity=capacity,
            **kwargs
        )
        return table.save()


class Order(TenantAwareModel):
    """Order model for kitchen display and order tracking."""
    
    __tablename__ = 'restaurant_order'
    
    # Order identification
    order_number = db.Column(db.String(50), nullable=False, unique=True, index=True)
    
    # References
    transaction_id = db.Column(db.Integer, db.ForeignKey('transaction.id'), nullable=True)
    transaction = db.relationship('Transaction', backref='restaurant_orders')
    table_id = db.Column(db.Integer, db.ForeignKey('restaurant_table.id'), nullable=True)
    table = db.relationship('Table', backref='orders')
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    user = db.relationship('User', backref='restaurant_orders')
    
    # Order details
    order_type = db.Column(db.String(20), nullable=False, default='dine_in')  # dine_in, takeout, delivery
    status = db.Column(db.Enum(OrderStatus), nullable=False, default=OrderStatus.PENDING)
    priority = db.Column(db.Integer, nullable=False, default=0)  # Higher number = higher priority
    
    # Customer information
    customer_name = db.Column(db.String(100), nullable=True)
    customer_phone = db.Column(db.String(20), nullable=True)
    
    # Timing
    order_time = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)
    preparation_started_at = db.Column(db.DateTime, nullable=True)
    ready_at = db.Column(db.DateTime, nullable=True)
    served_at = db.Column(db.DateTime, nullable=True)
    estimated_ready_time = db.Column(db.DateTime, nullable=True)
    
    # Special instructions
    special_instructions = db.Column(db.Text, nullable=True)
    kitchen_notes = db.Column(db.Text, nullable=True)
    
    # Relationships
    items = db.relationship('OrderItem', backref='order_ref', lazy=True, cascade='all, delete-orphan')
    
    def __repr__(self):
        return f'<Order {self.order_number} ({self.status.value})>'
    
    def start_preparation(self):
        """Mark order as being prepared."""
        if self.status != OrderStatus.PENDING:
            raise ValueError("Can only start preparation for pending orders")
        
        self.status = OrderStatus.PREPARING
        self.preparation_started_at = datetime.utcnow()
        return self.save()
    
    def mark_ready(self):
        """Mark order as ready for serving."""
        if self.status != OrderStatus.PREPARING:
            raise ValueError("Can only mark preparing orders as ready")
        
        self.status = OrderStatus.READY
        self.ready_at = datetime.utcnow()
        return self.save()
    
    def mark_served(self):
        """Mark order as served."""
        if self.status != OrderStatus.READY:
            raise ValueError("Can only mark ready orders as served")
        
        self.status = OrderStatus.SERVED
        self.served_at = datetime.utcnow()
        return self.save()
    
    def cancel_order(self, reason=None):
        """Cancel the order."""
        if self.status in [OrderStatus.SERVED, OrderStatus.CANCELLED]:
            raise ValueError("Cannot cancel served or already cancelled orders")
        
        self.status = OrderStatus.CANCELLED
        if reason:
            self.kitchen_notes = f"Cancelled: {reason}"
        return self.save()
    
    def get_preparation_time(self):
        """Get preparation time in minutes."""
        if not self.preparation_started_at:
            return 0
        
        end_time = self.ready_at or datetime.utcnow()
        duration = end_time - self.preparation_started_at
        return int(duration.total_seconds() / 60)
    
    def get_total_time(self):
        """Get total time from order to ready in minutes."""
        if not self.ready_at:
            return None
        
        duration = self.ready_at - self.order_time
        return int(duration.total_seconds() / 60)
    
    def get_wait_time(self):
        """Get current wait time in minutes."""
        if self.status == OrderStatus.SERVED:
            return self.get_total_time()
        
        duration = datetime.utcnow() - self.order_time
        return int(duration.total_seconds() / 60)
    
    def is_overdue(self):
        """Check if order is overdue based on estimated ready time."""
        if not self.estimated_ready_time:
            return False
        
        return datetime.utcnow() > self.estimated_ready_time and self.status not in [OrderStatus.READY, OrderStatus.SERVED]
    
    def add_item(self, product, quantity, special_instructions=None):
        """Add an item to the order."""
        # Ensure order is saved to database first
        if not self.id:
            self.save()
        
        item = OrderItem(
            order_id=self.id,
            product_id=product.id,
            tenant_id=self.tenant_id,
            quantity=quantity,
            product_name=product.name,
            special_instructions=special_instructions
        )
        return item.save()
    
    def get_table_display_name(self):
        """Get display name for the table."""
        if self.table:
            return self.table.table_name or f"Table {self.table.table_number}"
        return "No Table"
    
    @classmethod
    def generate_order_number(cls, tenant_id):
        """Generate a unique order number."""
        import random
        
        # Format: ORD-HHMMSS-XXX
        now = datetime.now()
        time_part = now.strftime("%H%M%S")
        random_part = f"{random.randint(100, 999)}"
        
        order_number = f"ORD-{time_part}-{random_part}"
        
        # Ensure uniqueness
        while cls.query.filter_by(order_number=order_number).first():
            random_part = f"{random.randint(100, 999)}"
            order_number = f"ORD-{time_part}-{random_part}"
        
        return order_number
    
    @classmethod
    def get_kitchen_display_orders(cls, tenant_id, limit=20):
        """Get orders for kitchen display."""
        return cls.query.filter(
            cls.tenant_id == tenant_id,
            cls.status.in_([OrderStatus.PENDING, OrderStatus.PREPARING, OrderStatus.READY])
        ).order_by(
            cls.priority.desc(),
            cls.order_time.asc()
        ).limit(limit).all()
    
    @classmethod
    def get_pending_orders(cls, tenant_id):
        """Get all pending orders."""
        return cls.query.filter_by(
            tenant_id=tenant_id,
            status=OrderStatus.PENDING
        ).order_by(cls.order_time.asc()).all()
    
    @classmethod
    def get_orders_by_table(cls, tenant_id, table_id):
        """Get all orders for a specific table."""
        return cls.query.filter_by(
            tenant_id=tenant_id,
            table_id=table_id
        ).order_by(cls.order_time.desc()).all()


class OrderItem(TenantAwareModel):
    """Individual items within a restaurant order."""
    
    __tablename__ = 'restaurant_order_item'
    
    # References
    order_id = db.Column(db.Integer, db.ForeignKey('restaurant_order.id'), nullable=False)
    product_id = db.Column(db.Integer, db.ForeignKey('product.id'), nullable=False)
    product = db.relationship('Product', backref='restaurant_order_items')
    
    # Item details
    quantity = db.Column(db.Integer, nullable=False, default=1)
    product_name = db.Column(db.String(200), nullable=False)  # Snapshot
    special_instructions = db.Column(db.Text, nullable=True)
    
    # Status tracking
    status = db.Column(db.Enum(OrderStatus), nullable=False, default=OrderStatus.PENDING)
    started_at = db.Column(db.DateTime, nullable=True)
    completed_at = db.Column(db.DateTime, nullable=True)
    
    def __repr__(self):
        return f'<OrderItem {self.product_name} x{self.quantity}>'
    
    def start_preparation(self):
        """Mark item as being prepared."""
        self.status = OrderStatus.PREPARING
        self.started_at = datetime.utcnow()
        return self.save()
    
    def mark_ready(self):
        """Mark item as ready."""
        self.status = OrderStatus.READY
        self.completed_at = datetime.utcnow()
        return self.save()
    
    def get_preparation_time(self):
        """Get preparation time in minutes."""
        if not self.started_at or not self.completed_at:
            return None
        
        duration = self.completed_at - self.started_at
        return int(duration.total_seconds() / 60)