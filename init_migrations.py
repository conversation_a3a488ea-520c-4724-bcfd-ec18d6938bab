"""Initialize Flask-Migrate for database migrations."""

import os
from flask_migrate import init, migrate, upgrade
from run import app
from app import db

def init_migrations():
    """Initialize Flask-Migrate."""
    with app.app_context():
        # Initialize migrations if not already done
        if not os.path.exists('migrations'):
            print("Initializing Flask-Migrate...")
            init()
            print("Flask-Migrate initialized successfully!")
        else:
            print("Flask-Migrate already initialized.")
        
        # Create initial migration
        try:
            print("Creating initial migration...")
            migrate(message='Initial migration with base models')
            print("Initial migration created successfully!")
        except Exception as e:
            print(f"Migration creation failed or already exists: {e}")
        
        # Apply migrations
        try:
            print("Applying migrations...")
            upgrade()
            print("Migrations applied successfully!")
        except Exception as e:
            print(f"Migration application failed: {e}")

if __name__ == '__main__':
    print("Setting up database migrations...")
    init_migrations()
    print("Migration setup complete!")