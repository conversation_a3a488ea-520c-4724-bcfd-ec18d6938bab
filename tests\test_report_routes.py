"""Integration tests for report routes."""

import pytest
from datetime import datetime, timedelta
from flask import url_for
from app.models.user import User, Tenant
from app.models.product import Product, Category
from app.models.transaction import Transaction, TransactionItem, TransactionStatus, PaymentMethod
from app import db


@pytest.fixture
def test_tenant(app):
    """Create a test tenant."""
    tenant = Tenant(
        name="Test Business",
        business_type="retail",
        subscription_status="active"
    )
    db.session.add(tenant)
    db.session.commit()
    return tenant


@pytest.fixture
def test_user(app, test_tenant):
    """Create a test user."""
    user = User(
        email="<EMAIL>",
        first_name="Test",
        last_name="User",
        tenant_id=test_tenant.id,
        role="admin"
    )
    user.set_password("testpass123")
    db.session.add(user)
    db.session.commit()
    return user


@pytest.fixture
def test_category(app, test_tenant):
    """Create a test category."""
    category = Category(
        name="Test Category",
        description="Test category description",
        tenant_id=test_tenant.id
    )
    db.session.add(category)
    db.session.commit()
    return category


@pytest.fixture
def test_products(app, test_tenant, test_category):
    """Create test products."""
    products = []
    for i in range(3):
        product = Product(
            name=f"Test Product {i+1}",
            sku=f"TEST-{i+1:03d}",
            selling_price=10.00 + i,
            cost_price=5.00 + i,
            current_stock=100 - (i * 10),
            minimum_stock=10,
            maximum_stock=200,
            category_id=test_category.id,
            tenant_id=test_tenant.id,
            track_inventory=True,
            is_active=True
        )
        db.session.add(product)
        products.append(product)
    
    db.session.commit()
    return products


@pytest.fixture
def test_transactions(app, test_tenant, test_user, test_products):
    """Create test transactions."""
    transactions = []
    base_date = datetime.utcnow() - timedelta(days=7)
    
    for i in range(5):
        transaction = Transaction(
            tenant_id=test_tenant.id,
            user_id=test_user.id,
            status=TransactionStatus.COMPLETED,
            payment_method=PaymentMethod.CASH,
            subtotal=0,
            tax_amount=0,
            discount_amount=0,
            total_amount=0,
            customer_name=f"Customer {i+1}" if i < 3 else None,
            customer_email=f"customer{i+1}@example.com" if i < 2 else None,
            customer_phone=f"555-000{i+1}" if i < 2 else None,
            created_at=base_date + timedelta(days=i),
            completed_at=base_date + timedelta(days=i)
        )
        db.session.add(transaction)
        db.session.flush()  # Get the ID
        
        # Add transaction items
        total = 0
        for j, product in enumerate(test_products[:2]):  # Use first 2 products
            quantity = j + 1
            line_total = product.selling_price * quantity
            
            item = TransactionItem(
                transaction_id=transaction.id,
                product_id=product.id,
                quantity=quantity,
                unit_price=product.selling_price,
                line_total=line_total,
                tax_amount=line_total * 0.1,
                discount_amount=0
            )
            db.session.add(item)
            total += line_total
        
        # Update transaction totals
        transaction.subtotal = total
        transaction.tax_amount = total * 0.1
        transaction.total_amount = total + transaction.tax_amount
        
        transactions.append(transaction)
    
    db.session.commit()
    return transactions


class TestReportRoutes:
    """Test report routes functionality."""
    
    def test_reports_dashboard_access_requires_login(self, client):
        """Test that reports dashboard requires login."""
        response = client.get('/reports/')
        assert response.status_code == 302  # Redirect to login
    
    def test_reports_dashboard_loads(self, client, test_user, test_transactions):
        """Test that reports dashboard loads successfully."""
        with client.session_transaction() as sess:
            sess['_user_id'] = str(test_user.id)
            sess['_fresh'] = True
        
        response = client.get('/reports/')
        assert response.status_code == 200
        assert b'Reports Dashboard' in response.data
        assert b'Total Revenue' in response.data
        assert b'Total Transactions' in response.data
    
    def test_reports_dashboard_with_date_filter(self, client, test_user, test_transactions):
        """Test reports dashboard with date filtering."""
        with client.session_transaction() as sess:
            sess['_user_id'] = str(test_user.id)
            sess['_fresh'] = True
        
        start_date = (datetime.utcnow() - timedelta(days=5)).strftime('%Y-%m-%d')
        end_date = datetime.utcnow().strftime('%Y-%m-%d')
        
        response = client.get(f'/reports/?start_date={start_date}&end_date={end_date}')
        assert response.status_code == 200
        assert b'Reports Dashboard' in response.data
    
    def test_sales_report_loads(self, client, test_user, test_transactions):
        """Test that sales report loads successfully."""
        with client.session_transaction() as sess:
            sess['_user_id'] = str(test_user.id)
            sess['_fresh'] = True
        
        response = client.get('/reports/sales')
        assert response.status_code == 200
        assert b'Sales Report' in response.data
        assert b'Summary Metrics' in response.data
        assert b'Daily Sales Trend' in response.data
    
    def test_product_performance_report_loads(self, client, test_user, test_transactions):
        """Test that product performance report loads successfully."""
        with client.session_transaction() as sess:
            sess['_user_id'] = str(test_user.id)
            sess['_fresh'] = True
        
        response = client.get('/reports/products')
        assert response.status_code == 200
        assert b'Product Performance Report' in response.data
        assert b'Product Performance Details' in response.data
    
    def test_product_performance_with_filters(self, client, test_user, test_transactions):
        """Test product performance report with filters."""
        with client.session_transaction() as sess:
            sess['_user_id'] = str(test_user.id)
            sess['_fresh'] = True
        
        response = client.get('/reports/products?sort_by=quantity&limit=25')
        assert response.status_code == 200
        assert b'Product Performance Report' in response.data
    
    def test_inventory_report_loads(self, client, test_user, test_products):
        """Test that inventory report loads successfully."""
        with client.session_transaction() as sess:
            sess['_user_id'] = str(test_user.id)
            sess['_fresh'] = True
        
        response = client.get('/reports/inventory')
        assert response.status_code == 200
        assert b'Inventory Report' in response.data
        assert b'Total Products' in response.data
        assert b'In Stock' in response.data
    
    def test_inventory_report_with_filters(self, client, test_user, test_products):
        """Test inventory report with filters."""
        with client.session_transaction() as sess:
            sess['_user_id'] = str(test_user.id)
            sess['_fresh'] = True
        
        response = client.get('/reports/inventory?low_stock_threshold=20&include_out_of_stock=1')
        assert response.status_code == 200
        assert b'Inventory Report' in response.data
    
    def test_customer_report_loads(self, client, test_user, test_transactions):
        """Test that customer report loads successfully."""
        with client.session_transaction() as sess:
            sess['_user_id'] = str(test_user.id)
            sess['_fresh'] = True
        
        response = client.get('/reports/customers')
        assert response.status_code == 200
        assert b'Customer Report' in response.data
        assert b'Top Customers' in response.data
    
    def test_customer_report_with_filters(self, client, test_user, test_transactions):
        """Test customer report with filters."""
        with client.session_transaction() as sess:
            sess['_user_id'] = str(test_user.id)
            sess['_fresh'] = True
        
        start_date = (datetime.utcnow() - timedelta(days=5)).strftime('%Y-%m-%d')
        end_date = datetime.utcnow().strftime('%Y-%m-%d')
        
        response = client.get(f'/reports/customers?start_date={start_date}&end_date={end_date}&limit=10')
        assert response.status_code == 200
        assert b'Customer Report' in response.data


class TestReportExports:
    """Test report export functionality."""
    
    def test_sales_csv_export(self, client, test_user, test_transactions):
        """Test sales report CSV export."""
        with client.session_transaction() as sess:
            sess['_user_id'] = str(test_user.id)
            sess['_fresh'] = True
        
        response = client.get('/reports/export/sales/csv')
        assert response.status_code == 200
        assert response.headers['Content-Type'] == 'text/csv'
        assert 'sales_report_' in response.headers['Content-Disposition']
        assert b'Sales Summary Report' in response.data
    
    def test_sales_pdf_export(self, client, test_user, test_transactions):
        """Test sales report PDF export."""
        with client.session_transaction() as sess:
            sess['_user_id'] = str(test_user.id)
            sess['_fresh'] = True
        
        response = client.get('/reports/export/sales/pdf')
        assert response.status_code == 200
        assert response.headers['Content-Type'] == 'application/pdf'
        assert 'sales_report_' in response.headers['Content-Disposition']
    
    def test_products_csv_export(self, client, test_user, test_transactions):
        """Test product performance CSV export."""
        with client.session_transaction() as sess:
            sess['_user_id'] = str(test_user.id)
            sess['_fresh'] = True
        
        response = client.get('/reports/export/products/csv')
        assert response.status_code == 200
        assert response.headers['Content-Type'] == 'text/csv'
        assert 'product_performance_' in response.headers['Content-Disposition']
        assert b'Product Performance Report' in response.data
    
    def test_products_pdf_export(self, client, test_user, test_transactions):
        """Test product performance PDF export."""
        with client.session_transaction() as sess:
            sess['_user_id'] = str(test_user.id)
            sess['_fresh'] = True
        
        response = client.get('/reports/export/products/pdf')
        assert response.status_code == 200
        assert response.headers['Content-Type'] == 'application/pdf'
        assert 'product_performance_' in response.headers['Content-Disposition']
    
    def test_inventory_csv_export(self, client, test_user, test_products):
        """Test inventory report CSV export."""
        with client.session_transaction() as sess:
            sess['_user_id'] = str(test_user.id)
            sess['_fresh'] = True
        
        response = client.get('/reports/export/inventory/csv')
        assert response.status_code == 200
        assert response.headers['Content-Type'] == 'text/csv'
        assert 'inventory_report_' in response.headers['Content-Disposition']
        assert b'Inventory Report' in response.data
    
    def test_inventory_pdf_export(self, client, test_user, test_products):
        """Test inventory report PDF export."""
        with client.session_transaction() as sess:
            sess['_user_id'] = str(test_user.id)
            sess['_fresh'] = True
        
        response = client.get('/reports/export/inventory/pdf')
        assert response.status_code == 200
        assert response.headers['Content-Type'] == 'application/pdf'
        assert 'inventory_report_' in response.headers['Content-Disposition']
    
    def test_customers_csv_export(self, client, test_user, test_transactions):
        """Test customer report CSV export."""
        with client.session_transaction() as sess:
            sess['_user_id'] = str(test_user.id)
            sess['_fresh'] = True
        
        response = client.get('/reports/export/customers/csv')
        assert response.status_code == 200
        assert response.headers['Content-Type'] == 'text/csv'
        assert 'customer_report_' in response.headers['Content-Disposition']
        assert b'Customer Report' in response.data
    
    def test_customers_pdf_export(self, client, test_user, test_transactions):
        """Test customer report PDF export."""
        with client.session_transaction() as sess:
            sess['_user_id'] = str(test_user.id)
            sess['_fresh'] = True
        
        response = client.get('/reports/export/customers/pdf')
        assert response.status_code == 200
        assert response.headers['Content-Type'] == 'application/pdf'
        assert 'customer_report_' in response.headers['Content-Disposition']
    
    def test_export_with_date_filters(self, client, test_user, test_transactions):
        """Test export with date filters."""
        with client.session_transaction() as sess:
            sess['_user_id'] = str(test_user.id)
            sess['_fresh'] = True
        
        start_date = (datetime.utcnow() - timedelta(days=5)).strftime('%Y-%m-%d')
        end_date = datetime.utcnow().strftime('%Y-%m-%d')
        
        response = client.get(f'/reports/export/sales/csv?start_date={start_date}&end_date={end_date}')
        assert response.status_code == 200
        assert response.headers['Content-Type'] == 'text/csv'
    
    def test_invalid_export_format(self, client, test_user, test_transactions):
        """Test invalid export format returns error."""
        with client.session_transaction() as sess:
            sess['_user_id'] = str(test_user.id)
            sess['_fresh'] = True
        
        response = client.get('/reports/export/sales/xml')
        assert response.status_code == 400
        assert b'Invalid export format' in response.data
    
    def test_export_requires_login(self, client):
        """Test that export endpoints require login."""
        response = client.get('/reports/export/sales/csv')
        assert response.status_code == 302  # Redirect to login
    
    def test_export_with_no_data(self, client, test_user):
        """Test export with no data returns appropriate response."""
        with client.session_transaction() as sess:
            sess['_user_id'] = str(test_user.id)
            sess['_fresh'] = True
        
        # Request data from far future to ensure no results
        start_date = (datetime.utcnow() + timedelta(days=30)).strftime('%Y-%m-%d')
        end_date = (datetime.utcnow() + timedelta(days=60)).strftime('%Y-%m-%d')
        
        response = client.get(f'/reports/export/sales/csv?start_date={start_date}&end_date={end_date}')
        assert response.status_code == 200
        assert response.headers['Content-Type'] == 'text/csv'
        # Should still contain headers even with no data
        assert b'Sales Summary Report' in response.data


class TestReportSecurity:
    """Test report security and access control."""
    
    def test_tenant_isolation(self, app, client):
        """Test that reports are isolated by tenant."""
        with app.app_context():
            # Create two tenants with users
            tenant1 = Tenant(name="Tenant 1", business_type="retail")
            tenant2 = Tenant(name="Tenant 2", business_type="restaurant")
            db.session.add_all([tenant1, tenant2])
            db.session.commit()
            
            user1 = User(
                email="<EMAIL>",
                first_name="User",
                last_name="One",
                tenant_id=tenant1.id,
                role="admin"
            )
            user1.set_password("testpass123")
            
            user2 = User(
                email="<EMAIL>",
                first_name="User",
                last_name="Two",
                tenant_id=tenant2.id,
                role="admin"
            )
            user2.set_password("testpass123")
            
            db.session.add_all([user1, user2])
            db.session.commit()
            
            # Create products for each tenant
            product1 = Product(
                name="Tenant 1 Product",
                sku="T1-001",
                selling_price=10.00,
                cost_price=5.00,
                current_stock=100,
                tenant_id=tenant1.id,
                is_active=True
            )
            
            product2 = Product(
                name="Tenant 2 Product",
                sku="T2-001",
                selling_price=15.00,
                cost_price=7.50,
                current_stock=50,
                tenant_id=tenant2.id,
                is_active=True
            )
            
            db.session.add_all([product1, product2])
            db.session.commit()
            
            # Test that user1 only sees tenant1 data
            with client.session_transaction() as sess:
                sess['_user_id'] = str(user1.id)
                sess['_fresh'] = True
            
            response = client.get('/reports/inventory')
            assert response.status_code == 200
            assert b'Tenant 1 Product' in response.data
            assert b'Tenant 2 Product' not in response.data
            
            # Test that user2 only sees tenant2 data
            with client.session_transaction() as sess:
                sess['_user_id'] = str(user2.id)
                sess['_fresh'] = True
            
            response = client.get('/reports/inventory')
            assert response.status_code == 200
            assert b'Tenant 2 Product' in response.data
            assert b'Tenant 1 Product' not in response.data
    
    def test_unauthorized_access_redirects(self, client):
        """Test that unauthorized access redirects to login."""
        endpoints = [
            '/reports/',
            '/reports/sales',
            '/reports/products',
            '/reports/inventory',
            '/reports/customers',
            '/reports/export/sales/csv',
            '/reports/export/products/pdf'
        ]
        
        for endpoint in endpoints:
            response = client.get(endpoint)
            assert response.status_code == 302  # Redirect to login