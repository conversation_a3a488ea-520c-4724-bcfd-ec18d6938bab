"""Base model class with tenant isolation for SaaS POS System."""

from datetime import datetime
from flask import g
from flask_sqlalchemy import SQLAlchemy
from sqlalchemy import event, and_, Index
from sqlalchemy.orm import Query, joinedload, selectinload
from sqlalchemy.ext.declarative import declared_attr
from app import db


class TenantQuery(Query):
    """Custom query class that automatically filters by tenant_id."""
    
    def get(self, ident):
        """Override get to include tenant filtering."""
        # For get operations, we still need to respect tenant isolation
        obj = super().get(ident)
        if obj and hasattr(obj, 'tenant_id') and hasattr(g, 'current_tenant_id'):
            if obj.tenant_id != g.current_tenant_id:
                return None
        return obj
    
    def filter_by_tenant(self, tenant_id=None):
        """Apply tenant filtering to the query."""
        if tenant_id is None:
            tenant_id = getattr(g, 'current_tenant_id', None)
        
        if tenant_id and hasattr(self.column_descriptions[0]['type'], 'tenant_id'):
            return self.filter(self.column_descriptions[0]['type'].tenant_id == tenant_id)
        return self
    
    def with_tenant_isolation(self):
        """Automatically apply tenant isolation if current tenant is set."""
        current_tenant = getattr(g, 'current_tenant_id', None)
        if current_tenant and hasattr(self.column_descriptions[0]['type'], 'tenant_id'):
            return self.filter(self.column_descriptions[0]['type'].tenant_id == current_tenant)
        return self
    
    def optimized_load(self, *relationships):
        """Apply optimized loading for relationships."""
        query = self
        for rel in relationships:
            if isinstance(rel, str):
                query = query.options(joinedload(rel))
            else:
                query = query.options(rel)
        return query
    
    def paginate_optimized(self, page=1, per_page=20, error_out=True):
        """Optimized pagination with count query optimization."""
        # Use more efficient count query for large datasets
        total = self.order_by(None).count()
        items = self.offset((page - 1) * per_page).limit(per_page).all()
        
        return {
            'items': items,
            'total': total,
            'page': page,
            'per_page': per_page,
            'pages': (total + per_page - 1) // per_page,
            'has_prev': page > 1,
            'has_next': page * per_page < total
        }


class BaseModel(db.Model):
    """Base model class with common fields and tenant isolation."""
    
    __abstract__ = True
    
    # Common fields for all models
    id = db.Column(db.Integer, primary_key=True)
    created_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def save(self):
        """Save the model instance to the database."""
        try:
            db.session.add(self)
            db.session.commit()
            return self
        except Exception as e:
            db.session.rollback()
            raise e
    
    def delete(self):
        """Delete the model instance from the database."""
        try:
            db.session.delete(self)
            db.session.commit()
            return True
        except Exception as e:
            db.session.rollback()
            raise e
    
    def to_dict(self):
        """Convert model instance to dictionary."""
        result = {}
        for column in self.__table__.columns:
            value = getattr(self, column.name)
            if isinstance(value, datetime):
                value = value.isoformat()
            result[column.name] = value
        return result
    
    @classmethod
    def create(cls, **kwargs):
        """Create a new instance of the model."""
        instance = cls(**kwargs)
        return instance.save()


class TenantMixin:
    """Mixin class for models that need tenant isolation."""
    
    tenant_id = db.Column(db.Integer, db.ForeignKey('tenant.id'), nullable=False, index=True)
    
    # Use custom query class for tenant filtering
    query_class = TenantQuery
    
    def __init__(self, *args, **kwargs):
        """Initialize with automatic tenant_id assignment."""
        # If tenant_id is not provided and we have a current tenant, use it
        if 'tenant_id' not in kwargs and hasattr(g, 'current_tenant_id') and g.current_tenant_id:
            kwargs['tenant_id'] = g.current_tenant_id
        super().__init__(*args, **kwargs)


class TenantAwareModel(TenantMixin, BaseModel):
    """Base model class with tenant isolation."""
    
    __abstract__ = True


# Event listeners for automatic tenant filtering
@event.listens_for(db.session, 'before_flush')
def before_flush(session, flush_context, instances):
    """Ensure tenant_id is set before flushing to database."""
    if not hasattr(g, 'current_tenant_id') or not g.current_tenant_id:
        return
    
    for instance in session.new:
        if hasattr(instance, 'tenant_id') and not instance.tenant_id:
            instance.tenant_id = g.current_tenant_id


def set_current_tenant(tenant_id):
    """Set the current tenant ID in the Flask g object."""
    g.current_tenant_id = tenant_id


def get_current_tenant():
    """Get the current tenant ID from the Flask g object."""
    return getattr(g, 'current_tenant_id', None)


def clear_current_tenant():
    """Clear the current tenant ID from the Flask g object."""
    if hasattr(g, 'current_tenant_id'):
        delattr(g, 'current_tenant_id')