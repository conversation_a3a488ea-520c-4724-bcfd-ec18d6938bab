# Design Document

## Overview

This design document outlines the approach to fix the tenant relationship issue in the SaaS POS system. The current problem is that the navigation template is trying to access `current_user.tenant.name`, but the User model has a relationship with the Tenant model through the `tenant_ref` backref instead of a direct `tenant` attribute. This causes a Jinja2 UndefinedError when trying to render the navigation template.

## Architecture

The SaaS POS system follows a multi-tenant architecture where each user belongs to a specific tenant. The relationship between users and tenants is defined in the User model with a foreign key to the Tenant model. The current implementation uses SQLAlchemy's relationship feature with a backref named `tenant_ref` instead of `tenant`, which is causing the template rendering error.

## Components and Interfaces

### User Model

The User model currently defines the tenant relationship as follows:

```python
# Tenant relationship
tenant_id = db.Column(db.Integer, db.ForeignKey('tenant.id'), nullable=False, index=True)
```

The Tenant model defines the relationship with users as:

```python
# Relationships
users = db.relationship('User', backref='tenant_ref', lazy=True, cascade='all, delete-orphan')
```

### Navigation Template

The navigation template is trying to access the tenant name using:

```html
<div class="text-xs text-gray-500">Tenant: {{ current_user.tenant.name }}</div>
```

## Design Decision

There are two possible approaches to fix this issue:

1. **Update the template to use the correct attribute name**: Change all occurrences of `current_user.tenant` to `current_user.tenant_ref` in the templates.

2. **Update the model to use the expected attribute name**: Change the backref in the Tenant model from `tenant_ref` to `tenant` to match what the templates are expecting.

After careful consideration, we've decided to go with option 2 (updating the model) for the following reasons:

- It's more intuitive to use `tenant` as the attribute name for the tenant relationship
- It requires fewer changes (only one change in the model vs. potentially multiple changes in templates)
- It follows the convention used in the templates, which is more natural (`user.tenant` vs. `user.tenant_ref`)
- It's less likely to introduce new errors in other parts of the application that might also be using `current_user.tenant`

## Implementation Details

The implementation will involve:

1. Updating the `users` relationship in the Tenant model to use `tenant` as the backref name instead of `tenant_ref`
2. Ensuring that any code that might be explicitly using `tenant_ref` is updated to use `tenant` instead

## Error Handling

If there are any other parts of the application that explicitly use `tenant_ref`, they will need to be updated to use `tenant` instead. However, based on the error message, it seems that the application is expecting `tenant` to be the attribute name, so this change should resolve the issue without introducing new ones.

## Testing Strategy

1. **Unit Tests**: Update any unit tests that might be using `tenant_ref` to use `tenant` instead
2. **Integration Tests**: Test the navigation template rendering to ensure it correctly displays the tenant name
3. **Manual Testing**: Verify that the dashboard loads correctly and the tenant name is displayed in the navigation bar