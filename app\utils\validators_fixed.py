"""Input validation utilities for SaaS POS System."""

import re
from decimal import Decimal, InvalidOperation
from wtforms import ValidationError
from flask import request
import html


def validate_email(form, field):
    """Validate email format."""
    if not field.data:
        return
    
    email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    if not re.match(email_pattern, field.data):
        raise ValidationError('Invalid email address format.')
    
    if len(field.data) > 254:
        raise ValidationError('Email address is too long.')
    
    # Additional validation for double dots and other edge cases
    if '..' in field.data or field.data.startswith('.') or field.data.endswith('.'):
        raise ValidationError('Invalid email address format.')


def validate_password_strength(form, field):
    """Validate password strength."""
    if not field.data:
        return
    
    password = field.data
    
    if len(password) < 8:
        raise ValidationError('Password must be at least 8 characters long.')
    
    if len(password) > 128:
        raise ValidationError('Password cannot exceed 128 characters.')
    
    if not re.search(r'[A-Z]', password):
        raise ValidationError('Password must contain at least one uppercase letter.')
    
    if not re.search(r'[a-z]', password):
        raise ValidationError('Password must contain at least one lowercase letter.')
    
    if not re.search(r'\d', password):
        raise ValidationError('Password must contain at least one digit.')
    
    # Check for common weak passwords
    weak_passwords = ['password', '12345678', 'qwerty123', 'admin123']
    if password.lower() in weak_passwords:
        raise ValidationError('Password is too common. Please choose a stronger password.')


def validate_phone_number(form, field):
    """Validate phone number format."""
    if not field.data:
        return
    
    # Remove all non-digit characters for validation
    digits_only = re.sub(r'\D', '', field.data)
    
    # Check if it's a valid US phone number (10 or 11 digits)
    if len(digits_only) not in [10, 11]:
        raise ValidationError('Phone number must be 10 or 11 digits.')
    
    # If 11 digits, first digit should be 1
    if len(digits_only) == 11 and not digits_only.startswith('1'):
        raise ValidationError('Invalid phone number format.')


def validate_positive_number(form, field):
    """Validate that number is positive."""
    if field.data is not None and field.data <= 0:
        raise ValidationError('Value must be positive.')


def validate_non_negative_number(form, field):
    """Validate that number is non-negative."""
    if field.data is not None and field.data < 0:
        raise ValidationError('Value cannot be negative.')


def validate_business_name(form, field):
    """Validate business name."""
    if not field.data:
        raise ValidationError('Business name is required.')
    
    name = field.data.strip()
    if len(name) < 2:
        raise ValidationError('Business name must be at least 2 characters long.')
    
    if len(name) > 100:
        raise ValidationError('Business name cannot exceed 100 characters.')
    
    # Check for valid characters (letters, numbers, spaces, common punctuation)
    if not re.match(r'^[a-zA-Z0-9\s\-\.\,\&\'\"]+$', name):
        raise ValidationError('Business name contains invalid characters.')


def validate_product_name(form, field):
    """Validate product name."""
    if not field.data:
        raise ValidationError('Product name is required.')
    
    name = field.data.strip()
    if len(name) < 1:
        raise ValidationError('Product name is required.')
    
    if len(name) > 200:
        raise ValidationError('Product name cannot exceed 200 characters.')


def validate_sku(form, field):
    """Validate SKU format."""
    if not field.data:
        return
    
    sku = field.data.strip()
    if len(sku) > 50:
        raise ValidationError('SKU cannot exceed 50 characters.')
    
    # SKU should contain only alphanumeric characters, hyphens, and underscores
    if not re.match(r'^[a-zA-Z0-9\-_]+$', sku):
        raise ValidationError('SKU can only contain letters, numbers, hyphens, and underscores.')


def validate_barcode(form, field):
    """Validate barcode format."""
    if not field.data:
        return
    
    barcode = field.data.strip()
    if len(barcode) > 50:
        raise ValidationError('Barcode cannot exceed 50 characters.')
    
    # Barcode should contain only digits
    if not re.match(r'^\d+$', barcode):
        raise ValidationError('Barcode can only contain digits.')


def validate_price(form, field):
    """Validate price values."""
    if field.data is None:
        return
    
    try:
        price = Decimal(str(field.data))
        if price < 0:
            raise ValidationError('Price cannot be negative.')
        if price > Decimal('999999.99'):
            raise ValidationError('Price cannot exceed $999,999.99.')
    except (InvalidOperation, ValueError):
        raise ValidationError('Invalid price format.')


def validate_stock_quantity(form, field):
    """Validate stock quantity."""
    if field.data is None:
        return
    
    if not isinstance(field.data, int):
        raise ValidationError('Stock quantity must be a whole number.')
    
    if field.data < 0:
        raise ValidationError('Stock quantity cannot be negative.')
    
    if field.data > 999999:
        raise ValidationError('Stock quantity cannot exceed 999,999.')


def validate_tax_rate(form, field):
    """Validate tax rate percentage."""
    if field.data is None:
        return
    
    try:
        rate = Decimal(str(field.data))
        if rate < 0:
            raise ValidationError('Tax rate cannot be negative.')
        if rate > 100:
            raise ValidationError('Tax rate cannot exceed 100%.')
    except (InvalidOperation, ValueError):
        raise ValidationError('Invalid tax rate format.')


def validate_discount_value(form, field):
    """Validate discount value."""
    if field.data is None:
        return
    
    try:
        discount = Decimal(str(field.data))
        if discount < 0:
            raise ValidationError('Discount value cannot be negative.')
        if discount > Decimal('999999.99'):
            raise ValidationError('Discount value is too large.')
    except (InvalidOperation, ValueError):
        raise ValidationError('Invalid discount value format.')


def validate_name_field(form, field):
    """Validate name fields (first name, last name)."""
    if not field.data:
        raise ValidationError('This field is required.')
    
    name = field.data.strip()
    if len(name) < 1:
        raise ValidationError('This field is required.')
    
    if len(name) > 50:
        raise ValidationError('Name cannot exceed 50 characters.')
    
    # Names should only contain letters, spaces, hyphens, and apostrophes
    if not re.match(r'^[a-zA-Z\s\-\']+$', name):
        raise ValidationError('Name can only contain letters, spaces, hyphens, and apostrophes.')


def validate_tenant_id(form, field):
    """Validate tenant ID."""
    if field.data is None:
        raise ValidationError('Tenant ID is required.')
    
    if not isinstance(field.data, int) or field.data <= 0:
        raise ValidationError('Invalid tenant ID.')


def validate_url(form, field):
    """Validate URL format."""
    if not field.data:
        return
    
    url_pattern = r'^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$'
    if not re.match(url_pattern, field.data):
        raise ValidationError('Invalid URL format.')


def validate_color_hex(form, field):
    """Validate hex color code."""
    if not field.data:
        return
    
    color_pattern = r'^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$'
    if not re.match(color_pattern, field.data):
        raise ValidationError('Invalid color format. Use hex format like #FF0000.')


def sanitize_input(text):
    """Comprehensive input sanitization."""
    if not text:
        return text
    
    # Convert to string if not already
    text = str(text)
    
    # HTML escape to prevent XSS
    text = html.escape(text)
    
    # Remove null bytes and other control characters
    text = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', text)
    
    # Strip whitespace
    text = text.strip()
    
    return text


def validate_json_input(data, required_fields=None, max_length=None):
    """Validate JSON input data."""
    if not isinstance(data, dict):
        raise ValueError('Invalid JSON data format.')
    
    # Check required fields
    if required_fields:
        missing_fields = [field for field in required_fields if field not in data or not data[field]]
        if missing_fields:
            raise ValueError(f'Missing required fields: {", ".join(missing_fields)}')
    
    # Sanitize all string values
    sanitized_data = {}
    for key, value in data.items():
        if isinstance(value, str):
            sanitized_data[key] = sanitize_input(value)
            
            # Check max length if specified
            if max_length and len(sanitized_data[key]) > max_length:
                raise ValueError(f'Field {key} exceeds maximum length of {max_length} characters.')
        else:
            sanitized_data[key] = value
    
    return sanitized_data


def validate_search_query(query):
    """Validate search query input."""
    if not query:
        return ''
    
    # Sanitize the query
    query = sanitize_input(query)
    
    # Limit length
    if len(query) > 100:
        query = query[:100]
    
    # Remove SQL injection patterns
    dangerous_patterns = [
        r'(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)',
        r'(--|\/\*|\*\/)',
        r'(\bOR\b.*=.*\bOR\b)',
        r'(\bAND\b.*=.*\bAND\b)',
        r'(\'.*\')',
        r'(;)'
    ]
    
    for pattern in dangerous_patterns:
        query = re.sub(pattern, '', query, flags=re.IGNORECASE)
    
    return query.strip()


def validate_pagination_params(page=1, per_page=20):
    """Validate pagination parameters."""
    try:
        page = int(page) if page else 1
        per_page = int(per_page) if per_page else 20
    except (ValueError, TypeError):
        page = 1
        per_page = 20
    
    # Ensure reasonable limits
    page = max(1, page)
    per_page = max(1, min(100, per_page))  # Limit to 100 items per page
    
    return page, per_page


def validate_date_range(start_date, end_date):
    """Validate date range inputs."""
    from datetime import datetime, timedelta
    
    errors = []
    
    if start_date:
        try:
            start_date = datetime.strptime(start_date, '%Y-%m-%d')
        except ValueError:
            errors.append('Invalid start date format. Use YYYY-MM-DD.')
            start_date = None
    
    if end_date:
        try:
            end_date = datetime.strptime(end_date, '%Y-%m-%d')
            # Set to end of day
            end_date = end_date.replace(hour=23, minute=59, second=59)
        except ValueError:
            errors.append('Invalid end date format. Use YYYY-MM-DD.')
            end_date = None
    
    # Validate date range logic
    if start_date and end_date and start_date > end_date:
        errors.append('Start date cannot be after end date.')
    
    # Prevent excessively large date ranges (more than 2 years)
    if start_date and end_date:
        if (end_date - start_date).days > 730:
            errors.append('Date range cannot exceed 2 years.')
    
    return start_date, end_date, errors


class CSRFProtection:
    """Simple CSRF protection for API endpoints."""
    
    @staticmethod
    def validate_csrf_token():
        """Validate CSRF token from request headers."""
        from flask import session, request
        
        # Skip CSRF validation for GET requests
        if request.method == 'GET':
            return True
        
        # Get token from header or form data
        token = request.headers.get('X-CSRF-Token') or request.form.get('csrf_token')
        session_token = session.get('csrf_token')
        
        if not token or not session_token or token != session_token:
            return False
        
        return True
    
    @staticmethod
    def generate_csrf_token():
        """Generate a new CSRF token."""
        import secrets
        from flask import session
        
        token = secrets.token_urlsafe(32)
        session['csrf_token'] = token
        return token