"""Decorators for SaaS POS System."""

from functools import wraps
from flask import request, jsonify, flash, redirect, url_for
from flask_login import current_user, login_required
from app.utils.validators import (
    validate_json_input, validate_search_query, validate_pagination_params,
    CSRFProtection, sanitize_input
)


def validate_json_request(required_fields=None, max_length=1000):
    """Decorator to validate JSON request data."""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if request.is_json:
                try:
                    data = request.get_json()
                    validated_data = validate_json_input(
                        data, 
                        required_fields=required_fields,
                        max_length=max_length
                    )
                    # Replace request data with validated data
                    request.validated_json = validated_data
                except ValueError as e:
                    return jsonify({'error': str(e)}), 400
            return f(*args, **kwargs)
        return decorated_function
    return decorator


def validate_form_request():
    """Decorator to validate and sanitize form data."""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if request.method == 'POST' and request.form:
                # Sanitize all form inputs
                sanitized_form = {}
                for key, value in request.form.items():
                    if isinstance(value, str):
                        sanitized_form[key] = sanitize_input(value)
                    else:
                        sanitized_form[key] = value
                
                # Store sanitized form data
                request.sanitized_form = sanitized_form
            return f(*args, **kwargs)
        return decorated_function
    return decorator


def validate_search_params():
    """Decorator to validate search and pagination parameters."""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # Validate search query
            search = request.args.get('search', '')
            if search:
                search = validate_search_query(search)
            
            # Validate pagination
            page = request.args.get('page', 1)
            per_page = request.args.get('per_page', 20)
            page, per_page = validate_pagination_params(page, per_page)
            
            # Store validated parameters
            request.validated_search = search
            request.validated_page = page
            request.validated_per_page = per_page
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator


def csrf_protect():
    """Decorator to protect routes with CSRF validation."""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not CSRFProtection.validate_csrf_token():
                if request.is_json:
                    return jsonify({'error': 'CSRF token validation failed'}), 403
                else:
                    flash('Security validation failed. Please try again.', 'error')
                    return redirect(url_for('main.index'))
            return f(*args, **kwargs)
        return decorated_function
    return decorator


def validate_numeric_params(*param_names):
    """Decorator to validate numeric URL parameters."""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            for param_name in param_names:
                if param_name in kwargs:
                    try:
                        # Ensure the parameter is a positive integer
                        value = int(kwargs[param_name])
                        if value <= 0:
                            raise ValueError(f"Invalid {param_name}")
                        kwargs[param_name] = value
                    except (ValueError, TypeError):
                        if request.is_json:
                            return jsonify({'error': f'Invalid {param_name}'}), 400
                        else:
                            flash(f'Invalid {param_name}', 'error')
                            return redirect(url_for('main.index'))
            return f(*args, **kwargs)
        return decorated_function
    return decorator


def rate_limit(max_requests=60, window=60):
    """Simple rate limiting decorator."""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # This is a simplified rate limiter
            # In production, you'd want to use Redis or a proper rate limiting library
            from flask import session
            import time
            
            current_time = time.time()
            session_key = f'rate_limit_{f.__name__}'
            
            if session_key not in session:
                session[session_key] = []
            
            # Clean old requests
            session[session_key] = [
                req_time for req_time in session[session_key] 
                if current_time - req_time < window
            ]
            
            # Check rate limit
            if len(session[session_key]) >= max_requests:
                if request.is_json:
                    return jsonify({'error': 'Rate limit exceeded'}), 429
                else:
                    flash('Too many requests. Please try again later.', 'error')
                    return redirect(url_for('main.index'))
            
            # Add current request
            session[session_key].append(current_time)
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator


def tenant_required(f):
    """Decorator to ensure user belongs to a tenant and tenant is active."""
    @wraps(f)
    @login_required
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated:
            if request.is_json:
                return jsonify({'error': 'Authentication required'}), 401
            else:
                flash('Please log in to access this page.', 'error')
                return redirect(url_for('auth.login'))
        
        # Check if user has a tenant
        if not hasattr(current_user, 'tenant_id') or not current_user.tenant_id:
            if request.is_json:
                return jsonify({'error': 'No tenant associated with user'}), 403
            else:
                flash('No tenant associated with your account.', 'error')
                return redirect(url_for('main.index'))
        
        # Check if tenant is active
        if hasattr(current_user, 'tenant') and current_user.tenant:
            if not current_user.tenant.is_active():
                if request.is_json:
                    return jsonify({'error': 'Tenant account is inactive'}), 403
                else:
                    flash('Your tenant account is inactive.', 'error')
                    return redirect(url_for('main.index'))
        
        return f(*args, **kwargs)
    return decorated_function


def admin_required(f):
    """Decorator to ensure user has admin privileges."""
    @wraps(f)
    @tenant_required
    def decorated_function(*args, **kwargs):
        if not current_user.is_admin():
            if request.is_json:
                return jsonify({'error': 'Admin privileges required'}), 403
            else:
                flash('Admin privileges required.', 'error')
                return redirect(url_for('main.index'))
        
        return f(*args, **kwargs)
    return decorated_function


def manager_required(f):
    """Decorator to ensure user has manager or admin privileges."""
    @wraps(f)
    @tenant_required
    def decorated_function(*args, **kwargs):
        if not current_user.is_manager():
            if request.is_json:
                return jsonify({'error': 'Manager privileges required'}), 403
            else:
                flash('Manager privileges required.', 'error')
                return redirect(url_for('main.index'))
        
        return f(*args, **kwargs)
    return decorated_function