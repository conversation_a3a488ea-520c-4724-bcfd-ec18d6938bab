"""Integration tests for inventory routes."""

import pytest
from flask import url_for
from app import create_app, db
from app.models.user import User, Tenant
from app.models.product import Product, Category
from config import TestingConfig


@pytest.fixture
def app():
    """Create test application."""
    app = create_app(TestingConfig)
    return app


@pytest.fixture
def client(app):
    """Create test client."""
    return app.test_client()


@pytest.fixture
def app_context(app):
    """Create application context."""
    with app.app_context():
        db.create_all()
        yield app
        db.session.remove()
        db.drop_all()


@pytest.fixture
def sample_tenant(app_context):
    """Create a sample tenant for testing."""
    tenant = Tenant.create(
        name='Test Business',
        business_type='retail',
        email='<EMAIL>',
        subscription_status='active'
    )
    return tenant


@pytest.fixture
def sample_user(app_context, sample_tenant):
    """Create a sample user for testing."""
    user = User.create_user(
        email='<EMAIL>',
        password='testpassword123',
        first_name='<PERSON>',
        last_name='Doe',
        tenant_id=sample_tenant.id,
        role='admin'
    )
    return user


@pytest.fixture
def sample_category(app_context, sample_tenant):
    """Create a sample category for testing."""
    category = Category(
        tenant_id=sample_tenant.id,
        name="Test Category",
        description="Test category description"
    )
    category.save()
    return category


@pytest.fixture
def sample_product(app_context, sample_tenant, sample_category):
    """Create a sample product for testing."""
    product = Product(
        tenant_id=sample_tenant.id,
        name="Test Product",
        description="Test product description",
        sku="TEST001",
        category_id=sample_category.id,
        selling_price=10.00,
        cost_price=5.00,
        current_stock=100,
        minimum_stock=10
    )
    product.save()
    return product


@pytest.fixture
def authenticated_client(client, app_context, sample_user, sample_tenant):
    """Create an authenticated client."""
    # Login the user
    client.post('/auth/login', data={
        'email': '<EMAIL>',
        'password': 'testpassword123',
        'tenant_id': sample_tenant.id
    })
    return client


class TestInventoryRoutes:
    """Test inventory route endpoints."""

    def test_inventory_index_requires_auth(self, client):
        """Test that inventory index requires authentication."""
        response = client.get(url_for('inventory.index'))
        assert response.status_code == 302  # Redirect to login

    def test_inventory_index_authenticated(self, authenticated_client, sample_tenant, sample_product):
        """Test inventory index with authenticated user."""
        response = authenticated_client.get(url_for('inventory.index'))
        assert response.status_code == 200
        assert b'Inventory Dashboard' in response.data
        assert sample_product.name.encode() in response.data

    def test_inventory_index_with_search(self, authenticated_client, sample_product):
        """Test inventory index with search functionality."""
        response = authenticated_client.get(
            url_for('inventory.index', search='Test')
        )
        assert response.status_code == 200
        assert sample_product.name.encode() in response.data

    def test_inventory_index_with_category_filter(self, authenticated_client, sample_product, sample_category):
        """Test inventory index with category filter."""
        response = authenticated_client.get(
            url_for('inventory.index', category_id=sample_category.id)
        )
        assert response.status_code == 200
        assert sample_product.name.encode() in response.data

    def test_inventory_index_low_stock_filter(self, authenticated_client, app_context, sample_tenant):
        """Test inventory index with low stock filter."""
        # Create a low stock product
        low_stock_product = Product(
            tenant_id=sample_tenant.id,
            name="Low Stock Product",
            selling_price=10.00,
            current_stock=5,
            minimum_stock=10
        )
        low_stock_product.save()

        response = authenticated_client.get(
            url_for('inventory.index', low_stock=True)
        )
        assert response.status_code == 200
        assert b'Low Stock Product' in response.data

    def test_products_list(self, authenticated_client, sample_product):
        """Test products listing page."""
        response = authenticated_client.get(url_for('inventory.products'))
        assert response.status_code == 200
        assert b'Products' in response.data
        assert sample_product.name.encode() in response.data

    def test_new_product_get(self, authenticated_client, sample_category):
        """Test GET request to new product form."""
        response = authenticated_client.get(url_for('inventory.new_product'))
        assert response.status_code == 200
        assert b'Add Product' in response.data
        assert sample_category.name.encode() in response.data

    def test_new_product_post_valid(self, authenticated_client, sample_category):
        """Test POST request to create new product with valid data."""
        product_data = {
            'name': 'New Test Product',
            'description': 'New product description',
            'sku': 'NEW001',
            'category_id': sample_category.id,
            'selling_price': 15.00,
            'cost_price': 8.00,
            'current_stock': 50,
            'minimum_stock': 5,
            'is_active': 'on'
        }
        
        response = authenticated_client.post(
            url_for('inventory.new_product'),
            data=product_data,
            follow_redirects=True
        )
        assert response.status_code == 200
        assert b'Product created successfully' in response.data

    def test_new_product_post_invalid(self, authenticated_client):
        """Test POST request to create new product with invalid data."""
        product_data = {
            'name': '',  # Missing required name
            'selling_price': 'invalid'  # Invalid price
        }
        
        response = authenticated_client.post(
            url_for('inventory.new_product'),
            data=product_data
        )
        assert response.status_code == 200
        assert b'Add Product' in response.data

    def test_view_product(self, authenticated_client, sample_product):
        """Test product detail view."""
        response = authenticated_client.get(
            url_for('inventory.view_product', product_id=sample_product.id)
        )
        assert response.status_code == 200
        assert sample_product.name.encode() in response.data
        assert sample_product.description.encode() in response.data

    def test_view_product_not_found(self, authenticated_client):
        """Test viewing non-existent product."""
        response = authenticated_client.get(
            url_for('inventory.view_product', product_id=99999),
            follow_redirects=True
        )
        assert response.status_code == 200
        assert b'Product not found' in response.data

    def test_edit_product_get(self, authenticated_client, sample_product, sample_category):
        """Test GET request to edit product form."""
        response = authenticated_client.get(
            url_for('inventory.edit_product', product_id=sample_product.id)
        )
        assert response.status_code == 200
        assert b'Edit Product' in response.data
        assert sample_product.name.encode() in response.data

    def test_edit_product_post_valid(self, authenticated_client, sample_product):
        """Test POST request to update product with valid data."""
        updated_data = {
            'name': 'Updated Product Name',
            'description': 'Updated description',
            'sku': sample_product.sku,
            'selling_price': 12.00,
            'cost_price': 6.00,
            'minimum_stock': 15,
            'is_active': 'on'
        }
        
        response = authenticated_client.post(
            url_for('inventory.edit_product', product_id=sample_product.id),
            data=updated_data,
            follow_redirects=True
        )
        assert response.status_code == 200
        assert b'Product updated successfully' in response.data

    def test_delete_product(self, authenticated_client, sample_product):
        """Test product deletion (soft delete)."""
        response = authenticated_client.post(
            url_for('inventory.delete_product', product_id=sample_product.id),
            follow_redirects=True
        )
        assert response.status_code == 200
        assert b'Product deleted successfully' in response.data

    def test_adjust_stock_get(self, authenticated_client, sample_product):
        """Test GET request to stock adjustment form."""
        response = authenticated_client.get(
            url_for('inventory.adjust_stock', product_id=sample_product.id)
        )
        assert response.status_code == 200
        assert b'Adjust Stock' in response.data
        assert sample_product.name.encode() in response.data

    def test_adjust_stock_post_valid(self, authenticated_client, sample_product):
        """Test POST request to adjust stock with valid data."""
        adjustment_data = {
            'quantity': 25,
            'reason': 'manual_adjustment',
            'notes': 'Test stock adjustment'
        }
        
        response = authenticated_client.post(
            url_for('inventory.adjust_stock', product_id=sample_product.id),
            data=adjustment_data,
            follow_redirects=True
        )
        assert response.status_code == 200
        assert b'Stock adjusted successfully' in response.data

    def test_adjust_stock_invalid_quantity(self, authenticated_client, sample_product):
        """Test stock adjustment with invalid quantity."""
        adjustment_data = {
            'quantity': 'invalid',
            'reason': 'manual_adjustment'
        }
        
        response = authenticated_client.post(
            url_for('inventory.adjust_stock', product_id=sample_product.id),
            data=adjustment_data
        )
        assert response.status_code == 200
        assert b'Invalid quantity value' in response.data

    def test_categories_list(self, authenticated_client, sample_category):
        """Test categories listing page."""
        response = authenticated_client.get(url_for('inventory.categories'))
        assert response.status_code == 200
        assert b'Categories' in response.data
        assert sample_category.name.encode() in response.data

    def test_new_category_get(self, authenticated_client):
        """Test GET request to new category form."""
        response = authenticated_client.get(url_for('inventory.new_category'))
        assert response.status_code == 200
        assert b'Add Category' in response.data

    def test_new_category_post_valid(self, authenticated_client):
        """Test POST request to create new category with valid data."""
        category_data = {
            'name': 'New Category',
            'description': 'New category description',
            'is_active': 'on'
        }
        
        response = authenticated_client.post(
            url_for('inventory.new_category'),
            data=category_data,
            follow_redirects=True
        )
        assert response.status_code == 200
        assert b'Category created successfully' in response.data

    def test_edit_category_get(self, authenticated_client, sample_category):
        """Test GET request to edit category form."""
        response = authenticated_client.get(
            url_for('inventory.edit_category', category_id=sample_category.id)
        )
        assert response.status_code == 200
        assert b'Edit Category' in response.data
        assert sample_category.name.encode() in response.data

    def test_edit_category_post_valid(self, authenticated_client, sample_category):
        """Test POST request to update category with valid data."""
        updated_data = {
            'name': 'Updated Category',
            'description': 'Updated description',
            'is_active': 'on'
        }
        
        response = authenticated_client.post(
            url_for('inventory.edit_category', category_id=sample_category.id),
            data=updated_data,
            follow_redirects=True
        )
        assert response.status_code == 200
        assert b'Category updated successfully' in response.data

    def test_delete_category_with_products(self, authenticated_client, sample_category, sample_product):
        """Test deleting category that has products (should fail)."""
        response = authenticated_client.post(
            url_for('inventory.delete_category', category_id=sample_category.id),
            follow_redirects=True
        )
        assert response.status_code == 200
        assert b'Cannot delete category with products' in response.data

    def test_delete_empty_category(self, authenticated_client, app_context, sample_tenant):
        """Test deleting category with no products."""
        empty_category = Category(
            tenant_id=sample_tenant.id,
            name="Empty Category"
        )
        empty_category.save()
        category_id = empty_category.id

        response = authenticated_client.post(
            url_for('inventory.delete_category', category_id=category_id),
            follow_redirects=True
        )
        assert response.status_code == 200
        assert b'Category deleted successfully' in response.data

    def test_low_stock_alerts(self, authenticated_client, app_context, sample_tenant):
        """Test low stock alerts page."""
        # Create a low stock product
        low_stock_product = Product(
            tenant_id=sample_tenant.id,
            name="Low Stock Alert Product",
            selling_price=10.00,
            current_stock=2,
            minimum_stock=10
        )
        low_stock_product.save()

        response = authenticated_client.get(url_for('inventory.low_stock'))
        assert response.status_code == 200
        assert b'Low Stock Alerts' in response.data
        assert b'Low Stock Alert Product' in response.data

    def test_analytics_page(self, authenticated_client):
        """Test analytics page."""
        response = authenticated_client.get(url_for('inventory.analytics'))
        assert response.status_code == 200
        assert b'Inventory Analytics' in response.data

    def test_analytics_with_date_range(self, authenticated_client):
        """Test analytics page with custom date range."""
        response = authenticated_client.get(
            url_for('inventory.analytics', 
                   start_date='2024-01-01', 
                   end_date='2024-12-31')
        )
        assert response.status_code == 200
        assert b'Inventory Analytics' in response.data

    def test_pagination_products(self, authenticated_client, app_context, sample_tenant):
        """Test product pagination."""
        # Create multiple products to test pagination
        for i in range(25):
            product = Product(
                tenant_id=sample_tenant.id,
                name=f"Product {i}",
                selling_price=10.00,
                current_stock=10
            )
            product.save()

        response = authenticated_client.get(
            url_for('inventory.products', per_page=10)
        )
        assert response.status_code == 200
        assert b'page' in response.data.lower()

    def test_stock_level_indicators(self, authenticated_client, app_context, sample_tenant):
        """Test that stock level indicators are displayed correctly."""
        # Create products with different stock levels
        out_of_stock = Product(
            tenant_id=sample_tenant.id,
            name="Out of Stock Product",
            selling_price=10.00,
            current_stock=0,
            minimum_stock=5
        )
        out_of_stock.save()

        low_stock = Product(
            tenant_id=sample_tenant.id,
            name="Low Stock Product",
            selling_price=10.00,
            current_stock=3,
            minimum_stock=10
        )
        low_stock.save()

        response = authenticated_client.get(url_for('inventory.products'))
        assert response.status_code == 200
        assert b'Out of Stock' in response.data
        assert b'Low Stock' in response.data

    def test_responsive_design_elements(self, authenticated_client, sample_product):
        """Test that responsive design elements are present."""
        response = authenticated_client.get(url_for('inventory.index'))
        assert response.status_code == 200
        # Check for Tailwind CSS classes that indicate responsive design
        assert b'md:grid-cols' in response.data or b'lg:grid-cols' in response.data
        assert b'sm:flex' in response.data or b'md:flex' in response.data

    def test_error_handling_invalid_product_id(self, authenticated_client):
        """Test error handling for invalid product IDs."""
        response = authenticated_client.get(
            url_for('inventory.edit_product', product_id='invalid'),
            follow_redirects=True
        )
        # Should handle gracefully, either 404 or redirect with error message
        assert response.status_code in [200, 404]

    def test_form_validation_messages(self, authenticated_client):
        """Test that form validation messages are displayed."""
        # Try to create product without required fields
        response = authenticated_client.post(
            url_for('inventory.new_product'),
            data={'name': ''},  # Empty name should trigger validation
            follow_redirects=True
        )
        assert response.status_code == 200
        # Should stay on form page or show error message
        assert b'Add Product' in response.data or b'error' in response.data.lower()