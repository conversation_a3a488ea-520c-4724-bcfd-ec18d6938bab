"""Tests for security features."""

import pytest
from flask import Flask, session, request
from app.utils.validators import CSRFProtection, sanitize_input
from app.utils.decorators import rate_limit, validate_json_request
from app import create_app


@pytest.fixture
def app():
    """Create test app."""
    app = create_app()
    app.config['TESTING'] = True
    app.config['WTF_CSRF_ENABLED'] = True
    return app


@pytest.fixture
def client(app):
    """Create test client."""
    return app.test_client()


def test_csrf_token_generation():
    """Test CSRF token generation."""
    app = Flask(__name__)
    app.secret_key = 'test-secret-key'
    
    with app.test_request_context():
        with app.test_client() as client:
            with client.session_transaction() as sess:
                token = CSRFProtection.generate_csrf_token()
                assert token is not None
                assert len(token) > 0
                assert sess.get('csrf_token') == token


def test_csrf_token_validation():
    """Test CSRF token validation."""
    app = Flask(__name__)
    app.secret_key = 'test-secret-key'
    
    with app.test_request_context():
        with app.test_client() as client:
            with client.session_transaction() as sess:
                # Generate token
                token = CSRFProtection.generate_csrf_token()
                sess['csrf_token'] = token
            
            # Test valid token in header
            with app.test_request_context(headers={'X-CSRF-Token': token}, method='POST'):
                assert CSRFProtection.validate_csrf_token() is True
            
            # Test invalid token
            with app.test_request_context(headers={'X-CSRF-Token': 'invalid'}, method='POST'):
                assert CSRFProtection.validate_csrf_token() is False
            
            # Test missing token
            with app.test_request_context(method='POST'):
                assert CSRFProtection.validate_csrf_token() is False
            
            # Test GET request (should pass)
            with app.test_request_context(method='GET'):
                assert CSRFProtection.validate_csrf_token() is True


def test_input_sanitization():
    """Test comprehensive input sanitization."""
    # Test XSS prevention
    malicious_inputs = [
        '<script>alert("xss")</script>',
        '<img src="x" onerror="alert(1)">',
        'javascript:alert("xss")',
        '<svg onload="alert(1)">',
        '"><script>alert("xss")</script>'
    ]
    
    for malicious_input in malicious_inputs:
        sanitized = sanitize_input(malicious_input)
        assert '<script>' not in sanitized
        assert 'javascript:' not in sanitized
        assert 'onerror=' not in sanitized
        assert 'onload=' not in sanitized


def test_sql_injection_prevention():
    """Test SQL injection prevention in search queries."""
    from app.utils.validators import validate_search_query
    
    malicious_queries = [
        "'; DROP TABLE users; --",
        "' OR '1'='1",
        "' UNION SELECT * FROM users --",
        "admin'--",
        "' OR 1=1 --"
    ]
    
    for query in malicious_queries:
        sanitized = validate_search_query(query)
        assert 'DROP' not in sanitized.upper()
        assert 'UNION' not in sanitized.upper()
        assert '--' not in sanitized
        assert "'" not in sanitized


def test_json_validation_security():
    """Test JSON validation security features."""
    from app.utils.validators import validate_json_input
    
    # Test oversized payload
    large_data = {'field': 'a' * 10000}
    with pytest.raises(ValueError, match='exceeds maximum length'):
        validate_json_input(large_data, max_length=1000)
    
    # Test malicious JSON structure
    malicious_data = {
        'script': '<script>alert("xss")</script>',
        'sql': "'; DROP TABLE users; --"
    }
    
    sanitized = validate_json_input(malicious_data)
    assert '<script>' not in sanitized['script']
    assert 'DROP' not in sanitized['sql']


def test_rate_limiting():
    """Test rate limiting functionality."""
    app = Flask(__name__)
    app.secret_key = 'test-secret-key'
    
    @app.route('/test')
    @rate_limit(max_requests=2, window=60)
    def test_endpoint():
        return 'success'
    
    with app.test_client() as client:
        # First request should succeed
        response = client.get('/test')
        assert response.status_code == 200
        
        # Second request should succeed
        response = client.get('/test')
        assert response.status_code == 200
        
        # Third request should be rate limited
        response = client.get('/test')
        assert response.status_code == 302  # Redirect due to rate limit


def test_numeric_parameter_validation():
    """Test numeric parameter validation."""
    from app.utils.decorators import validate_numeric_params
    from flask import Flask
    
    app = Flask(__name__)
    
    @app.route('/test/<int:user_id>')
    @validate_numeric_params('user_id')
    def test_endpoint(user_id):
        return f'User ID: {user_id}'
    
    with app.test_client() as client:
        # Valid numeric parameter
        response = client.get('/test/123')
        assert response.status_code == 200
        
        # Invalid parameter (handled by Flask routing)
        response = client.get('/test/invalid')
        assert response.status_code == 404


def test_form_data_sanitization():
    """Test form data sanitization."""
    app = Flask(__name__)
    app.secret_key = 'test-secret-key'
    
    from app.utils.decorators import validate_form_request
    
    @app.route('/test', methods=['POST'])
    @validate_form_request()
    def test_endpoint():
        sanitized_form = getattr(request, 'sanitized_form', {})
        return sanitized_form.get('test_field', '')
    
    with app.test_client() as client:
        # Test malicious form data
        response = client.post('/test', data={
            'test_field': '<script>alert("xss")</script>'
        })
        
        # Should be sanitized
        assert '<script>' not in response.get_data(as_text=True)


def test_password_security():
    """Test password validation security."""
    from app.utils.validators import validate_password_strength
    
    class MockForm:
        pass
    
    class MockField:
        def __init__(self, data):
            self.data = data
    
    form = MockForm()
    
    # Test common weak passwords
    weak_passwords = [
        'password',
        '12345678',
        'qwerty123',
        'admin123'
    ]
    
    for weak_password in weak_passwords:
        field = MockField(weak_password)
        with pytest.raises(ValidationError, match='too common'):
            validate_password_strength(form, field)


def test_business_name_security():
    """Test business name validation security."""
    from app.utils.validators import validate_business_name
    
    class MockForm:
        pass
    
    class MockField:
        def __init__(self, data):
            self.data = data
    
    form = MockForm()
    
    # Test malicious business names
    malicious_names = [
        '<script>alert("xss")</script>',
        'DROP TABLE users;',
        '../../etc/passwd'
    ]
    
    for malicious_name in malicious_names:
        field = MockField(malicious_name)
        with pytest.raises(ValidationError, match='invalid characters'):
            validate_business_name(form, field)


def test_email_security():
    """Test email validation security."""
    from app.utils.validators import validate_email
    
    class MockForm:
        pass
    
    class MockField:
        def __init__(self, data):
            self.data = data
    
    form = MockForm()
    
    # Test malicious email addresses
    malicious_emails = [
        '<EMAIL><script>alert("xss")</script>',
        'test+<script>@example.com',
        'a' * 250 + '@example.com'  # Oversized email
    ]
    
    for malicious_email in malicious_emails:
        field = MockField(malicious_email)
        with pytest.raises(ValidationError):
            validate_email(form, field)