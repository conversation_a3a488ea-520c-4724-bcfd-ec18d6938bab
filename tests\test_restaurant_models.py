"""Tests for restaurant models."""

import pytest
from datetime import datetime, timedelta
from app import create_app, db
from app.models.user import User, Tenant
from app.models.product import Product, Category
from app.models.restaurant import Table, Order, OrderItem, TableStatus, OrderStatus
from app.models.transaction import Transaction
from app.models.base import set_current_tenant, clear_current_tenant
from config import TestingConfig


@pytest.fixture
def app():
    """Create application for testing."""
    app = create_app(TestingConfig)
    return app


@pytest.fixture
def app_context(app):
    """Create application context."""
    with app.app_context():
        db.create_all()
        yield app
        db.session.remove()
        db.drop_all()


@pytest.fixture
def client(app):
    """Create test client."""
    return app.test_client()


@pytest.fixture
def tenant(app_context):
    """Create test tenant."""
    tenant = Tenant(
        name="Test Restaurant",
        business_type="restaurant"
    )
    tenant.save()
    set_current_tenant(tenant.id)
    return tenant


@pytest.fixture
def user(app_context, tenant):
    """Create test user."""
    user = User.create_user(
        email="<EMAIL>",
        password="password123",
        first_name="Test",
        last_name="User",
        tenant_id=tenant.id,
        role="manager"
    )
    return user


@pytest.fixture
def category(app_context, tenant):
    """Create test category."""
    category = Category(
        tenant_id=tenant.id,
        name="Food",
        description="Food items"
    )
    return category.save()


@pytest.fixture
def product(app_context, tenant, category):
    """Create test product."""
    product = Product(
        tenant_id=tenant.id,
        name="Burger",
        sku="BURGER001",
        category_id=category.id,
        selling_price=12.99,
        cost_price=6.50,
        stock_quantity=100
    )
    return product.save()


class TestTable:
    """Test Table model."""
    
    def test_create_table(self, app, tenant):
        """Test creating a table."""
        table = Table.create_table(
            tenant_id=tenant.id,
            table_number="T1",
            capacity=4,
            section="Main Dining"
        )
        
        assert table.id is not None
        assert table.table_number == "T1"
        assert table.capacity == 4
        assert table.section == "Main Dining"
        assert table.status == TableStatus.AVAILABLE
    
    def test_table_unique_constraint(self, app, tenant):
        """Test table number uniqueness within tenant."""
        Table.create_table(tenant_id=tenant.id, table_number="T1")
        
        with pytest.raises(Exception):  # Should raise integrity error
            Table.create_table(tenant_id=tenant.id, table_number="T1")
    
    def test_occupy_table(self, app, tenant):
        """Test occupying a table."""
        table = Table.create_table(tenant_id=tenant.id, table_number="T1")
        
        table.occupy_table(party_size=2, estimated_duration=60)
        
        assert table.status == TableStatus.OCCUPIED
        assert table.current_party_size == 2
        assert table.estimated_duration == 60
        assert table.occupied_since is not None
    
    def test_occupy_unavailable_table(self, app, tenant):
        """Test occupying an unavailable table."""
        table = Table.create_table(tenant_id=tenant.id, table_number="T1")
        table.occupy_table(party_size=2)
        
        with pytest.raises(ValueError, match="not available"):
            table.occupy_table(party_size=3)
    
    def test_reserve_table(self, app, tenant):
        """Test reserving a table."""
        table = Table.create_table(tenant_id=tenant.id, table_number="T1")
        
        reservation_time = datetime.utcnow() + timedelta(hours=2)
        table.reserve_table(
            customer_name="John Doe",
            phone="555-1234",
            notes="Birthday dinner",
            reservation_time=reservation_time
        )
        
        assert table.status == TableStatus.RESERVED
        assert table.reserved_for == "John Doe"
        assert table.reservation_phone == "555-1234"
        assert table.reservation_notes == "Birthday dinner"
        assert table.reserved_at == reservation_time
    
    def test_clear_table(self, app, tenant):
        """Test clearing a table."""
        table = Table.create_table(tenant_id=tenant.id, table_number="T1")
        table.occupy_table(party_size=2)
        
        table.clear_table()
        
        assert table.status == TableStatus.AVAILABLE
        assert table.current_party_size is None
        assert table.occupied_since is None
    
    def test_occupancy_duration(self, app, tenant):
        """Test calculating occupancy duration."""
        table = Table.create_table(tenant_id=tenant.id, table_number="T1")
        
        # Set occupied time to 30 minutes ago
        past_time = datetime.utcnow() - timedelta(minutes=30)
        table.occupy_table(party_size=2)
        table.occupied_since = past_time
        table.save()
        
        duration = table.get_occupancy_duration()
        assert duration >= 29  # Allow for small timing differences
        assert duration <= 31
    
    def test_is_overdue(self, app, tenant):
        """Test checking if table is overdue."""
        table = Table.create_table(tenant_id=tenant.id, table_number="T1")
        
        # Set occupied time to 90 minutes ago with 60 minute estimate
        past_time = datetime.utcnow() - timedelta(minutes=90)
        table.occupy_table(party_size=2, estimated_duration=60)
        table.occupied_since = past_time
        table.save()
        
        assert table.is_overdue()
    
    def test_generate_qr_code(self, app, tenant):
        """Test generating QR code for table."""
        table = Table.create_table(tenant_id=tenant.id, table_number="T1")
        
        table.generate_qr_code()
        
        assert table.qr_code is not None
        assert table.qr_code_enabled is True
    
    def test_get_available_tables(self, app, tenant):
        """Test getting available tables."""
        Table.create_table(tenant_id=tenant.id, table_number="T1", capacity=2)
        Table.create_table(tenant_id=tenant.id, table_number="T2", capacity=4)
        table3 = Table.create_table(tenant_id=tenant.id, table_number="T3", capacity=6)
        table3.occupy_table(party_size=4)
        
        available_tables = Table.get_available_tables(tenant.id)
        assert len(available_tables) == 2
        
        # Test capacity filter
        large_tables = Table.get_available_tables(tenant.id, capacity=4)
        assert len(large_tables) == 1
        assert large_tables[0].table_number == "T2"


class TestOrder:
    """Test Order model."""
    
    def test_create_order(self, app, tenant, user):
        """Test creating an order."""
        table = Table.create_table(tenant_id=tenant.id, table_number="T1")
        
        order = Order(
            tenant_id=tenant.id,
            order_number="ORD-001",
            table_id=table.id,
            user_id=user.id,
            order_type="dine_in",
            customer_name="John Doe"
        )
        order.save()
        
        assert order.id is not None
        assert order.status == OrderStatus.PENDING
        assert order.order_time is not None
    
    def test_order_workflow(self, app, tenant, user):
        """Test complete order workflow."""
        order = Order(
            tenant_id=tenant.id,
            order_number="ORD-001",
            user_id=user.id
        )
        order.save()
        
        # Start preparation
        order.start_preparation()
        assert order.status == OrderStatus.PREPARING
        assert order.preparation_started_at is not None
        
        # Mark ready
        order.mark_ready()
        assert order.status == OrderStatus.READY
        assert order.ready_at is not None
        
        # Mark served
        order.mark_served()
        assert order.status == OrderStatus.SERVED
        assert order.served_at is not None
    
    def test_cancel_order(self, app, tenant, user):
        """Test cancelling an order."""
        order = Order(
            tenant_id=tenant.id,
            order_number="ORD-001",
            user_id=user.id
        )
        order.save()
        
        order.cancel_order("Customer changed mind")
        
        assert order.status == OrderStatus.CANCELLED
        assert "Cancelled: Customer changed mind" in order.kitchen_notes
    
    def test_cannot_cancel_served_order(self, app, tenant, user):
        """Test that served orders cannot be cancelled."""
        order = Order(
            tenant_id=tenant.id,
            order_number="ORD-001",
            user_id=user.id
        )
        order.save()
        order.start_preparation()
        order.mark_ready()
        order.mark_served()
        
        with pytest.raises(ValueError, match="Cannot cancel served"):
            order.cancel_order()
    
    def test_add_item_to_order(self, app, tenant, user, product):
        """Test adding items to an order."""
        order = Order(
            tenant_id=tenant.id,
            order_number="ORD-001",
            user_id=user.id
        )
        order.save()
        
        order_item = order.add_item(product, quantity=2, special_instructions="No onions")
        
        assert order_item.quantity == 2
        assert order_item.product_name == "Burger"
        assert order_item.special_instructions == "No onions"
        assert len(order.items) == 1
    
    def test_get_preparation_time(self, app, tenant, user):
        """Test calculating preparation time."""
        order = Order(
            tenant_id=tenant.id,
            order_number="ORD-001",
            user_id=user.id
        )
        order.save()
        
        # Set preparation times
        start_time = datetime.utcnow() - timedelta(minutes=15)
        end_time = datetime.utcnow()
        
        order.preparation_started_at = start_time
        order.ready_at = end_time
        order.save()
        
        prep_time = order.get_preparation_time()
        assert prep_time >= 14  # Allow for small timing differences
        assert prep_time <= 16
    
    def test_generate_order_number(self, app, tenant):
        """Test generating unique order numbers."""
        order_number1 = Order.generate_order_number(tenant.id)
        order_number2 = Order.generate_order_number(tenant.id)
        
        assert order_number1 != order_number2
        assert order_number1.startswith("ORD-")
        assert order_number2.startswith("ORD-")
    
    def test_get_kitchen_display_orders(self, app, tenant, user):
        """Test getting orders for kitchen display."""
        # Create orders with different statuses
        order1 = Order(tenant_id=tenant.id, order_number="ORD-001", user_id=user.id, status=OrderStatus.PENDING)
        order2 = Order(tenant_id=tenant.id, order_number="ORD-002", user_id=user.id, status=OrderStatus.PREPARING)
        order3 = Order(tenant_id=tenant.id, order_number="ORD-003", user_id=user.id, status=OrderStatus.SERVED)
        
        order1.save()
        order2.save()
        order3.save()
        
        kitchen_orders = Order.get_kitchen_display_orders(tenant.id)
        
        # Should only include pending and preparing orders
        assert len(kitchen_orders) == 2
        order_numbers = [o.order_number for o in kitchen_orders]
        assert "ORD-001" in order_numbers
        assert "ORD-002" in order_numbers
        assert "ORD-003" not in order_numbers


class TestOrderItem:
    """Test OrderItem model."""
    
    def test_create_order_item(self, app, tenant, user, product):
        """Test creating an order item."""
        order = Order(
            tenant_id=tenant.id,
            order_number="ORD-001",
            user_id=user.id
        )
        order.save()
        
        order_item = OrderItem(
            tenant_id=tenant.id,
            order_id=order.id,
            product_id=product.id,
            quantity=2,
            product_name=product.name,
            special_instructions="Extra cheese"
        )
        order_item.save()
        
        assert order_item.id is not None
        assert order_item.quantity == 2
        assert order_item.status == OrderStatus.PENDING
    
    def test_order_item_preparation_workflow(self, app, tenant, user, product):
        """Test order item preparation workflow."""
        order = Order(tenant_id=tenant.id, order_number="ORD-001", user_id=user.id)
        order.save()
        
        order_item = OrderItem(
            tenant_id=tenant.id,
            order_id=order.id,
            product_id=product.id,
            quantity=1,
            product_name=product.name
        )
        order_item.save()
        
        # Start preparation
        order_item.start_preparation()
        assert order_item.status == OrderStatus.PREPARING
        assert order_item.started_at is not None
        
        # Mark ready
        order_item.mark_ready()
        assert order_item.status == OrderStatus.READY
        assert order_item.completed_at is not None
    
    def test_order_item_preparation_time(self, app, tenant, user, product):
        """Test calculating order item preparation time."""
        order = Order(tenant_id=tenant.id, order_number="ORD-001", user_id=user.id)
        order.save()
        
        order_item = OrderItem(
            tenant_id=tenant.id,
            order_id=order.id,
            product_id=product.id,
            quantity=1,
            product_name=product.name
        )
        order_item.save()
        
        # Set preparation times
        start_time = datetime.utcnow() - timedelta(minutes=10)
        end_time = datetime.utcnow()
        
        order_item.started_at = start_time
        order_item.completed_at = end_time
        order_item.save()
        
        prep_time = order_item.get_preparation_time()
        assert prep_time >= 9  # Allow for small timing differences
        assert prep_time <= 11


class TestRestaurantIntegration:
    """Test integration between restaurant models."""
    
    def test_table_order_relationship(self, app, tenant, user):
        """Test relationship between tables and orders."""
        table = Table.create_table(tenant_id=tenant.id, table_number="T1")
        
        order = Order(
            tenant_id=tenant.id,
            order_number="ORD-001",
            table_id=table.id,
            user_id=user.id
        )
        order.save()
        
        # Test relationships
        assert order.table == table
        assert order in table.orders
    
    def test_order_transaction_integration(self, app, tenant, user, product):
        """Test creating transaction from order."""
        table = Table.create_table(tenant_id=tenant.id, table_number="T1")
        
        order = Order(
            tenant_id=tenant.id,
            order_number="ORD-001",
            table_id=table.id,
            user_id=user.id,
            customer_name="John Doe"
        )
        order.save()
        
        # Add item to order
        order.add_item(product, quantity=2)
        
        # Create transaction
        transaction = Transaction(
            tenant_id=tenant.id,
            user_id=user.id,
            customer_name=order.customer_name,
            table_number=table.table_number
        )
        transaction.save()
        
        # Add order items to transaction
        for order_item in order.items:
            transaction.add_item(product, order_item.quantity)
        
        # Link order to transaction
        order.transaction_id = transaction.id
        order.save()
        
        assert order.transaction == transaction
        assert transaction.table_number == "T1"
        assert len(transaction.items) == 1
        assert transaction.items[0].quantity == 2
    
    def teardown_method(self):
        """Clean up after each test."""
        clear_current_tenant()