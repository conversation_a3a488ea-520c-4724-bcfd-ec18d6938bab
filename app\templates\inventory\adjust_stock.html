{% extends "base.html" %}

{% block title %}Adjust Stock - {{ product.name }} - SaaS POS System{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <!-- Header -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-3xl font-bold text-gray-900">Adjust Stock</h1>
            <p class="text-gray-600 mt-1">{{ product.name }}</p>
        </div>
        <div class="flex space-x-4">
            <a href="{{ url_for('inventory.view_product', product_id=product.id) }}" 
               class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg font-medium">
                Back to Product
            </a>
        </div>
    </div>

    <!-- Flash Messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="mb-4 p-4 rounded-lg {% if category == 'error' %}bg-red-100 text-red-700 border border-red-300{% else %}bg-green-100 text-green-700 border border-green-300{% endif %}">
                    {{ message }}
                </div>
            {% endfor %}
        {% endif %}
    {% endwith %}

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Stock Adjustment Form -->
        <div class="lg:col-span-2">
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">Stock Adjustment</h3>
                
                <form method="POST" class="space-y-6">
                    <!-- Current Stock Display -->
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <div class="flex items-center justify-between">
                            <div>
                                <h4 class="text-lg font-medium text-gray-900">Current Stock Level</h4>
                                <p class="text-sm text-gray-600">{{ product.name }}</p>
                            </div>
                            <div class="text-right">
                                <div class="text-3xl font-bold text-gray-900">{{ product.current_stock }}</div>
                                <div class="text-sm text-gray-500">{{ product.unit_of_measure }}</div>
                            </div>
                        </div>
                        
                        {% if product.is_low_stock %}
                        <div class="mt-3 p-3 bg-yellow-100 border border-yellow-300 rounded-lg">
                            <div class="flex items-center">
                                <svg class="w-5 h-5 text-yellow-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                </svg>
                                <span class="text-sm text-yellow-800">
                                    Low stock warning: Current stock ({{ product.current_stock }}) is at or below minimum level ({{ product.minimum_stock }})
                                </span>
                            </div>
                        </div>
                        {% endif %}
                    </div>

                    <!-- Adjustment Type -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-3">Adjustment Type</label>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <label class="relative flex cursor-pointer rounded-lg border bg-white p-4 shadow-sm focus:outline-none">
                                <input type="radio" name="adjustment_type" value="increase" class="sr-only" checked>
                                <span class="flex flex-1">
                                    <span class="flex flex-col">
                                        <span class="block text-sm font-medium text-gray-900">Increase Stock</span>
                                        <span class="mt-1 flex items-center text-sm text-gray-500">Add inventory to current stock</span>
                                    </span>
                                </span>
                                <svg class="h-5 w-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clip-rule="evenodd"></path>
                                </svg>
                            </label>
                            
                            <label class="relative flex cursor-pointer rounded-lg border bg-white p-4 shadow-sm focus:outline-none">
                                <input type="radio" name="adjustment_type" value="decrease" class="sr-only">
                                <span class="flex flex-1">
                                    <span class="flex flex-col">
                                        <span class="block text-sm font-medium text-gray-900">Decrease Stock</span>
                                        <span class="mt-1 flex items-center text-sm text-gray-500">Remove inventory from current stock</span>
                                    </span>
                                </span>
                                <svg class="h-5 w-5 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M5 10a1 1 0 011-1h8a1 1 0 110 2H6a1 1 0 01-1-1z" clip-rule="evenodd"></path>
                                </svg>
                            </label>
                        </div>
                    </div>

                    <!-- Quantity -->
                    <div>
                        <label for="quantity" class="block text-sm font-medium text-gray-700 mb-1">
                            Quantity <span class="text-red-500">*</span>
                        </label>
                        <div class="relative">
                            <input type="number" name="quantity" id="quantity" min="1" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                   placeholder="Enter quantity to adjust">
                            <span class="absolute right-3 top-2 text-gray-500">{{ product.unit_of_measure }}</span>
                        </div>
                        <p class="mt-1 text-sm text-gray-500">Enter the amount to add or remove from current stock</p>
                    </div>

                    <!-- Reason -->
                    <div>
                        <label for="reason" class="block text-sm font-medium text-gray-700 mb-1">
                            Reason <span class="text-red-500">*</span>
                        </label>
                        <select name="reason" id="reason" required
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="">Select reason</option>
                            <option value="manual_adjustment">Manual Adjustment</option>
                            <option value="stock_count">Stock Count</option>
                            <option value="damaged_goods">Damaged Goods</option>
                            <option value="expired_goods">Expired Goods</option>
                            <option value="theft_loss">Theft/Loss</option>
                            <option value="supplier_return">Supplier Return</option>
                            <option value="customer_return">Customer Return</option>
                            <option value="transfer_in">Transfer In</option>
                            <option value="transfer_out">Transfer Out</option>
                            <option value="promotion_sample">Promotion/Sample</option>
                            <option value="other">Other</option>
                        </select>
                    </div>

                    <!-- Notes -->
                    <div>
                        <label for="notes" class="block text-sm font-medium text-gray-700 mb-1">
                            Notes
                        </label>
                        <textarea name="notes" id="notes" rows="3"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                  placeholder="Optional notes about this adjustment"></textarea>
                    </div>

                    <!-- Preview -->
                    <div id="adjustment-preview" class="bg-blue-50 border border-blue-200 rounded-lg p-4" style="display: none;">
                        <h4 class="text-sm font-medium text-blue-900 mb-2">Adjustment Preview</h4>
                        <div class="text-sm text-blue-800">
                            <div class="flex justify-between">
                                <span>Current Stock:</span>
                                <span id="preview-current">{{ product.current_stock }} {{ product.unit_of_measure }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span>Adjustment:</span>
                                <span id="preview-adjustment">+0 {{ product.unit_of_measure }}</span>
                            </div>
                            <div class="flex justify-between font-medium border-t border-blue-300 pt-2 mt-2">
                                <span>New Stock Level:</span>
                                <span id="preview-new">{{ product.current_stock }} {{ product.unit_of_measure }}</span>
                            </div>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                        <a href="{{ url_for('inventory.view_product', product_id=product.id) }}" 
                           class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-6 py-2 rounded-lg font-medium">
                            Cancel
                        </a>
                        <button type="submit" 
                                class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium">
                            Apply Adjustment
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Product Summary -->
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Product Summary</h3>
                
                <div class="flex items-center space-x-4 mb-4">
                    {% if product.image_url %}
                    <img class="h-16 w-16 rounded-lg object-cover" src="{{ product.image_url }}" alt="{{ product.name }}">
                    {% else %}
                    <div class="h-16 w-16 rounded-lg bg-gray-200 flex items-center justify-center">
                        <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                        </svg>
                    </div>
                    {% endif %}
                    <div>
                        <h4 class="font-medium text-gray-900">{{ product.name }}</h4>
                        {% if product.sku %}
                        <p class="text-sm text-gray-500">SKU: {{ product.sku }}</p>
                        {% endif %}
                    </div>
                </div>

                <div class="space-y-2 text-sm">
                    <div class="flex justify-between">
                        <span class="text-gray-600">Current Stock:</span>
                        <span class="font-medium text-gray-900">{{ product.current_stock }} {{ product.unit_of_measure }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">Minimum Level:</span>
                        <span class="font-medium text-gray-900">{{ product.minimum_stock }} {{ product.unit_of_measure }}</span>
                    </div>
                    {% if product.maximum_stock %}
                    <div class="flex justify-between">
                        <span class="text-gray-600">Maximum Level:</span>
                        <span class="font-medium text-gray-900">{{ product.maximum_stock }} {{ product.unit_of_measure }}</span>
                    </div>
                    {% endif %}
                    <div class="flex justify-between">
                        <span class="text-gray-600">Selling Price:</span>
                        <span class="font-medium text-gray-900">${{ "%.2f"|format(product.selling_price) }}</span>
                    </div>
                </div>
            </div>

            <!-- Quick Adjustment Buttons -->
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Quick Adjustments</h3>
                <div class="grid grid-cols-2 gap-2">
                    <button type="button" onclick="setQuickAdjustment(1, 'increase')" 
                            class="bg-green-100 hover:bg-green-200 text-green-800 px-3 py-2 rounded text-sm font-medium">
                        +1
                    </button>
                    <button type="button" onclick="setQuickAdjustment(5, 'increase')" 
                            class="bg-green-100 hover:bg-green-200 text-green-800 px-3 py-2 rounded text-sm font-medium">
                        +5
                    </button>
                    <button type="button" onclick="setQuickAdjustment(10, 'increase')" 
                            class="bg-green-100 hover:bg-green-200 text-green-800 px-3 py-2 rounded text-sm font-medium">
                        +10
                    </button>
                    <button type="button" onclick="setQuickAdjustment(25, 'increase')" 
                            class="bg-green-100 hover:bg-green-200 text-green-800 px-3 py-2 rounded text-sm font-medium">
                        +25
                    </button>
                    <button type="button" onclick="setQuickAdjustment(1, 'decrease')" 
                            class="bg-red-100 hover:bg-red-200 text-red-800 px-3 py-2 rounded text-sm font-medium">
                        -1
                    </button>
                    <button type="button" onclick="setQuickAdjustment(5, 'decrease')" 
                            class="bg-red-100 hover:bg-red-200 text-red-800 px-3 py-2 rounded text-sm font-medium">
                        -5
                    </button>
                    <button type="button" onclick="setQuickAdjustment(10, 'decrease')" 
                            class="bg-red-100 hover:bg-red-200 text-red-800 px-3 py-2 rounded text-sm font-medium">
                        -10
                    </button>
                    <button type="button" onclick="setQuickAdjustment(25, 'decrease')" 
                            class="bg-red-100 hover:bg-red-200 text-red-800 px-3 py-2 rounded text-sm font-medium">
                        -25
                    </button>
                </div>
            </div>

            <!-- Help -->
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Help</h3>
                <div class="text-sm text-gray-600 space-y-2">
                    <p><strong>Increase Stock:</strong> Add inventory (e.g., new deliveries, returns)</p>
                    <p><strong>Decrease Stock:</strong> Remove inventory (e.g., damage, theft, samples)</p>
                    <p><strong>Reason:</strong> Select the appropriate reason for tracking purposes</p>
                    <p><strong>Notes:</strong> Add any additional details about the adjustment</p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const currentStock = {{ product.current_stock }};
    const unitOfMeasure = '{{ product.unit_of_measure }}';
    const quantityInput = document.getElementById('quantity');
    const adjustmentTypeRadios = document.querySelectorAll('input[name="adjustment_type"]');
    const preview = document.getElementById('adjustment-preview');
    const previewCurrent = document.getElementById('preview-current');
    const previewAdjustment = document.getElementById('preview-adjustment');
    const previewNew = document.getElementById('preview-new');

    function updatePreview() {
        const quantity = parseInt(quantityInput.value) || 0;
        const adjustmentType = document.querySelector('input[name="adjustment_type"]:checked').value;
        
        if (quantity > 0) {
            preview.style.display = 'block';
            
            const adjustment = adjustmentType === 'increase' ? quantity : -quantity;
            const newStock = currentStock + adjustment;
            
            previewCurrent.textContent = `${currentStock} ${unitOfMeasure}`;
            previewAdjustment.textContent = `${adjustment > 0 ? '+' : ''}${adjustment} ${unitOfMeasure}`;
            previewNew.textContent = `${newStock} ${unitOfMeasure}`;
            
            // Color code the new stock level
            if (newStock < 0) {
                previewNew.className = 'text-red-600 font-bold';
            } else if (newStock <= {{ product.minimum_stock }}) {
                previewNew.className = 'text-yellow-600 font-bold';
            } else {
                previewNew.className = 'text-green-600 font-bold';
            }
        } else {
            preview.style.display = 'none';
        }
    }

    quantityInput.addEventListener('input', updatePreview);
    adjustmentTypeRadios.forEach(radio => {
        radio.addEventListener('change', updatePreview);
    });

    // Handle radio button styling
    adjustmentTypeRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            adjustmentTypeRadios.forEach(r => {
                const label = r.closest('label');
                if (r.checked) {
                    label.classList.add('ring-2', 'ring-blue-500', 'border-blue-500');
                } else {
                    label.classList.remove('ring-2', 'ring-blue-500', 'border-blue-500');
                }
            });
        });
    });

    // Initialize first radio button as selected
    adjustmentTypeRadios[0].checked = true;
    adjustmentTypeRadios[0].dispatchEvent(new Event('change'));
});

function setQuickAdjustment(quantity, type) {
    document.getElementById('quantity').value = quantity;
    document.querySelector(`input[name="adjustment_type"][value="${type}"]`).checked = true;
    document.querySelector(`input[name="adjustment_type"][value="${type}"]`).dispatchEvent(new Event('change'));
    
    // Update preview
    const event = new Event('input');
    document.getElementById('quantity').dispatchEvent(event);
}
</script>
{% endblock %}