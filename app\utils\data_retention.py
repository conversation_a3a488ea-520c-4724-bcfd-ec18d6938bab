"""Data retention and cleanup utilities for SaaS POS System."""

import os
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from flask import current_app
from app import db
from app.models.audit import AuditLog, BackupLog
from app.services.audit_service import AuditService
from app.models.audit import AuditAction, AuditSeverity


class DataRetentionManager:
    """Manager for data retention policies and cleanup operations."""
    
    def __init__(self):
        self.retention_policies = self._get_retention_policies()
    
    def _get_retention_policies(self) -> Dict[str, int]:
        """Get retention policies from configuration or defaults."""
        
        return {
            'audit_logs': int(os.environ.get('AUDIT_LOG_RETENTION_DAYS', 90)),
            'backup_logs': int(os.environ.get('BACKUP_LOG_RETENTION_DAYS', 30)),
            'backup_files': int(os.environ.get('BACKUP_FILE_RETENTION_DAYS', 30)),
            'session_logs': int(os.environ.get('SESSION_LOG_RETENTION_DAYS', 7)),
            'error_logs': int(os.environ.get('ERROR_LOG_RETENTION_DAYS', 30)),
            'transaction_logs': int(os.environ.get('TRANSACTION_LOG_RETENTION_DAYS', 365)),  # Keep longer for compliance
            'user_activity_logs': int(os.environ.get('USER_ACTIVITY_RETENTION_DAYS', 180))
        }
    
    def run_cleanup(self, dry_run: bool = False) -> Dict[str, Any]:
        """
        Run all cleanup operations based on retention policies.
        
        Args:
            dry_run: If True, only calculate what would be deleted without actually deleting
        
        Returns:
            Dictionary with cleanup results
        """
        
        results = {
            'started_at': datetime.utcnow().isoformat(),
            'dry_run': dry_run,
            'operations': {},
            'total_deleted': 0,
            'errors': []
        }
        
        try:
            # Clean up audit logs
            audit_result = self._cleanup_audit_logs(dry_run)
            results['operations']['audit_logs'] = audit_result
            results['total_deleted'] += audit_result.get('deleted_count', 0)
            
            # Clean up backup logs and files
            backup_result = self._cleanup_backups(dry_run)
            results['operations']['backups'] = backup_result
            results['total_deleted'] += backup_result.get('deleted_count', 0)
            
            # Clean up session data (if stored in database)
            session_result = self._cleanup_session_data(dry_run)
            results['operations']['sessions'] = session_result
            results['total_deleted'] += session_result.get('deleted_count', 0)
            
            # Clean up temporary files
            temp_result = self._cleanup_temporary_files(dry_run)
            results['operations']['temporary_files'] = temp_result
            results['total_deleted'] += temp_result.get('deleted_count', 0)
            
            results['completed_at'] = datetime.utcnow().isoformat()
            results['success'] = True
            
            # Log the cleanup operation
            if not dry_run:
                AuditService.log_system_event(
                    action=AuditAction.SETTINGS_CHANGED,
                    description=f"Data retention cleanup completed. Total items deleted: {results['total_deleted']}",
                    severity=AuditSeverity.LOW,
                    additional_data=results
                )
            
        except Exception as e:
            results['success'] = False
            results['error'] = str(e)
            results['errors'].append(str(e))
            
            # Log the cleanup failure
            AuditService.log_system_event(
                action=AuditAction.SETTINGS_CHANGED,
                description=f"Data retention cleanup failed: {str(e)}",
                severity=AuditSeverity.HIGH,
                additional_data={'error': str(e)}
            )
            
            raise
        
        return results
    
    def _cleanup_audit_logs(self, dry_run: bool = False) -> Dict[str, Any]:
        """Clean up old audit logs."""
        
        retention_days = self.retention_policies['audit_logs']
        cutoff_date = datetime.utcnow() - timedelta(days=retention_days)
        
        # Count records to be deleted
        count_query = AuditLog.query.filter(AuditLog.created_at < cutoff_date)
        count_to_delete = count_query.count()
        
        result = {
            'retention_days': retention_days,
            'cutoff_date': cutoff_date.isoformat(),
            'records_to_delete': count_to_delete,
            'deleted_count': 0
        }
        
        if not dry_run and count_to_delete > 0:
            deleted_count = AuditLog.cleanup_old_logs(retention_days)
            result['deleted_count'] = deleted_count
        
        return result
    
    def _cleanup_backups(self, dry_run: bool = False) -> Dict[str, Any]:
        """Clean up old backup logs and files."""
        
        retention_days = self.retention_policies['backup_logs']
        cutoff_date = datetime.utcnow() - timedelta(days=retention_days)
        
        # Count records to be deleted
        count_query = BackupLog.query.filter(BackupLog.created_at < cutoff_date)
        count_to_delete = count_query.count()
        
        result = {
            'retention_days': retention_days,
            'cutoff_date': cutoff_date.isoformat(),
            'records_to_delete': count_to_delete,
            'deleted_count': 0,
            'files_deleted': 0
        }
        
        if not dry_run and count_to_delete > 0:
            deleted_count = BackupLog.cleanup_old_backups(retention_days)
            result['deleted_count'] = deleted_count
            result['files_deleted'] = deleted_count  # Assuming 1:1 ratio
        
        return result
    
    def _cleanup_session_data(self, dry_run: bool = False) -> Dict[str, Any]:
        """Clean up old session data if stored in database."""
        
        # This is a placeholder for session cleanup
        # Implementation depends on session storage mechanism
        
        retention_days = self.retention_policies['session_logs']
        
        result = {
            'retention_days': retention_days,
            'records_to_delete': 0,
            'deleted_count': 0,
            'note': 'Session cleanup not implemented (sessions stored in Redis/memory)'
        }
        
        return result
    
    def _cleanup_temporary_files(self, dry_run: bool = False) -> Dict[str, Any]:
        """Clean up temporary files."""
        
        temp_dirs = [
            '/tmp',
            os.path.join(os.getcwd(), 'temp'),
            os.path.join(os.getcwd(), 'tmp')
        ]
        
        deleted_count = 0
        files_to_delete = []
        
        # Look for temporary files older than 1 day
        cutoff_time = datetime.utcnow() - timedelta(days=1)
        
        for temp_dir in temp_dirs:
            if not os.path.exists(temp_dir):
                continue
            
            try:
                for filename in os.listdir(temp_dir):
                    if filename.startswith(('pos_temp_', 'backup_temp_', 'export_temp_')):
                        filepath = os.path.join(temp_dir, filename)
                        
                        if os.path.isfile(filepath):
                            file_time = datetime.fromtimestamp(os.path.getmtime(filepath))
                            
                            if file_time < cutoff_time:
                                files_to_delete.append(filepath)
                                
                                if not dry_run:
                                    try:
                                        os.remove(filepath)
                                        deleted_count += 1
                                    except Exception as e:
                                        # Log but continue
                                        print(f"Failed to delete temp file {filepath}: {e}")
            
            except Exception as e:
                # Log but continue with other directories
                print(f"Failed to process temp directory {temp_dir}: {e}")
        
        return {
            'directories_checked': temp_dirs,
            'files_to_delete': len(files_to_delete),
            'deleted_count': deleted_count,
            'cutoff_time': cutoff_time.isoformat()
        }
    
    def get_retention_status(self) -> Dict[str, Any]:
        """Get current status of data retention."""
        
        status = {
            'retention_policies': self.retention_policies,
            'current_data_sizes': {},
            'cleanup_recommendations': []
        }
        
        # Get current data sizes
        try:
            # Audit logs
            audit_count = AuditLog.query.count()
            old_audit_count = AuditLog.query.filter(
                AuditLog.created_at < datetime.utcnow() - timedelta(days=self.retention_policies['audit_logs'])
            ).count()
            
            status['current_data_sizes']['audit_logs'] = {
                'total_records': audit_count,
                'old_records': old_audit_count,
                'cleanup_needed': old_audit_count > 0
            }
            
            # Backup logs
            backup_count = BackupLog.query.count()
            old_backup_count = BackupLog.query.filter(
                BackupLog.created_at < datetime.utcnow() - timedelta(days=self.retention_policies['backup_logs'])
            ).count()
            
            status['current_data_sizes']['backup_logs'] = {
                'total_records': backup_count,
                'old_records': old_backup_count,
                'cleanup_needed': old_backup_count > 0
            }
            
            # Generate recommendations
            if old_audit_count > 1000:
                status['cleanup_recommendations'].append({
                    'type': 'audit_logs',
                    'message': f'Consider cleaning up {old_audit_count} old audit log records',
                    'priority': 'medium'
                })
            
            if old_backup_count > 10:
                status['cleanup_recommendations'].append({
                    'type': 'backup_logs',
                    'message': f'Consider cleaning up {old_backup_count} old backup records',
                    'priority': 'low'
                })
            
        except Exception as e:
            status['error'] = str(e)
        
        return status
    
    def schedule_cleanup(self, schedule_type: str = 'daily') -> Dict[str, Any]:
        """
        Schedule automatic cleanup operations.
        
        This method provides the configuration for scheduling cleanup operations
        with external schedulers like cron or Celery.
        """
        
        schedules = {
            'daily': {
                'cron': '0 2 * * *',  # 2 AM daily
                'description': 'Run cleanup daily at 2 AM',
                'operations': ['audit_logs', 'temporary_files']
            },
            'weekly': {
                'cron': '0 3 * * 0',  # 3 AM on Sundays
                'description': 'Run full cleanup weekly on Sundays at 3 AM',
                'operations': ['audit_logs', 'backups', 'sessions', 'temporary_files']
            },
            'monthly': {
                'cron': '0 4 1 * *',  # 4 AM on 1st of month
                'description': 'Run comprehensive cleanup monthly',
                'operations': ['audit_logs', 'backups', 'sessions', 'temporary_files', 'optimize_database']
            }
        }
        
        if schedule_type not in schedules:
            raise ValueError(f"Invalid schedule type: {schedule_type}")
        
        return schedules[schedule_type]
    
    def optimize_database(self, dry_run: bool = False) -> Dict[str, Any]:
        """Optimize database performance after cleanup."""
        
        result = {
            'started_at': datetime.utcnow().isoformat(),
            'dry_run': dry_run,
            'operations': []
        }
        
        try:
            if not dry_run:
                # Analyze tables to update statistics
                if current_app.config.get('SQLALCHEMY_DATABASE_URI', '').startswith('postgresql'):
                    # PostgreSQL optimization
                    db.session.execute(text('ANALYZE;'))
                    result['operations'].append('PostgreSQL ANALYZE completed')
                    
                elif current_app.config.get('SQLALCHEMY_DATABASE_URI', '').startswith('sqlite'):
                    # SQLite optimization
                    db.session.execute(text('VACUUM;'))
                    db.session.execute(text('ANALYZE;'))
                    result['operations'].append('SQLite VACUUM and ANALYZE completed')
                
                db.session.commit()
            else:
                result['operations'].append('Database optimization would be performed')
            
            result['completed_at'] = datetime.utcnow().isoformat()
            result['success'] = True
            
        except Exception as e:
            result['success'] = False
            result['error'] = str(e)
            raise
        
        return result


def create_cleanup_script() -> str:
    """Create a shell script for automated cleanup."""
    
    script_content = """#!/bin/bash
# Automated data retention cleanup script for SaaS POS System

# Set environment variables
export FLASK_APP=run.py
export FLASK_ENV=production

# Change to application directory
cd "$(dirname "$0")"

# Run cleanup
python -c "
from app import create_app
from app.utils.data_retention import DataRetentionManager

app = create_app()
with app.app_context():
    manager = DataRetentionManager()
    result = manager.run_cleanup(dry_run=False)
    print(f'Cleanup completed. Total items deleted: {result[\"total_deleted\"]}')
    
    if not result['success']:
        print(f'Cleanup failed: {result.get(\"error\", \"Unknown error\")}')
        exit(1)
"

echo "Data retention cleanup completed at $(date)"
"""
    
    return script_content