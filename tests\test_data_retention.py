"""Tests for data retention functionality."""

import os
import tempfile
import shutil
from datetime import datetime, timedelta
import pytest
from unittest.mock import patch, MagicMock
from app import create_app, db
from app.utils.data_retention import DataRetentionManager, create_cleanup_script
from app.models.audit import AuditLog, BackupLog, AuditAction, AuditSeverity
from app.models.user import User, Tenant
from config import TestingConfig


@pytest.fixture
def app():
    """Create test application."""
    app = create_app(TestingConfig)
    with app.app_context():
        db.create_all()
        yield app
        db.drop_all()


@pytest.fixture
def temp_dir():
    """Create temporary directory for testing."""
    temp_dir = tempfile.mkdtemp()
    yield temp_dir
    shutil.rmtree(temp_dir)


@pytest.fixture
def retention_manager(app):
    """Create data retention manager."""
    with app.app_context():
        return DataRetentionManager()


@pytest.fixture
def sample_tenant(app):
    """Create sample tenant for testing."""
    with app.app_context():
        tenant = Tenant(
            name='Test Business',
            business_type='retail',
            email='<EMAIL>'
        )
        tenant.save()
        return tenant


class TestDataRetentionManager:
    """Test cases for DataRetentionManager."""
    
    def test_initialization(self, retention_manager):
        """Test retention manager initialization."""
        assert retention_manager is not None
        assert isinstance(retention_manager.retention_policies, dict)
        assert 'audit_logs' in retention_manager.retention_policies
        assert 'backup_logs' in retention_manager.retention_policies
    
    def test_get_retention_policies(self, retention_manager):
        """Test getting retention policies."""
        policies = retention_manager._get_retention_policies()
        
        expected_keys = [
            'audit_logs',
            'backup_logs',
            'backup_files',
            'session_logs',
            'error_logs',
            'transaction_logs',
            'user_activity_logs'
        ]
        
        for key in expected_keys:
            assert key in policies
            assert isinstance(policies[key], int)
            assert policies[key] > 0
    
    def test_cleanup_audit_logs_dry_run(self, retention_manager, sample_tenant):
        """Test audit logs cleanup in dry run mode."""
        # Create old audit logs
        old_date = datetime.utcnow() - timedelta(days=100)
        
        for i in range(5):
            old_log = AuditLog(
                action=AuditAction.LOGIN,
                tenant_id=sample_tenant.id,
                description=f'Old log {i}',
                created_at=old_date
            )
            old_log.save()
        
        # Create recent audit logs
        for i in range(3):
            recent_log = AuditLog(
                action=AuditAction.LOGIN,
                tenant_id=sample_tenant.id,
                description=f'Recent log {i}'
            )
            recent_log.save()
        
        result = retention_manager._cleanup_audit_logs(dry_run=True)
        
        assert result['records_to_delete'] == 5
        assert result['deleted_count'] == 0
        assert result['retention_days'] == retention_manager.retention_policies['audit_logs']
        
        # Verify no logs were actually deleted
        total_logs = AuditLog.query.count()
        assert total_logs == 8
    
    def test_cleanup_audit_logs_actual(self, retention_manager, sample_tenant):
        """Test actual audit logs cleanup."""
        # Create old audit logs
        old_date = datetime.utcnow() - timedelta(days=100)
        
        for i in range(5):
            old_log = AuditLog(
                action=AuditAction.LOGIN,
                tenant_id=sample_tenant.id,
                description=f'Old log {i}',
                created_at=old_date
            )
            old_log.save()
        
        # Create recent audit logs
        for i in range(3):
            recent_log = AuditLog(
                action=AuditAction.LOGIN,
                tenant_id=sample_tenant.id,
                description=f'Recent log {i}'
            )
            recent_log.save()
        
        result = retention_manager._cleanup_audit_logs(dry_run=False)
        
        assert result['records_to_delete'] == 5
        assert result['deleted_count'] == 5
        
        # Verify old logs were deleted but recent ones remain
        remaining_logs = AuditLog.query.count()
        assert remaining_logs == 3
    
    def test_cleanup_backups_dry_run(self, retention_manager, temp_dir):
        """Test backup cleanup in dry run mode."""
        # Create old backup logs
        old_date = datetime.utcnow() - timedelta(days=40)
        
        for i in range(3):
            old_backup = BackupLog(
                backup_type='full',
                backup_filename=f'old_backup_{i}.sql.gz',
                backup_path=os.path.join(temp_dir, f'old_backup_{i}.sql.gz'),
                backup_status='completed',
                created_at=old_date
            )
            old_backup.save()
        
        # Create recent backup logs
        recent_backup = BackupLog(
            backup_type='full',
            backup_filename='recent_backup.sql.gz',
            backup_path=os.path.join(temp_dir, 'recent_backup.sql.gz'),
            backup_status='completed'
        )
        recent_backup.save()
        
        result = retention_manager._cleanup_backups(dry_run=True)
        
        assert result['records_to_delete'] == 3
        assert result['deleted_count'] == 0
        
        # Verify no backups were actually deleted
        total_backups = BackupLog.query.count()
        assert total_backups == 4
    
    def test_cleanup_temporary_files(self, retention_manager, temp_dir):
        """Test temporary files cleanup."""
        # Create temporary files
        temp_files = [
            'pos_temp_file1.txt',
            'backup_temp_file2.sql',
            'export_temp_file3.csv',
            'regular_file.txt'  # Should not be deleted
        ]
        
        # Create files with old timestamps
        old_time = datetime.utcnow() - timedelta(days=2)
        
        for filename in temp_files:
            filepath = os.path.join(temp_dir, filename)
            with open(filepath, 'w') as f:
                f.write('test content')
            
            # Set old modification time
            old_timestamp = old_time.timestamp()
            os.utime(filepath, (old_timestamp, old_timestamp))
        
        # Mock temp directories to include our test directory
        with patch.object(retention_manager, '_cleanup_temporary_files') as mock_cleanup:
            # Manually call the method with our test directory
            result = retention_manager._cleanup_temporary_files.__wrapped__(retention_manager, dry_run=False)
            
            # Since we can't easily mock the temp directories, we'll test the structure
            assert 'directories_checked' in result
            assert 'files_to_delete' in result
            assert 'deleted_count' in result
    
    def test_run_cleanup_dry_run(self, retention_manager, sample_tenant):
        """Test running full cleanup in dry run mode."""
        # Create test data
        old_date = datetime.utcnow() - timedelta(days=100)
        
        # Old audit log
        AuditLog(
            action=AuditAction.LOGIN,
            tenant_id=sample_tenant.id,
            description='Old audit log',
            created_at=old_date
        ).save()
        
        # Old backup log
        BackupLog(
            backup_type='full',
            backup_filename='old_backup.sql.gz',
            backup_path='/tmp/old_backup.sql.gz',
            backup_status='completed',
            created_at=old_date
        ).save()
        
        result = retention_manager.run_cleanup(dry_run=True)
        
        assert result['success'] is True
        assert result['dry_run'] is True
        assert 'operations' in result
        assert 'audit_logs' in result['operations']
        assert 'backups' in result['operations']
        assert 'sessions' in result['operations']
        assert 'temporary_files' in result['operations']
        assert result['total_deleted'] == 0  # Dry run should not delete anything
    
    def test_run_cleanup_actual(self, retention_manager, sample_tenant):
        """Test running actual cleanup."""
        # Create test data
        old_date = datetime.utcnow() - timedelta(days=100)
        
        # Old audit log
        AuditLog(
            action=AuditAction.LOGIN,
            tenant_id=sample_tenant.id,
            description='Old audit log',
            created_at=old_date
        ).save()
        
        result = retention_manager.run_cleanup(dry_run=False)
        
        assert result['success'] is True
        assert result['dry_run'] is False
        assert result['total_deleted'] >= 1
        
        # Verify audit log was deleted
        remaining_logs = AuditLog.query.filter_by(tenant_id=sample_tenant.id).count()
        assert remaining_logs == 0
    
    def test_get_retention_status(self, retention_manager, sample_tenant):
        """Test getting retention status."""
        # Create test data
        old_date = datetime.utcnow() - timedelta(days=100)
        
        # Create old and recent audit logs
        AuditLog(
            action=AuditAction.LOGIN,
            tenant_id=sample_tenant.id,
            description='Old audit log',
            created_at=old_date
        ).save()
        
        AuditLog(
            action=AuditAction.LOGIN,
            tenant_id=sample_tenant.id,
            description='Recent audit log'
        ).save()
        
        status = retention_manager.get_retention_status()
        
        assert 'retention_policies' in status
        assert 'current_data_sizes' in status
        assert 'cleanup_recommendations' in status
        
        # Check audit logs status
        audit_status = status['current_data_sizes']['audit_logs']
        assert audit_status['total_records'] == 2
        assert audit_status['old_records'] == 1
        assert audit_status['cleanup_needed'] is True
    
    def test_schedule_cleanup_configurations(self, retention_manager):
        """Test cleanup schedule configurations."""
        daily_schedule = retention_manager.schedule_cleanup('daily')
        assert daily_schedule['cron'] == '0 2 * * *'
        assert 'audit_logs' in daily_schedule['operations']
        
        weekly_schedule = retention_manager.schedule_cleanup('weekly')
        assert weekly_schedule['cron'] == '0 3 * * 0'
        assert 'backups' in weekly_schedule['operations']
        
        monthly_schedule = retention_manager.schedule_cleanup('monthly')
        assert monthly_schedule['cron'] == '0 4 1 * *'
        assert 'optimize_database' in monthly_schedule['operations']
        
        # Test invalid schedule type
        with pytest.raises(ValueError, match='Invalid schedule type'):
            retention_manager.schedule_cleanup('invalid')
    
    def test_optimize_database_dry_run(self, retention_manager):
        """Test database optimization in dry run mode."""
        result = retention_manager.optimize_database(dry_run=True)
        
        assert result['success'] is True
        assert result['dry_run'] is True
        assert len(result['operations']) > 0
        assert 'Database optimization would be performed' in result['operations']
    
    def test_optimize_database_sqlite(self, retention_manager):
        """Test database optimization for SQLite."""
        with patch('app.db.session.execute') as mock_execute:
            with patch('app.db.session.commit') as mock_commit:
                result = retention_manager.optimize_database(dry_run=False)
                
                assert result['success'] is True
                assert result['dry_run'] is False
                
                # Should call VACUUM and ANALYZE for SQLite
                assert mock_execute.call_count >= 2
                mock_commit.assert_called_once()
    
    def test_cleanup_error_handling(self, retention_manager):
        """Test error handling during cleanup."""
        with patch.object(retention_manager, '_cleanup_audit_logs', side_effect=Exception('Test error')):
            result = retention_manager.run_cleanup(dry_run=False)
            
            assert result['success'] is False
            assert 'Test error' in result['error']
            assert 'Test error' in result['errors']


class TestCreateCleanupScript:
    """Test cases for cleanup script generation."""
    
    def test_create_cleanup_script(self):
        """Test cleanup script creation."""
        script_content = create_cleanup_script()
        
        assert isinstance(script_content, str)
        assert '#!/bin/bash' in script_content
        assert 'DataRetentionManager' in script_content
        assert 'run_cleanup' in script_content
        assert 'FLASK_APP=run.py' in script_content
    
    def test_script_contains_error_handling(self):
        """Test that script contains error handling."""
        script_content = create_cleanup_script()
        
        assert 'exit(1)' in script_content
        assert 'if not result[\'success\']' in script_content


class TestDataRetentionIntegration:
    """Integration tests for data retention functionality."""
    
    def test_full_retention_workflow(self, retention_manager, sample_tenant):
        """Test complete retention workflow."""
        # Step 1: Create test data of various ages
        dates = [
            datetime.utcnow() - timedelta(days=200),  # Very old
            datetime.utcnow() - timedelta(days=100),  # Old
            datetime.utcnow() - timedelta(days=50),   # Medium
            datetime.utcnow() - timedelta(days=10),   # Recent
            datetime.utcnow()                         # Current
        ]
        
        for i, date in enumerate(dates):
            # Create audit logs
            AuditLog(
                action=AuditAction.LOGIN,
                tenant_id=sample_tenant.id,
                description=f'Audit log {i}',
                created_at=date
            ).save()
            
            # Create backup logs
            BackupLog(
                backup_type='full',
                backup_filename=f'backup_{i}.sql.gz',
                backup_path=f'/tmp/backup_{i}.sql.gz',
                backup_status='completed',
                created_at=date
            ).save()
        
        # Step 2: Check initial status
        initial_status = retention_manager.get_retention_status()
        assert initial_status['current_data_sizes']['audit_logs']['total_records'] == 5
        assert initial_status['current_data_sizes']['backup_logs']['total_records'] == 5
        
        # Step 3: Run cleanup
        cleanup_result = retention_manager.run_cleanup(dry_run=False)
        assert cleanup_result['success'] is True
        assert cleanup_result['total_deleted'] > 0
        
        # Step 4: Verify cleanup results
        final_status = retention_manager.get_retention_status()
        
        # Should have fewer old records
        assert (final_status['current_data_sizes']['audit_logs']['old_records'] < 
                initial_status['current_data_sizes']['audit_logs']['old_records'])
        
        # Step 5: Optimize database
        optimize_result = retention_manager.optimize_database(dry_run=False)
        assert optimize_result['success'] is True
    
    def test_retention_with_different_policies(self, app):
        """Test retention with different policy configurations."""
        # Test with custom retention policies
        with patch.dict(os.environ, {
            'AUDIT_LOG_RETENTION_DAYS': '30',
            'BACKUP_LOG_RETENTION_DAYS': '7'
        }):
            with app.app_context():
                manager = DataRetentionManager()
                
                assert manager.retention_policies['audit_logs'] == 30
                assert manager.retention_policies['backup_logs'] == 7
    
    def test_concurrent_cleanup_safety(self, retention_manager, sample_tenant):
        """Test that cleanup operations are safe for concurrent execution."""
        # Create test data
        for i in range(10):
            AuditLog(
                action=AuditAction.LOGIN,
                tenant_id=sample_tenant.id,
                description=f'Audit log {i}',
                created_at=datetime.utcnow() - timedelta(days=100)
            ).save()
        
        # Run multiple cleanup operations
        results = []
        for _ in range(3):
            try:
                result = retention_manager.run_cleanup(dry_run=False)
                results.append(result)
            except Exception as e:
                # Should handle concurrent access gracefully
                assert 'database is locked' not in str(e).lower()
        
        # At least one cleanup should succeed
        successful_cleanups = [r for r in results if r.get('success')]
        assert len(successful_cleanups) >= 1