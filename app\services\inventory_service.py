"""Inventory service for SaaS POS System."""

from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List
from decimal import Decimal
from flask import current_app
from flask_login import current_user
from sqlalchemy import and_, or_, func
from sqlalchemy.exc import IntegrityError

from app import db, cache
from app.models.product import Product, Category, InventoryMovement
from app.models.base import get_current_tenant
from app.utils.cache import cached, cache_product_data, get_cached_product_data, cache_inventory_summary, get_cached_inventory_summary, invalidate_product_cache, invalidate_inventory_cache


class InventoryError(Exception):
    """Base exception for inventory errors."""
    pass


class InsufficientStockError(InventoryError):
    """Raised when there's insufficient stock for an operation."""
    pass


class ProductNotFoundError(InventoryError):
    """Raised when a product is not found."""
    pass


class CategoryNotFoundError(InventoryError):
    """Raised when a category is not found."""
    pass


class DuplicateProductError(InventoryError):
    """Raised when trying to create a product with duplicate SKU/barcode."""
    pass


class InventoryService:
    """Service class for handling inventory operations."""
    
    @staticmethod
    def create_product(product_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a new product.
        
        Args:
            product_data: Dictionary containing product information
            
        Returns:
            Dict containing success status and product info
        """
        try:
            tenant_id = get_current_tenant()
            if not tenant_id:
                return {'success': False, 'error': 'No tenant context available'}
            
            # Check for duplicate SKU or barcode within tenant
            if product_data.get('sku'):
                existing_sku = Product.query.filter_by(
                    tenant_id=tenant_id, 
                    sku=product_data['sku']
                ).first()
                if existing_sku:
                    raise DuplicateProductError(f"Product with SKU '{product_data['sku']}' already exists")
            
            if product_data.get('barcode'):
                existing_barcode = Product.query.filter_by(
                    tenant_id=tenant_id, 
                    barcode=product_data['barcode']
                ).first()
                if existing_barcode:
                    raise DuplicateProductError(f"Product with barcode '{product_data['barcode']}' already exists")
            
            # Validate category if provided
            if product_data.get('category_id'):
                category = Category.query.filter_by(
                    id=product_data['category_id'], 
                    tenant_id=tenant_id
                ).first()
                if not category:
                    raise CategoryNotFoundError("Category not found")
            
            # Create product
            product = Product(
                tenant_id=tenant_id,
                name=product_data['name'],
                description=product_data.get('description'),
                sku=product_data.get('sku'),
                barcode=product_data.get('barcode'),
                category_id=product_data.get('category_id'),
                cost_price=Decimal(str(product_data.get('cost_price', 0))),
                selling_price=Decimal(str(product_data.get('selling_price', 0))),
                current_stock=int(product_data.get('current_stock', 0)),
                minimum_stock=int(product_data.get('minimum_stock', 0)),
                maximum_stock=product_data.get('maximum_stock'),
                track_inventory=product_data.get('track_inventory', True),
                allow_negative_stock=product_data.get('allow_negative_stock', False),
                unit_of_measure=product_data.get('unit_of_measure', 'each'),
                weight=product_data.get('weight'),
                tax_rate=Decimal(str(product_data.get('tax_rate', 0))),
                tax_inclusive=product_data.get('tax_inclusive', False),
                is_active=product_data.get('is_active', True),
                is_featured=product_data.get('is_featured', False),
                image_url=product_data.get('image_url'),
                color=product_data.get('color'),
                sort_order=product_data.get('sort_order', 0)
            )
            
            product.save()
            
            # Create initial inventory movement if stock > 0
            if product.current_stock > 0:
                movement = InventoryMovement(
                    product_id=product.id,
                    tenant_id=tenant_id,
                    movement_type='initial_stock',
                    quantity=product.current_stock,
                    old_stock=0,
                    new_stock=product.current_stock,
                    reason='Initial stock entry',
                    user_id=current_user.id if current_user.is_authenticated else None
                )
                movement.save()
            
            # Clear cache
            InventoryService._clear_product_cache(tenant_id)
            
            return {
                'success': True,
                'product': InventoryService._product_to_dict(product)
            }
            
        except (DuplicateProductError, CategoryNotFoundError) as e:
            return {'success': False, 'error': str(e)}
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Product creation error: {str(e)}")
            return {'success': False, 'error': 'An error occurred while creating the product'}
    
    @staticmethod
    def update_product(product_id: int, product_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Update an existing product.
        
        Args:
            product_id: ID of the product to update
            product_data: Dictionary containing updated product information
            
        Returns:
            Dict containing success status and updated product info
        """
        try:
            tenant_id = get_current_tenant()
            if not tenant_id:
                return {'success': False, 'error': 'No tenant context available'}
            
            product = Product.query.filter_by(id=product_id, tenant_id=tenant_id).first()
            if not product:
                raise ProductNotFoundError("Product not found")
            
            # Check for duplicate SKU or barcode (excluding current product)
            if product_data.get('sku') and product_data['sku'] != product.sku:
                existing_sku = Product.query.filter(
                    and_(
                        Product.tenant_id == tenant_id,
                        Product.sku == product_data['sku'],
                        Product.id != product_id
                    )
                ).first()
                if existing_sku:
                    raise DuplicateProductError(f"Product with SKU '{product_data['sku']}' already exists")
            
            if product_data.get('barcode') and product_data['barcode'] != product.barcode:
                existing_barcode = Product.query.filter(
                    and_(
                        Product.tenant_id == tenant_id,
                        Product.barcode == product_data['barcode'],
                        Product.id != product_id
                    )
                ).first()
                if existing_barcode:
                    raise DuplicateProductError(f"Product with barcode '{product_data['barcode']}' already exists")
            
            # Validate category if provided
            if product_data.get('category_id'):
                category = Category.query.filter_by(
                    id=product_data['category_id'], 
                    tenant_id=tenant_id
                ).first()
                if not category:
                    raise CategoryNotFoundError("Category not found")
            
            # Update product fields
            for field, value in product_data.items():
                if hasattr(product, field) and field not in ['id', 'tenant_id', 'created_at', 'updated_at']:
                    if field in ['cost_price', 'selling_price', 'tax_rate'] and value is not None:
                        setattr(product, field, Decimal(str(value)))
                    elif field in ['current_stock', 'minimum_stock'] and value is not None:
                        setattr(product, field, int(value))
                    else:
                        setattr(product, field, value)
            
            product.save()
            
            # Clear cache
            InventoryService._clear_product_cache(tenant_id)
            
            return {
                'success': True,
                'product': InventoryService._product_to_dict(product)
            }
            
        except (ProductNotFoundError, DuplicateProductError, CategoryNotFoundError) as e:
            return {'success': False, 'error': str(e)}
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Product update error: {str(e)}")
            return {'success': False, 'error': 'An error occurred while updating the product'}
    
    @staticmethod
    def delete_product(product_id: int) -> Dict[str, Any]:
        """
        Delete a product (soft delete by setting is_active to False).
        
        Args:
            product_id: ID of the product to delete
            
        Returns:
            Dict containing success status
        """
        try:
            tenant_id = get_current_tenant()
            if not tenant_id:
                return {'success': False, 'error': 'No tenant context available'}
            
            product = Product.query.filter_by(id=product_id, tenant_id=tenant_id).first()
            if not product:
                raise ProductNotFoundError("Product not found")
            
            # Soft delete - set is_active to False
            product.is_active = False
            product.save()
            
            # Clear cache
            InventoryService._clear_product_cache(tenant_id)
            
            return {'success': True, 'message': 'Product deleted successfully'}
            
        except ProductNotFoundError as e:
            return {'success': False, 'error': str(e)}
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Product deletion error: {str(e)}")
            return {'success': False, 'error': 'An error occurred while deleting the product'}
    
    @staticmethod
    @cached(timeout=600, key_prefix="product")
    def get_product(product_id: int) -> Optional[Dict[str, Any]]:
        """
        Get a single product by ID.
        
        Args:
            product_id: ID of the product
            
        Returns:
            Product dictionary or None if not found
        """
        try:
            tenant_id = get_current_tenant()
            if not tenant_id:
                return None
            
            product = Product.query.filter_by(id=product_id, tenant_id=tenant_id).first()
            if not product:
                return None
            
            return InventoryService._product_to_dict(product)
            
        except Exception as e:
            current_app.logger.error(f"Get product error: {str(e)}")
            return None
    
    @staticmethod
    def get_products(page: int = 1, per_page: int = 50, category_id: Optional[int] = None,
                    search: Optional[str] = None, active_only: bool = True,
                    low_stock_only: bool = False) -> Dict[str, Any]:
        """
        Get paginated list of products with optional filtering.
        
        Args:
            page: Page number
            per_page: Items per page
            category_id: Filter by category ID
            search: Search term for name, SKU, or barcode
            active_only: Only return active products
            low_stock_only: Only return products with low stock
            
        Returns:
            Dict containing products list and pagination info
        """
        try:
            tenant_id = get_current_tenant()
            if not tenant_id:
                return {'success': False, 'error': 'No tenant context available'}
            
            # Build query
            query = Product.query.filter_by(tenant_id=tenant_id)
            
            if active_only:
                query = query.filter_by(is_active=True)
            
            if category_id:
                query = query.filter_by(category_id=category_id)
            
            if search:
                search_pattern = f"%{search}%"
                query = query.filter(
                    or_(
                        Product.name.ilike(search_pattern),
                        Product.sku.ilike(search_pattern),
                        Product.barcode.ilike(search_pattern)
                    )
                )
            
            if low_stock_only:
                query = query.filter(
                    and_(
                        Product.track_inventory == True,
                        Product.current_stock <= Product.minimum_stock
                    )
                )
            
            # Order by name
            query = query.order_by(Product.sort_order, Product.name)
            
            # Manual pagination since TenantQuery doesn't have paginate
            total = query.count()
            offset = (page - 1) * per_page
            items = query.offset(offset).limit(per_page).all()
            
            # Calculate pagination info
            has_prev = page > 1
            has_next = offset + per_page < total
            pages = (total - 1) // per_page + 1 if total > 0 else 1
            
            # Create pagination object
            class SimplePagination:
                def __init__(self, page, per_page, total, items, has_prev, has_next, pages):
                    self.page = page
                    self.per_page = per_page
                    self.total = total
                    self.items = items
                    self.has_prev = has_prev
                    self.has_next = has_next
                    self.pages = pages
            
            pagination = SimplePagination(page, per_page, total, items, has_prev, has_next, pages)
            
            products = [InventoryService._product_to_dict(p) for p in pagination.items]
            
            return {
                'success': True,
                'products': products,
                'pagination': {
                    'page': pagination.page,
                    'pages': pagination.pages,
                    'per_page': pagination.per_page,
                    'total': pagination.total,
                    'has_next': pagination.has_next,
                    'has_prev': pagination.has_prev
                }
            }
            
        except Exception as e:
            current_app.logger.error(f"Get products error: {str(e)}")
            return {'success': False, 'error': 'An error occurred while retrieving products'}    

    @staticmethod
    def adjust_stock(product_id: int, quantity: int, reason: str = 'manual_adjustment',
                    reference_id: Optional[str] = None, notes: Optional[str] = None) -> Dict[str, Any]:
        """
        Adjust stock level for a product.
        
        Args:
            product_id: ID of the product
            quantity: Quantity to adjust (positive to increase, negative to decrease)
            reason: Reason for adjustment
            reference_id: Optional reference ID
            notes: Optional notes
            
        Returns:
            Dict containing success status and updated product info
        """
        try:
            tenant_id = get_current_tenant()
            if not tenant_id:
                return {'success': False, 'error': 'No tenant context available'}
            
            product = Product.query.filter_by(id=product_id, tenant_id=tenant_id).first()
            if not product:
                raise ProductNotFoundError("Product not found")
            
            # Adjust stock
            movement = product.adjust_stock(quantity, reason)
            if reference_id:
                movement.reference_id = reference_id
            if notes:
                movement.notes = notes
            movement.save()
            
            # Clear cache
            InventoryService._clear_product_cache(tenant_id)
            
            return {
                'success': True,
                'product': InventoryService._product_to_dict(product),
                'movement': {
                    'id': movement.id,
                    'quantity': movement.quantity,
                    'old_stock': movement.old_stock,
                    'new_stock': movement.new_stock,
                    'reason': movement.reason
                }
            }
            
        except ProductNotFoundError as e:
            return {'success': False, 'error': str(e)}
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Stock adjustment error: {str(e)}")
            return {'success': False, 'error': 'An error occurred while adjusting stock'}
    
    @staticmethod
    def _product_to_dict(product: Product) -> Dict[str, Any]:
        """Convert product object to dictionary."""
        return {
            'id': product.id,
            'name': product.name,
            'description': product.description,
            'sku': product.sku,
            'barcode': product.barcode,
            'category_id': product.category_id,
            'cost_price': float(product.cost_price),
            'selling_price': float(product.selling_price),
            'current_stock': product.current_stock,
            'minimum_stock': product.minimum_stock,
            'maximum_stock': product.maximum_stock,
            'track_inventory': product.track_inventory,
            'allow_negative_stock': product.allow_negative_stock,
            'unit_of_measure': product.unit_of_measure,
            'weight': float(product.weight) if product.weight else None,
            'tax_rate': float(product.tax_rate),
            'tax_inclusive': product.tax_inclusive,
            'is_active': product.is_active,
            'is_featured': product.is_featured,
            'image_url': product.image_url,
            'color': product.color,
            'sort_order': product.sort_order,
            'is_low_stock': product.is_low_stock(),
            'is_out_of_stock': product.is_out_of_stock(),
            'profit_margin': float(product.get_profit_margin()),
            'profit_amount': float(product.get_profit_amount()),
            'tax_amount': float(product.get_tax_amount()),
            'price_with_tax': float(product.get_price_with_tax()),
            'created_at': product.created_at.isoformat(),
            'updated_at': product.updated_at.isoformat()
        }
    
    @staticmethod
    @cached(timeout=300, key_prefix="low_stock_alerts")
    def get_low_stock_alerts(threshold_multiplier: float = 1.0) -> Dict[str, Any]:
        """
        Get products that are low on stock with alert information.
        
        Args:
            threshold_multiplier: Multiplier for minimum stock threshold
            
        Returns:
            Dict containing low stock products and alert info
        """
        try:
            tenant_id = get_current_tenant()
            if not tenant_id:
                return {'success': False, 'error': 'No tenant context available'}
            
            # Get products where current stock <= (minimum stock * threshold_multiplier)
            threshold_condition = Product.current_stock <= (Product.minimum_stock * threshold_multiplier)
            
            low_stock_products = Product.query.filter(
                and_(
                    Product.tenant_id == tenant_id,
                    Product.is_active == True,
                    Product.track_inventory == True,
                    threshold_condition,
                    Product.minimum_stock > 0  # Only products with defined minimum stock
                )
            ).order_by(Product.current_stock.asc()).all()
            
            # Categorize alerts by severity
            critical_alerts = []  # Out of stock
            high_alerts = []      # Stock <= 25% of minimum
            medium_alerts = []    # Stock <= 50% of minimum
            low_alerts = []       # Stock <= minimum
            
            for product in low_stock_products:
                product_dict = InventoryService._product_to_dict(product)
                
                # Calculate stock percentage relative to minimum
                if product.minimum_stock > 0:
                    stock_percentage = (product.current_stock / product.minimum_stock) * 100
                else:
                    stock_percentage = 100
                
                alert_info = {
                    **product_dict,
                    'stock_percentage': round(stock_percentage, 1),
                    'shortage_quantity': max(0, product.minimum_stock - product.current_stock),
                    'days_until_stockout': InventoryService._estimate_days_until_stockout(product)
                }
                
                if product.current_stock <= 0:
                    critical_alerts.append(alert_info)
                elif stock_percentage <= 25:
                    high_alerts.append(alert_info)
                elif stock_percentage <= 50:
                    medium_alerts.append(alert_info)
                else:
                    low_alerts.append(alert_info)
            
            return {
                'success': True,
                'alerts': {
                    'critical': critical_alerts,
                    'high': high_alerts,
                    'medium': medium_alerts,
                    'low': low_alerts
                },
                'summary': {
                    'total_products': len(low_stock_products),
                    'critical_count': len(critical_alerts),
                    'high_count': len(high_alerts),
                    'medium_count': len(medium_alerts),
                    'low_count': len(low_alerts)
                }
            }
            
        except Exception as e:
            current_app.logger.error(f"Low stock alerts error: {str(e)}")
            return {'success': False, 'error': 'An error occurred while retrieving low stock alerts'}
    
    @staticmethod
    def get_product_performance_analytics(start_date: Optional[datetime] = None,
                                        end_date: Optional[datetime] = None,
                                        limit: int = 50) -> Dict[str, Any]:
        """
        Get product performance analytics including sales data and trends.
        
        Args:
            start_date: Start date for analysis
            end_date: End date for analysis
            limit: Maximum number of products to analyze
            
        Returns:
            Dict containing product performance data
        """
        try:
            tenant_id = get_current_tenant()
            if not tenant_id:
                return {'success': False, 'error': 'No tenant context available'}
            
            # Set default date range if not provided (last 30 days)
            if not end_date:
                end_date = datetime.utcnow()
            if not start_date:
                start_date = end_date - timedelta(days=30)
            
            # Import here to avoid circular imports
            from app.models.transaction import Transaction, TransactionItem
            
            # Get sales data for products
            sales_query = db.session.query(
                Product.id,
                Product.name,
                Product.sku,
                Product.selling_price,
                Product.cost_price,
                Product.current_stock,
                func.sum(TransactionItem.quantity).label('total_sold'),
                func.sum(TransactionItem.subtotal).label('total_revenue'),
                func.count(TransactionItem.id).label('transaction_count'),
                func.avg(TransactionItem.quantity).label('avg_quantity_per_sale')
            ).join(
                TransactionItem, Product.id == TransactionItem.product_id
            ).join(
                Transaction, TransactionItem.transaction_id == Transaction.id
            ).filter(
                and_(
                    Product.tenant_id == tenant_id,
                    Product.is_active == True,
                    Transaction.created_at >= start_date,
                    Transaction.created_at <= end_date,
                    Transaction.status == 'completed'
                )
            ).group_by(
                Product.id, Product.name, Product.sku, Product.selling_price,
                Product.cost_price, Product.current_stock
            ).order_by(
                func.sum(TransactionItem.quantity).desc()
            ).limit(limit)
            
            sales_data = sales_query.all()
            
            # Calculate performance metrics
            performance_data = []
            total_revenue = 0
            total_profit = 0
            
            for row in sales_data:
                revenue = float(row.total_revenue or 0)
                cost = float(row.cost_price or 0) * int(row.total_sold or 0)
                profit = revenue - cost
                profit_margin = (profit / revenue * 100) if revenue > 0 else 0
                
                # Calculate inventory turnover (sales / average stock)
                # For simplicity, using current stock as average stock
                turnover_rate = (int(row.total_sold or 0) / max(1, row.current_stock)) if row.current_stock > 0 else 0
                
                performance_data.append({
                    'product_id': row.id,
                    'product_name': row.name,
                    'sku': row.sku,
                    'selling_price': float(row.selling_price or 0),
                    'cost_price': float(row.cost_price or 0),
                    'current_stock': row.current_stock,
                    'total_sold': int(row.total_sold or 0),
                    'total_revenue': revenue,
                    'total_profit': profit,
                    'profit_margin': round(profit_margin, 2),
                    'transaction_count': int(row.transaction_count or 0),
                    'avg_quantity_per_sale': round(float(row.avg_quantity_per_sale or 0), 2),
                    'inventory_turnover': round(turnover_rate, 2)
                })
                
                total_revenue += revenue
                total_profit += profit
            
            # Get products with no sales for comparison
            products_with_no_sales = Product.query.filter(
                and_(
                    Product.tenant_id == tenant_id,
                    Product.is_active == True,
                    ~Product.id.in_([p['product_id'] for p in performance_data])
                )
            ).limit(20).all()
            
            no_sales_data = []
            for product in products_with_no_sales:
                no_sales_data.append({
                    'product_id': product.id,
                    'product_name': product.name,
                    'sku': product.sku,
                    'selling_price': float(product.selling_price),
                    'cost_price': float(product.cost_price),
                    'current_stock': product.current_stock,
                    'total_sold': 0,
                    'total_revenue': 0,
                    'total_profit': 0,
                    'profit_margin': 0,
                    'transaction_count': 0,
                    'avg_quantity_per_sale': 0,
                    'inventory_turnover': 0
                })
            
            # Calculate summary statistics
            if performance_data:
                avg_profit_margin = sum(p['profit_margin'] for p in performance_data) / len(performance_data)
                avg_turnover = sum(p['inventory_turnover'] for p in performance_data) / len(performance_data)
                top_performer = max(performance_data, key=lambda x: x['total_revenue'])
                worst_performer = min(performance_data, key=lambda x: x['total_revenue'])
            else:
                avg_profit_margin = 0
                avg_turnover = 0
                top_performer = None
                worst_performer = None
            
            return {
                'success': True,
                'date_range': {
                    'start_date': start_date.isoformat(),
                    'end_date': end_date.isoformat(),
                    'days': (end_date - start_date).days
                },
                'performance_data': performance_data,
                'no_sales_data': no_sales_data,
                'summary': {
                    'total_products_analyzed': len(performance_data),
                    'products_with_no_sales': len(no_sales_data),
                    'total_revenue': round(total_revenue, 2),
                    'total_profit': round(total_profit, 2),
                    'overall_profit_margin': round((total_profit / total_revenue * 100) if total_revenue > 0 else 0, 2),
                    'avg_profit_margin': round(avg_profit_margin, 2),
                    'avg_inventory_turnover': round(avg_turnover, 2),
                    'top_performer': top_performer,
                    'worst_performer': worst_performer
                }
            }
            
        except Exception as e:
            current_app.logger.error(f"Product performance analytics error: {str(e)}")
            return {'success': False, 'error': 'An error occurred while generating performance analytics'}
    
    @staticmethod
    def get_inventory_summary() -> Dict[str, Any]:
        """
        Get overall inventory summary and statistics.
        
        Returns:
            Dict containing inventory summary data
        """
        try:
            tenant_id = get_current_tenant()
            if not tenant_id:
                return {'success': False, 'error': 'No tenant context available'}
            
            # Get basic inventory counts
            total_products = Product.query.filter_by(tenant_id=tenant_id, is_active=True).count()
            
            # Get stock status counts
            low_stock_count = Product.query.filter(
                and_(
                    Product.tenant_id == tenant_id,
                    Product.is_active == True,
                    Product.track_inventory == True,
                    Product.current_stock <= Product.minimum_stock,
                    Product.minimum_stock > 0
                )
            ).count()
            
            out_of_stock_count = Product.query.filter(
                and_(
                    Product.tenant_id == tenant_id,
                    Product.is_active == True,
                    Product.track_inventory == True,
                    Product.current_stock <= 0
                )
            ).count()
            
            # Calculate total inventory value
            products = Product.query.filter_by(tenant_id=tenant_id, is_active=True).all()
            
            total_cost_value = sum(float(p.cost_price) * p.current_stock for p in products)
            total_retail_value = sum(float(p.selling_price) * p.current_stock for p in products)
            total_potential_profit = total_retail_value - total_cost_value
            
            # Get category breakdown
            category_stats = db.session.query(
                Category.name,
                func.count(Product.id).label('product_count'),
                func.sum(Product.current_stock).label('total_stock'),
                func.sum(Product.current_stock * Product.cost_price).label('total_value')
            ).join(
                Product, Category.id == Product.category_id
            ).filter(
                and_(
                    Category.tenant_id == tenant_id,
                    Category.is_active == True,
                    Product.is_active == True
                )
            ).group_by(Category.name).all()
            
            category_breakdown = []
            for stat in category_stats:
                category_breakdown.append({
                    'category_name': stat.name,
                    'product_count': int(stat.product_count or 0),
                    'total_stock': int(stat.total_stock or 0),
                    'total_value': float(stat.total_value or 0)
                })
            
            # Get products without category
            uncategorized_count = Product.query.filter(
                and_(
                    Product.tenant_id == tenant_id,
                    Product.is_active == True,
                    Product.category_id.is_(None)
                )
            ).count()
            
            return {
                'success': True,
                'summary': {
                    'total_products': total_products,
                    'low_stock_count': low_stock_count,
                    'out_of_stock_count': out_of_stock_count,
                    'uncategorized_count': uncategorized_count,
                    'total_cost_value': round(total_cost_value, 2),
                    'total_retail_value': round(total_retail_value, 2),
                    'total_potential_profit': round(total_potential_profit, 2),
                    'profit_margin_percentage': round(
                        (total_potential_profit / total_retail_value * 100) if total_retail_value > 0 else 0, 2
                    )
                },
                'category_breakdown': category_breakdown,
                'alerts': {
                    'low_stock_percentage': round((low_stock_count / max(1, total_products)) * 100, 1),
                    'out_of_stock_percentage': round((out_of_stock_count / max(1, total_products)) * 100, 1)
                }
            }
            
        except Exception as e:
            current_app.logger.error(f"Inventory summary error: {str(e)}")
            return {'success': False, 'error': 'An error occurred while generating inventory summary'}
    
    @staticmethod
    def _estimate_days_until_stockout(product: Product) -> Optional[int]:
        """
        Estimate days until product will be out of stock based on recent sales.
        
        Args:
            product: Product instance
            
        Returns:
            Estimated days until stockout or None if cannot estimate
        """
        try:
            if not product.track_inventory or product.current_stock <= 0:
                return None
            
            # Get sales data from last 30 days
            thirty_days_ago = datetime.utcnow() - timedelta(days=30)
            
            # Import here to avoid circular imports
            from app.models.transaction import Transaction, TransactionItem
            
            total_sold = db.session.query(
                func.sum(TransactionItem.quantity)
            ).join(
                Transaction, TransactionItem.transaction_id == Transaction.id
            ).filter(
                and_(
                    TransactionItem.product_id == product.id,
                    Transaction.created_at >= thirty_days_ago,
                    Transaction.status == 'completed'
                )
            ).scalar()
            
            if not total_sold or total_sold <= 0:
                return None
            
            # Calculate daily average sales
            daily_avg_sales = total_sold / 30
            
            # Estimate days until stockout
            if daily_avg_sales > 0:
                days_until_stockout = int(product.current_stock / daily_avg_sales)
                return max(0, days_until_stockout)
            
            return None
            
        except Exception as e:
            current_app.logger.warning(f"Days until stockout estimation error: {str(e)}")
            return None
    
    @staticmethod
    def _clear_product_cache(tenant_id: int):
        """Clear product-related cache entries."""
        try:
            # Clear various cache keys related to products
            cache_keys = [
                f'products_tenant_{tenant_id}',
                f'featured_products_tenant_{tenant_id}',
                f'low_stock_products_tenant_{tenant_id}',
                f'inventory_summary_tenant_{tenant_id}',
                f'product_performance_tenant_{tenant_id}'
            ]
            
            for key in cache_keys:
                cache.delete(key)
                
        except Exception as e:
            current_app.logger.warning(f"Cache clear error: {str(e)}")
    
    @staticmethod
    def get_inventory_summary() -> Dict[str, Any]:
        """
        Get overall inventory summary and statistics.
        
        Returns:
            Dict containing inventory summary data
        """
        try:
            tenant_id = get_current_tenant()
            if not tenant_id:
                return {'success': False, 'error': 'No tenant context available'}
            
            # Get basic inventory counts
            total_products = Product.query.filter_by(tenant_id=tenant_id, is_active=True).count()
            
            # Get stock status counts
            low_stock_count = Product.query.filter(
                and_(
                    Product.tenant_id == tenant_id,
                    Product.is_active == True,
                    Product.track_inventory == True,
                    Product.current_stock <= Product.minimum_stock,
                    Product.minimum_stock > 0
                )
            ).count()
            
            out_of_stock_count = Product.query.filter(
                and_(
                    Product.tenant_id == tenant_id,
                    Product.is_active == True,
                    Product.track_inventory == True,
                    Product.current_stock <= 0
                )
            ).count()
            
            # Calculate total inventory value
            products = Product.query.filter_by(tenant_id=tenant_id, is_active=True).all()
            
            total_cost_value = sum(float(p.cost_price) * p.current_stock for p in products)
            total_retail_value = sum(float(p.selling_price) * p.current_stock for p in products)
            total_potential_profit = total_retail_value - total_cost_value
            
            # Get category breakdown
            category_stats = db.session.query(
                Category.name,
                func.count(Product.id).label('product_count'),
                func.sum(Product.current_stock).label('total_stock'),
                func.sum(Product.current_stock * Product.cost_price).label('total_value')
            ).join(
                Product, Category.id == Product.category_id
            ).filter(
                and_(
                    Category.tenant_id == tenant_id,
                    Category.is_active == True,
                    Product.is_active == True
                )
            ).group_by(Category.name).all()
            
            category_breakdown = []
            for stat in category_stats:
                category_breakdown.append({
                    'category_name': stat.name,
                    'product_count': int(stat.product_count or 0),
                    'total_stock': int(stat.total_stock or 0),
                    'total_value': float(stat.total_value or 0)
                })
            
            # Get products without category
            uncategorized_count = Product.query.filter(
                and_(
                    Product.tenant_id == tenant_id,
                    Product.is_active == True,
                    Product.category_id.is_(None)
                )
            ).count()
            
            return {
                'success': True,
                'summary': {
                    'total_products': total_products,
                    'low_stock_count': low_stock_count,
                    'out_of_stock_count': out_of_stock_count,
                    'uncategorized_count': uncategorized_count,
                    'total_cost_value': round(total_cost_value, 2),
                    'total_retail_value': round(total_retail_value, 2),
                    'total_potential_profit': round(total_potential_profit, 2),
                    'profit_margin_percentage': round(
                        (total_potential_profit / total_retail_value * 100) if total_retail_value > 0 else 0, 2
                    )
                },
                'category_breakdown': category_breakdown,
                'alerts': {
                    'low_stock_percentage': round((low_stock_count / max(1, total_products)) * 100, 1),
                    'out_of_stock_percentage': round((out_of_stock_count / max(1, total_products)) * 100, 1)
                }
            }
            
        except Exception as e:
            current_app.logger.error(f"Inventory summary error: {str(e)}")
            return {'success': False, 'error': 'An error occurred while generating inventory summary'}
    
    # Category Management Methods
    @staticmethod
    def create_category(category_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a new category.
        
        Args:
            category_data: Dictionary containing category information
            
        Returns:
            Dict containing success status and category info
        """
        try:
            tenant_id = get_current_tenant()
            if not tenant_id:
                return {'success': False, 'error': 'No tenant context available'}
            
            # Validate parent category if provided
            if category_data.get('parent_id'):
                parent = Category.query.filter_by(
                    id=category_data['parent_id'], 
                    tenant_id=tenant_id
                ).first()
                if not parent:
                    raise CategoryNotFoundError("Parent category not found")
            
            # Create category
            category = Category(
                tenant_id=tenant_id,
                name=category_data['name'],
                description=category_data.get('description'),
                parent_id=category_data.get('parent_id'),
                color=category_data.get('color'),
                icon=category_data.get('icon'),
                sort_order=category_data.get('sort_order', 0),
                is_active=category_data.get('is_active', True)
            )
            
            category.save()
            
            # Clear cache
            InventoryService._clear_product_cache(tenant_id)
            
            return {
                'success': True,
                'category': InventoryService._category_to_dict(category)
            }
            
        except CategoryNotFoundError as e:
            return {'success': False, 'error': str(e)}
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Category creation error: {str(e)}")
            return {'success': False, 'error': 'An error occurred while creating the category'}
    
    @staticmethod
    def update_category(category_id: int, category_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Update an existing category.
        
        Args:
            category_id: ID of the category to update
            category_data: Dictionary containing updated category information
            
        Returns:
            Dict containing success status and updated category info
        """
        try:
            tenant_id = get_current_tenant()
            if not tenant_id:
                return {'success': False, 'error': 'No tenant context available'}
            
            category = Category.query.filter_by(id=category_id, tenant_id=tenant_id).first()
            if not category:
                raise CategoryNotFoundError("Category not found")
            
            # Validate parent category if provided (and prevent circular reference)
            if category_data.get('parent_id'):
                if category_data['parent_id'] == category_id:
                    return {'success': False, 'error': 'Category cannot be its own parent'}
                
                parent = Category.query.filter_by(
                    id=category_data['parent_id'], 
                    tenant_id=tenant_id
                ).first()
                if not parent:
                    raise CategoryNotFoundError("Parent category not found")
            
            # Update category fields
            for field, value in category_data.items():
                if hasattr(category, field) and field not in ['id', 'tenant_id', 'created_at', 'updated_at']:
                    setattr(category, field, value)
            
            category.save()
            
            # Clear cache
            InventoryService._clear_product_cache(tenant_id)
            
            return {
                'success': True,
                'category': InventoryService._category_to_dict(category)
            }
            
        except CategoryNotFoundError as e:
            return {'success': False, 'error': str(e)}
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Category update error: {str(e)}")
            return {'success': False, 'error': 'An error occurred while updating the category'}
    
    @staticmethod
    def delete_category(category_id: int) -> Dict[str, Any]:
        """
        Delete a category (soft delete by setting is_active to False).
        
        Args:
            category_id: ID of the category to delete
            
        Returns:
            Dict containing success status
        """
        try:
            tenant_id = get_current_tenant()
            if not tenant_id:
                return {'success': False, 'error': 'No tenant context available'}
            
            category = Category.query.filter_by(id=category_id, tenant_id=tenant_id).first()
            if not category:
                raise CategoryNotFoundError("Category not found")
            
            # Check if category can be deleted
            if not category.can_be_deleted():
                return {'success': False, 'error': 'Category cannot be deleted as it contains products or subcategories'}
            
            # Soft delete - set is_active to False
            category.is_active = False
            category.save()
            
            # Clear cache
            InventoryService._clear_product_cache(tenant_id)
            
            return {'success': True, 'message': 'Category deleted successfully'}
            
        except CategoryNotFoundError as e:
            return {'success': False, 'error': str(e)}
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Category deletion error: {str(e)}")
            return {'success': False, 'error': 'An error occurred while deleting the category'}
    
    @staticmethod
    def get_categories(include_inactive: bool = False) -> Dict[str, Any]:
        """
        Get all categories for the current tenant.
        
        Args:
            include_inactive: Whether to include inactive categories
            
        Returns:
            Dict containing categories list
        """
        try:
            tenant_id = get_current_tenant()
            if not tenant_id:
                return {'success': False, 'error': 'No tenant context available'}
            
            query = Category.query.filter_by(tenant_id=tenant_id)
            
            if not include_inactive:
                query = query.filter_by(is_active=True)
            
            categories = query.order_by(Category.sort_order, Category.name).all()
            
            return {
                'success': True,
                'categories': [InventoryService._category_to_dict(c) for c in categories],
                'category_tree': Category.get_category_tree(tenant_id)
            }
            
        except Exception as e:
            current_app.logger.error(f"Get categories error: {str(e)}")
            return {'success': False, 'error': 'An error occurred while retrieving categories'}
    
    @staticmethod
    def _estimate_days_until_stockout(product: Product) -> Optional[int]:
        """
        Estimate days until product will be out of stock based on recent sales.
        
        Args:
            product: Product instance
            
        Returns:
            Estimated days until stockout or None if cannot estimate
        """
        try:
            if not product.track_inventory or product.current_stock <= 0:
                return None
            
            # Get sales data from last 30 days
            thirty_days_ago = datetime.utcnow() - timedelta(days=30)
            
            # Import here to avoid circular imports
            from app.models.transaction import Transaction, TransactionItem
            
            total_sold = db.session.query(
                func.sum(TransactionItem.quantity)
            ).join(
                Transaction, TransactionItem.transaction_id == Transaction.id
            ).filter(
                and_(
                    TransactionItem.product_id == product.id,
                    Transaction.created_at >= thirty_days_ago,
                    Transaction.status == 'completed'
                )
            ).scalar()
            
            if not total_sold or total_sold <= 0:
                return None
            
            # Calculate daily average sales
            daily_avg_sales = total_sold / 30
            
            # Estimate days until stockout
            if daily_avg_sales > 0:
                days_until_stockout = int(product.current_stock / daily_avg_sales)
                return max(0, days_until_stockout)
            
            return None
            
        except Exception as e:
            current_app.logger.warning(f"Days until stockout estimation error: {str(e)}")
            return None
    
    @staticmethod
    def _category_to_dict(category: Category) -> Dict[str, Any]:
        """Convert category object to dictionary."""
        return {
            'id': category.id,
            'name': category.name,
            'description': category.description,
            'parent_id': category.parent_id,
            'color': category.color,
            'icon': category.icon,
            'sort_order': category.sort_order,
            'is_active': category.is_active,
            'product_count': category.get_product_count(),
            'subcategory_count': category.get_subcategory_count(),
            'full_path': category.get_full_path(),
            'created_at': category.created_at.isoformat(),
            'updated_at': category.updated_at.isoformat()
        }
    
    @staticmethod
    def _clear_product_cache(tenant_id: int):
        """Clear product-related cache entries."""
        try:
            # Clear various cache keys related to products
            cache_keys = [
                f'products_tenant_{tenant_id}',
                f'featured_products_tenant_{tenant_id}',
                f'low_stock_products_tenant_{tenant_id}',
                f'inventory_summary_tenant_{tenant_id}',
                f'product_performance_tenant_{tenant_id}',
                f'categories_tenant_{tenant_id}',
                f'inventory_trends_tenant_{tenant_id}'
            ]
            
            for key in cache_keys:
                cache.delete(key)
                
        except Exception as e:
            current_app.logger.warning(f"Cache clear error: {str(e)}")
            
    @staticmethod
    @cache.memoize(timeout=3600)
    def get_inventory_trends(days: int = 30) -> Dict[str, Any]:
        """
        Get inventory trends over time, including stock level changes and sales patterns.
        
        Args:
            days: Number of days to analyze
            
        Returns:
            Dict containing inventory trend data
        """
        try:
            tenant_id = get_current_tenant()
            if not tenant_id:
                return {'success': False, 'error': 'No tenant context available'}
            
            # Calculate date range
            end_date = datetime.utcnow()
            start_date = end_date - timedelta(days=days)
            
            # Import here to avoid circular imports
            from app.models.transaction import Transaction, TransactionItem
            
            # Get daily stock movements
            movements = db.session.query(
                func.date(InventoryMovement.created_at).label('date'),
                func.sum(InventoryMovement.quantity).label('quantity_change'),
                InventoryMovement.movement_type,
                func.count(InventoryMovement.id).label('movement_count')
            ).filter(
                and_(
                    InventoryMovement.tenant_id == tenant_id,
                    InventoryMovement.created_at >= start_date,
                    InventoryMovement.created_at <= end_date
                )
            ).group_by(
                func.date(InventoryMovement.created_at),
                InventoryMovement.movement_type
            ).order_by(
                func.date(InventoryMovement.created_at)
            ).all()
            
            # Get daily sales data
            sales = db.session.query(
                func.date(Transaction.created_at).label('date'),
                func.sum(TransactionItem.quantity).label('quantity_sold'),
                func.count(Transaction.id.distinct()).label('transaction_count'),
                func.sum(Transaction.total_amount).label('total_sales')
            ).join(
                TransactionItem, Transaction.id == TransactionItem.transaction_id
            ).filter(
                and_(
                    Transaction.tenant_id == tenant_id,
                    Transaction.status == 'completed',
                    Transaction.created_at >= start_date,
                    Transaction.created_at <= end_date
                )
            ).group_by(
                func.date(Transaction.created_at)
            ).order_by(
                func.date(Transaction.created_at)
            ).all()
            
            # Process movement data by date
            movement_data = {}
            for m in movements:
                date_str = m.date.isoformat()
                if date_str not in movement_data:
                    movement_data[date_str] = {
                        'date': date_str,
                        'inflow': 0,
                        'outflow': 0,
                        'adjustment': 0,
                        'movement_count': 0
                    }
                
                movement_data[date_str]['movement_count'] += m.movement_count
                
                if m.movement_type in ['restock', 'return', 'initial_stock']:
                    movement_data[date_str]['inflow'] += m.quantity_change
                elif m.movement_type in ['sale', 'loss', 'damage']:
                    movement_data[date_str]['outflow'] += abs(m.quantity_change)
                else:
                    movement_data[date_str]['adjustment'] += m.quantity_change
            
            # Process sales data by date
            sales_data = {}
            for s in sales:
                date_str = s.date.isoformat()
                sales_data[date_str] = {
                    'date': date_str,
                    'quantity_sold': int(s.quantity_sold or 0),
                    'transaction_count': int(s.transaction_count or 0),
                    'total_sales': float(s.total_sales or 0)
                }
            
            # Get top moving products
            top_selling = db.session.query(
                Product.id,
                Product.name,
                Product.sku,
                func.sum(TransactionItem.quantity).label('quantity_sold')
            ).join(
                TransactionItem, Product.id == TransactionItem.product_id
            ).join(
                Transaction, TransactionItem.transaction_id == Transaction.id
            ).filter(
                and_(
                    Product.tenant_id == tenant_id,
                    Transaction.created_at >= start_date,
                    Transaction.created_at <= end_date,
                    Transaction.status == 'completed'
                )
            ).group_by(
                Product.id, Product.name, Product.sku
            ).order_by(
                func.sum(TransactionItem.quantity).desc()
            ).limit(10).all()
            
            # Get products with most stock changes
            most_movement = db.session.query(
                Product.id,
                Product.name,
                Product.sku,
                func.count(InventoryMovement.id).label('movement_count')
            ).join(
                InventoryMovement, Product.id == InventoryMovement.product_id
            ).filter(
                and_(
                    Product.tenant_id == tenant_id,
                    InventoryMovement.created_at >= start_date,
                    InventoryMovement.created_at <= end_date
                )
            ).group_by(
                Product.id, Product.name, Product.sku
            ).order_by(
                func.count(InventoryMovement.id).desc()
            ).limit(10).all()
            
            # Calculate overall statistics
            total_inflow = sum(data['inflow'] for data in movement_data.values())
            total_outflow = sum(data['outflow'] for data in movement_data.values())
            total_adjustment = sum(data['adjustment'] for data in movement_data.values())
            total_sold = sum(data['quantity_sold'] for data in sales_data.values() if 'quantity_sold' in data)
            total_sales = sum(data['total_sales'] for data in sales_data.values() if 'total_sales' in data)
            
            # Calculate daily averages
            avg_daily_sales = total_sold / days if days > 0 else 0
            avg_daily_revenue = total_sales / days if days > 0 else 0
            
            return {
                'success': True,
                'date_range': {
                    'start_date': start_date.isoformat(),
                    'end_date': end_date.isoformat(),
                    'days': days
                },
                'daily_data': {
                    'movements': list(movement_data.values()),
                    'sales': list(sales_data.values())
                },
                'top_products': {
                    'by_sales': [
                        {
                            'id': p.id,
                            'name': p.name,
                            'sku': p.sku,
                            'quantity_sold': int(p.quantity_sold or 0)
                        } for p in top_selling
                    ],
                    'by_movement': [
                        {
                            'id': p.id,
                            'name': p.name,
                            'sku': p.sku,
                            'movement_count': int(p.movement_count or 0)
                        } for p in most_movement
                    ]
                },
                'summary': {
                    'total_inflow': total_inflow,
                    'total_outflow': total_outflow,
                    'total_adjustment': total_adjustment,
                    'total_sold': total_sold,
                    'total_sales': round(total_sales, 2),
                    'avg_daily_sales': round(avg_daily_sales, 2),
                    'avg_daily_revenue': round(avg_daily_revenue, 2),
                    'net_inventory_change': total_inflow - total_outflow + total_adjustment
                }
            }
            
        except Exception as e:
            current_app.logger.error(f"Inventory trends error: {str(e)}")
            return {'success': False, 'error': 'An error occurred while generating inventory trends'}
    
    # Category Management Methods
    @staticmethod
    def create_category(category_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a new category.
        
        Args:
            category_data: Dictionary containing category information
            
        Returns:
            Dict containing success status and category info
        """
        try:
            tenant_id = get_current_tenant()
            if not tenant_id:
                return {'success': False, 'error': 'No tenant context available'}
            
            # Validate parent category if provided
            if category_data.get('parent_id'):
                parent = Category.query.filter_by(
                    id=category_data['parent_id'], 
                    tenant_id=tenant_id
                ).first()
                if not parent:
                    return {'success': False, 'error': 'Parent category not found'}
            
            # Create category
            category = Category(
                tenant_id=tenant_id,
                name=category_data['name'],
                description=category_data.get('description'),
                parent_id=category_data.get('parent_id'),
                color=category_data.get('color'),
                icon=category_data.get('icon'),
                sort_order=category_data.get('sort_order', 0),
                is_active=category_data.get('is_active', True)
            )
            
            category.save()
            
            return {
                'success': True,
                'category': InventoryService._category_to_dict(category)
            }
            
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Category creation error: {str(e)}")
            return {'success': False, 'error': 'An error occurred while creating the category'}
    
    @staticmethod
    def update_category(category_id: int, category_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Update an existing category.
        
        Args:
            category_id: ID of the category to update
            category_data: Dictionary containing updated category information
            
        Returns:
            Dict containing success status and updated category info
        """
        try:
            tenant_id = get_current_tenant()
            if not tenant_id:
                return {'success': False, 'error': 'No tenant context available'}
            
            category = Category.query.filter_by(id=category_id, tenant_id=tenant_id).first()
            if not category:
                return {'success': False, 'error': 'Category not found'}
            
            # Prevent circular reference
            if category_data.get('parent_id') == category_id:
                return {'success': False, 'error': 'A category cannot be its own parent'}
            
            # Validate parent category if provided
            if category_data.get('parent_id'):
                parent = Category.query.filter_by(
                    id=category_data['parent_id'], 
                    tenant_id=tenant_id
                ).first()
                if not parent:
                    return {'success': False, 'error': 'Parent category not found'}
            
            # Update category fields
            for field, value in category_data.items():
                if hasattr(category, field) and field not in ['id', 'tenant_id', 'created_at', 'updated_at']:
                    setattr(category, field, value)
            
            category.save()
            
            return {
                'success': True,
                'category': InventoryService._category_to_dict(category)
            }
            
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Category update error: {str(e)}")
            return {'success': False, 'error': 'An error occurred while updating the category'}
    
    @staticmethod
    def delete_category(category_id: int) -> Dict[str, Any]:
        """
        Delete a category (soft delete by setting is_active to False).
        
        Args:
            category_id: ID of the category to delete
            
        Returns:
            Dict containing success status
        """
        try:
            tenant_id = get_current_tenant()
            if not tenant_id:
                return {'success': False, 'error': 'No tenant context available'}
            
            category = Category.query.filter_by(id=category_id, tenant_id=tenant_id).first()
            if not category:
                return {'success': False, 'error': 'Category not found'}
            
            # Check if category can be deleted
            if not category.can_be_deleted():
                return {'success': False, 'error': 'Category cannot be deleted as it has products or subcategories'}
            
            # Soft delete - set is_active to False
            category.is_active = False
            category.save()
            
            return {'success': True, 'message': 'Category deleted successfully'}
            
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Category deletion error: {str(e)}")
            return {'success': False, 'error': 'An error occurred while deleting the category'}
    
    @staticmethod
    def get_categories() -> Dict[str, Any]:
        """
        Get all categories for the current tenant.
        
        Returns:
            Dict containing categories list and category tree
        """
        try:
            tenant_id = get_current_tenant()
            if not tenant_id:
                return {'success': False, 'error': 'No tenant context available'}
            
            categories = Category.query.filter_by(tenant_id=tenant_id).order_by(Category.sort_order, Category.name).all()
            category_tree = Category.get_category_tree(tenant_id)
            
            return {
                'success': True,
                'categories': [InventoryService._category_to_dict(c) for c in categories],
                'category_tree': category_tree
            }
            
        except Exception as e:
            current_app.logger.error(f"Get categories error: {str(e)}")
            return {'success': False, 'error': 'An error occurred while retrieving categories'}
    
    @staticmethod
    def get_inventory_summary() -> Dict[str, Any]:
        """
        Get inventory summary statistics.
        
        Returns:
            Dict containing inventory summary data
        """
        try:
            tenant_id = get_current_tenant()
            if not tenant_id:
                return {'success': False, 'error': 'No tenant context available'}
            
            # Get basic product counts
            total_products = Product.query.filter_by(tenant_id=tenant_id, is_active=True).count()
            low_stock_count = Product.query.filter(
                and_(
                    Product.tenant_id == tenant_id,
                    Product.is_active == True,
                    Product.track_inventory == True,
                    Product.current_stock <= Product.minimum_stock,
                    Product.minimum_stock > 0
                )
            ).count()
            
            out_of_stock_count = Product.query.filter(
                and_(
                    Product.tenant_id == tenant_id,
                    Product.is_active == True,
                    Product.track_inventory == True,
                    Product.current_stock <= 0
                )
            ).count()
            
            # Calculate total inventory value
            products = Product.query.filter_by(tenant_id=tenant_id, is_active=True).all()
            total_cost_value = sum(float(p.cost_price) * p.current_stock for p in products)
            total_retail_value = sum(float(p.selling_price) * p.current_stock for p in products)
            
            # Get category breakdown
            categories = Category.query.filter_by(tenant_id=tenant_id, is_active=True).all()
            category_breakdown = []
            
            for category in categories:
                category_products = Product.query.filter_by(
                    tenant_id=tenant_id, 
                    category_id=category.id, 
                    is_active=True
                ).all()
                
                if category_products:
                    category_value = sum(float(p.selling_price) * p.current_stock for p in category_products)
                    category_breakdown.append({
                        'category_id': category.id,
                        'category_name': category.name,
                        'product_count': len(category_products),
                        'total_value': round(category_value, 2)
                    })
            
            return {
                'success': True,
                'summary': {
                    'total_products': total_products,
                    'low_stock_count': low_stock_count,
                    'out_of_stock_count': out_of_stock_count,
                    'total_cost_value': round(total_cost_value, 2),
                    'total_retail_value': round(total_retail_value, 2),
                    'potential_profit': round(total_retail_value - total_cost_value, 2)
                },
                'category_breakdown': category_breakdown
            }
            
        except Exception as e:
            current_app.logger.error(f"Inventory summary error: {str(e)}")
            return {'success': False, 'error': 'An error occurred while generating inventory summary'}
    
    @staticmethod
    def _category_to_dict(category: Category) -> Dict[str, Any]:
        """Convert category object to dictionary."""
        return {
            'id': category.id,
            'name': category.name,
            'description': category.description,
            'parent_id': category.parent_id,
            'color': category.color,
            'icon': category.icon,
            'sort_order': category.sort_order,
            'is_active': category.is_active,
            'product_count': category.get_product_count(),
            'full_path': category.get_full_path(),
            'created_at': category.created_at.isoformat(),
            'updated_at': category.updated_at.isoformat()
        }
    
    @staticmethod
    def _estimate_days_until_stockout(product: Product) -> Optional[int]:
        """
        Estimate days until product will be out of stock based on recent sales.
        
        Args:
            product: Product to analyze
            
        Returns:
            Estimated days until stockout or None if cannot be calculated
        """
        try:
            if product.current_stock <= 0:
                return 0
            
            # Get sales data from last 30 days
            from app.models.transaction import Transaction, TransactionItem
            thirty_days_ago = datetime.utcnow() - timedelta(days=30)
            
            total_sold = db.session.query(func.sum(TransactionItem.quantity)).join(
                Transaction, TransactionItem.transaction_id == Transaction.id
            ).filter(
                and_(
                    TransactionItem.product_id == product.id,
                    Transaction.created_at >= thirty_days_ago,
                    Transaction.status == 'completed'
                )
            ).scalar() or 0
            
            if total_sold <= 0:
                return None  # No sales data available
            
            # Calculate average daily sales
            avg_daily_sales = total_sold / 30
            
            if avg_daily_sales <= 0:
                return None
            
            # Estimate days until stockout
            days_until_stockout = int(product.current_stock / avg_daily_sales)
            return max(0, days_until_stockout)
            
        except Exception:
            return None
    
    @staticmethod
    def _clear_product_cache(tenant_id: int):
        """Clear product-related cache entries for a tenant."""
        try:
            cache_keys = [
                f'products_tenant_{tenant_id}',
                f'categories_tenant_{tenant_id}',
                f'low_stock_tenant_{tenant_id}',
                f'inventory_summary_tenant_{tenant_id}'
            ]
            
            for key in cache_keys:
                cache.delete(key)
                
        except Exception as e:
            current_app.logger.warning(f"Cache clearing error: {str(e)}")