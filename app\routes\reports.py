"""Reports routes for SaaS POS System."""

import csv
import io
from datetime import datetime, timedelta
from flask import Blueprint, render_template, request, jsonify, make_response, current_app
from flask_login import login_required
from reportlab.lib import colors
from reportlab.lib.pagesizes import letter, A4
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch

from app.services.report_service import ReportService
from app.utils.decorators import tenant_required

bp = Blueprint('reports', __name__)


@bp.route('/')
@login_required
@tenant_required
def dashboard():
    """Reports dashboard with key metrics and charts."""
    try:
        # Get date range from request args
        start_date_str = request.args.get('start_date')
        end_date_str = request.args.get('end_date')
        
        start_date = None
        end_date = None
        
        if start_date_str:
            start_date = datetime.strptime(start_date_str, '%Y-%m-%d')
        if end_date_str:
            end_date = datetime.strptime(end_date_str, '%Y-%m-%d')
            # Set to end of day
            end_date = end_date.replace(hour=23, minute=59, second=59)
        
        # Get dashboard data
        sales_summary = ReportService.get_sales_summary(start_date, end_date)
        daily_trend = ReportService.get_daily_sales_trend(start_date, end_date)
        
        return render_template('reports/dashboard.html',
                             sales_summary=sales_summary,
                             daily_trend=daily_trend)
    
    except Exception as e:
        current_app.logger.error(f"Reports dashboard error: {str(e)}")
        return render_template('reports/dashboard.html',
                             sales_summary={'success': False, 'error': 'Failed to load dashboard data'},
                             daily_trend={'success': False})


@bp.route('/sales')
@login_required
@tenant_required
def sales_report():
    """Detailed sales report with filtering options."""
    try:
        # Get date range from request args
        start_date_str = request.args.get('start_date')
        end_date_str = request.args.get('end_date')
        
        start_date = None
        end_date = None
        
        if start_date_str:
            start_date = datetime.strptime(start_date_str, '%Y-%m-%d')
        if end_date_str:
            end_date = datetime.strptime(end_date_str, '%Y-%m-%d')
            # Set to end of day
            end_date = end_date.replace(hour=23, minute=59, second=59)
        
        # Get sales data
        sales_summary = ReportService.get_sales_summary(start_date, end_date)
        daily_trend = ReportService.get_daily_sales_trend(start_date, end_date)
        hourly_pattern = ReportService.get_hourly_sales_pattern(start_date, end_date)
        
        return render_template('reports/sales_report.html',
                             sales_summary=sales_summary,
                             daily_trend=daily_trend,
                             hourly_pattern=hourly_pattern)
    
    except Exception as e:
        current_app.logger.error(f"Sales report error: {str(e)}")
        return render_template('reports/sales_report.html',
                             sales_summary={'success': False, 'error': 'Failed to load sales data'},
                             daily_trend={'success': False},
                             hourly_pattern={'success': False})


@bp.route('/products')
@login_required
@tenant_required
def product_performance():
    """Product performance report with sorting and filtering."""
    try:
        # Get parameters from request args
        start_date_str = request.args.get('start_date')
        end_date_str = request.args.get('end_date')
        sort_by = request.args.get('sort_by', 'revenue')
        limit = int(request.args.get('limit', 50))
        
        start_date = None
        end_date = None
        
        if start_date_str:
            start_date = datetime.strptime(start_date_str, '%Y-%m-%d')
        if end_date_str:
            end_date = datetime.strptime(end_date_str, '%Y-%m-%d')
            # Set to end of day
            end_date = end_date.replace(hour=23, minute=59, second=59)
        
        # Get product performance data
        product_report = ReportService.get_product_performance_report(
            start_date, end_date, limit, sort_by
        )
        
        return render_template('reports/product_performance.html',
                             product_report=product_report)
    
    except Exception as e:
        current_app.logger.error(f"Product performance report error: {str(e)}")
        return render_template('reports/product_performance.html',
                             product_report={'success': False, 'error': 'Failed to load product data'})


@bp.route('/inventory')
@login_required
@tenant_required
def inventory_report():
    """Inventory status report with stock level analysis."""
    try:
        # Get parameters from request args
        low_stock_threshold = int(request.args.get('low_stock_threshold', 10))
        include_out_of_stock = bool(request.args.get('include_out_of_stock'))
        
        # Get inventory data
        inventory_report = ReportService.get_inventory_report(
            low_stock_threshold, include_out_of_stock
        )
        
        return render_template('reports/inventory_report.html',
                             inventory_report=inventory_report)
    
    except Exception as e:
        current_app.logger.error(f"Inventory report error: {str(e)}")
        return render_template('reports/inventory_report.html',
                             inventory_report={'success': False, 'error': 'Failed to load inventory data'})


@bp.route('/customers')
@login_required
@tenant_required
def customer_report():
    """Top customers report with purchase analysis."""
    try:
        # Get parameters from request args
        start_date_str = request.args.get('start_date')
        end_date_str = request.args.get('end_date')
        limit = int(request.args.get('limit', 20))
        
        start_date = None
        end_date = None
        
        if start_date_str:
            start_date = datetime.strptime(start_date_str, '%Y-%m-%d')
        if end_date_str:
            end_date = datetime.strptime(end_date_str, '%Y-%m-%d')
            # Set to end of day
            end_date = end_date.replace(hour=23, minute=59, second=59)
        
        # Get customer data
        customer_report = ReportService.get_top_customers_report(
            start_date, end_date, limit
        )
        
        return render_template('reports/customer_report.html',
                             customer_report=customer_report)
    
    except Exception as e:
        current_app.logger.error(f"Customer report error: {str(e)}")
        return render_template('reports/customer_report.html',
                             customer_report={'success': False, 'error': 'Failed to load customer data'})


# Export Routes

@bp.route('/export/sales/<format>')
@login_required
@tenant_required
def export_sales(format):
    """Export sales report in CSV or PDF format."""
    try:
        # Get date range from request args
        start_date_str = request.args.get('start_date')
        end_date_str = request.args.get('end_date')
        
        start_date = None
        end_date = None
        
        if start_date_str:
            start_date = datetime.strptime(start_date_str, '%Y-%m-%d')
        if end_date_str:
            end_date = datetime.strptime(end_date_str, '%Y-%m-%d')
            end_date = end_date.replace(hour=23, minute=59, second=59)
        
        # Get sales data
        sales_summary = ReportService.get_sales_summary(start_date, end_date)
        daily_trend = ReportService.get_daily_sales_trend(start_date, end_date)
        
        if not sales_summary.get('success'):
            return jsonify({'error': 'Failed to generate sales data'}), 500
        
        if format.lower() == 'csv':
            return _export_sales_csv(sales_summary, daily_trend)
        elif format.lower() == 'pdf':
            return _export_sales_pdf(sales_summary, daily_trend)
        else:
            return jsonify({'error': 'Invalid export format'}), 400
    
    except Exception as e:
        current_app.logger.error(f"Sales export error: {str(e)}")
        return jsonify({'error': 'Export failed'}), 500


@bp.route('/export/products/<format>')
@login_required
@tenant_required
def export_product_performance(format):
    """Export product performance report in CSV or PDF format."""
    try:
        # Get parameters from request args
        start_date_str = request.args.get('start_date')
        end_date_str = request.args.get('end_date')
        sort_by = request.args.get('sort_by', 'revenue')
        limit = int(request.args.get('limit', 50))
        
        start_date = None
        end_date = None
        
        if start_date_str:
            start_date = datetime.strptime(start_date_str, '%Y-%m-%d')
        if end_date_str:
            end_date = datetime.strptime(end_date_str, '%Y-%m-%d')
            end_date = end_date.replace(hour=23, minute=59, second=59)
        
        # Get product data
        product_report = ReportService.get_product_performance_report(
            start_date, end_date, limit, sort_by
        )
        
        if not product_report.get('success'):
            return jsonify({'error': 'Failed to generate product data'}), 500
        
        if format.lower() == 'csv':
            return _export_products_csv(product_report)
        elif format.lower() == 'pdf':
            return _export_products_pdf(product_report)
        else:
            return jsonify({'error': 'Invalid export format'}), 400
    
    except Exception as e:
        current_app.logger.error(f"Product export error: {str(e)}")
        return jsonify({'error': 'Export failed'}), 500


@bp.route('/export/inventory/<format>')
@login_required
@tenant_required
def export_inventory(format):
    """Export inventory report in CSV or PDF format."""
    try:
        # Get parameters from request args
        low_stock_threshold = int(request.args.get('low_stock_threshold', 10))
        include_out_of_stock = bool(request.args.get('include_out_of_stock'))
        
        # Get inventory data
        inventory_report = ReportService.get_inventory_report(
            low_stock_threshold, include_out_of_stock
        )
        
        if not inventory_report.get('success'):
            return jsonify({'error': 'Failed to generate inventory data'}), 500
        
        if format.lower() == 'csv':
            return _export_inventory_csv(inventory_report)
        elif format.lower() == 'pdf':
            return _export_inventory_pdf(inventory_report)
        else:
            return jsonify({'error': 'Invalid export format'}), 400
    
    except Exception as e:
        current_app.logger.error(f"Inventory export error: {str(e)}")
        return jsonify({'error': 'Export failed'}), 500


@bp.route('/export/customers/<format>')
@login_required
@tenant_required
def export_customers(format):
    """Export customer report in CSV or PDF format."""
    try:
        # Get parameters from request args
        start_date_str = request.args.get('start_date')
        end_date_str = request.args.get('end_date')
        limit = int(request.args.get('limit', 20))
        
        start_date = None
        end_date = None
        
        if start_date_str:
            start_date = datetime.strptime(start_date_str, '%Y-%m-%d')
        if end_date_str:
            end_date = datetime.strptime(end_date_str, '%Y-%m-%d')
            end_date = end_date.replace(hour=23, minute=59, second=59)
        
        # Get customer data
        customer_report = ReportService.get_top_customers_report(
            start_date, end_date, limit
        )
        
        if not customer_report.get('success'):
            return jsonify({'error': 'Failed to generate customer data'}), 500
        
        if format.lower() == 'csv':
            return _export_customers_csv(customer_report)
        elif format.lower() == 'pdf':
            return _export_customers_pdf(customer_report)
        else:
            return jsonify({'error': 'Invalid export format'}), 400
    
    except Exception as e:
        current_app.logger.error(f"Customer export error: {str(e)}")
        return jsonify({'error': 'Export failed'}), 500


# Helper functions for CSV exports

def _export_sales_csv(sales_summary, daily_trend):
    """Generate CSV export for sales report."""
    output = io.StringIO()
    writer = csv.writer(output)
    
    # Write summary section
    writer.writerow(['Sales Summary Report'])
    writer.writerow(['Generated:', datetime.now().strftime('%Y-%m-%d %H:%M:%S')])
    writer.writerow([])
    
    if sales_summary.get('date_range'):
        writer.writerow(['Date Range:', f"{sales_summary['date_range']['start_date']} to {sales_summary['date_range']['end_date']}"])
        writer.writerow([])
    
    # Write summary metrics
    writer.writerow(['Summary Metrics'])
    writer.writerow(['Metric', 'Value'])
    summary = sales_summary.get('summary', {})
    writer.writerow(['Total Revenue', f"${summary.get('total_revenue', 0):.2f}"])
    writer.writerow(['Total Transactions', summary.get('total_transactions', 0)])
    writer.writerow(['Average Transaction Value', f"${summary.get('avg_transaction_value', 0):.2f}"])
    writer.writerow(['Total Items Sold', summary.get('total_items_sold', 0)])
    writer.writerow(['Profit Margin', f"{summary.get('profit_margin', 0):.1f}%"])
    writer.writerow([])
    
    # Write daily trend data
    if daily_trend.get('success') and daily_trend.get('trend_data'):
        writer.writerow(['Daily Sales Trend'])
        writer.writerow(['Date', 'Transactions', 'Revenue', 'Tax', 'Discount', 'Net Sales'])
        for day in daily_trend['trend_data']:
            writer.writerow([
                day['date'],
                day['transaction_count'],
                f"${day['total_revenue']:.2f}",
                f"${day['total_tax']:.2f}",
                f"${day['total_discount']:.2f}",
                f"${day['net_sales']:.2f}"
            ])
    
    # Create response
    response = make_response(output.getvalue())
    response.headers['Content-Type'] = 'text/csv'
    response.headers['Content-Disposition'] = f'attachment; filename=sales_report_{datetime.now().strftime("%Y%m%d")}.csv'
    return response


def _export_products_csv(product_report):
    """Generate CSV export for product performance report."""
    output = io.StringIO()
    writer = csv.writer(output)
    
    # Write header
    writer.writerow(['Product Performance Report'])
    writer.writerow(['Generated:', datetime.now().strftime('%Y-%m-%d %H:%M:%S')])
    writer.writerow([])
    
    if product_report.get('date_range'):
        writer.writerow(['Date Range:', f"{product_report['date_range']['start_date']} to {product_report['date_range']['end_date']}"])
        writer.writerow([])
    
    # Write summary
    writer.writerow(['Summary'])
    summary = product_report.get('summary', {})
    writer.writerow(['Total Products', summary.get('total_products', 0)])
    writer.writerow(['Total Revenue', f"${summary.get('total_revenue', 0):.2f}"])
    writer.writerow(['Total Profit', f"${summary.get('total_profit', 0):.2f}"])
    writer.writerow(['Total Quantity Sold', summary.get('total_quantity_sold', 0)])
    writer.writerow([])
    
    # Write product details
    writer.writerow(['Product Details'])
    writer.writerow(['Product Name', 'SKU', 'Category', 'Quantity Sold', 'Revenue', 'Profit', 'Profit Margin %', 'Current Stock', 'Inventory Turnover'])
    
    for product in product_report.get('products', []):
        writer.writerow([
            product['product_name'],
            product.get('sku', ''),
            product.get('category_name', 'Uncategorized'),
            product['total_sold'],
            f"${product['total_revenue']:.2f}",
            f"${product['total_profit']:.2f}",
            f"{product['profit_margin']:.1f}%",
            product['current_stock'],
            f"{product['inventory_turnover']:.2f}"
        ])
    
    # Create response
    response = make_response(output.getvalue())
    response.headers['Content-Type'] = 'text/csv'
    response.headers['Content-Disposition'] = f'attachment; filename=product_performance_{datetime.now().strftime("%Y%m%d")}.csv'
    return response


def _export_inventory_csv(inventory_report):
    """Generate CSV export for inventory report."""
    output = io.StringIO()
    writer = csv.writer(output)
    
    # Write header
    writer.writerow(['Inventory Report'])
    writer.writerow(['Generated:', datetime.now().strftime('%Y-%m-%d %H:%M:%S')])
    writer.writerow([])
    
    # Write summary
    writer.writerow(['Summary'])
    summary = inventory_report.get('summary', {})
    writer.writerow(['Total Products', summary.get('total_products', 0)])
    writer.writerow(['In Stock', summary.get('in_stock_count', 0)])
    writer.writerow(['Low Stock', summary.get('low_stock_count', 0)])
    writer.writerow(['Out of Stock', summary.get('out_of_stock_count', 0)])
    writer.writerow(['Total Inventory Value', f"${summary.get('total_inventory_value', 0):.2f}"])
    writer.writerow([])
    
    # Write inventory details by status
    inventory_status = inventory_report.get('inventory_status', {})
    
    for status_name, products in inventory_status.items():
        if products:
            writer.writerow([f'{status_name.replace("_", " ").title()} Items'])
            writer.writerow(['Product Name', 'SKU', 'Category', 'Current Stock', 'Min Stock', 'Max Stock', 'Stock Value', 'Track Inventory'])
            
            for product in products:
                writer.writerow([
                    product['product_name'],
                    product.get('sku', ''),
                    product.get('category_name', 'Uncategorized'),
                    product['current_stock'],
                    product.get('minimum_stock', ''),
                    product.get('maximum_stock', ''),
                    f"${product['stock_value']:.2f}",
                    'Yes' if product.get('track_inventory') else 'No'
                ])
            writer.writerow([])
    
    # Create response
    response = make_response(output.getvalue())
    response.headers['Content-Type'] = 'text/csv'
    response.headers['Content-Disposition'] = f'attachment; filename=inventory_report_{datetime.now().strftime("%Y%m%d")}.csv'
    return response


def _export_customers_csv(customer_report):
    """Generate CSV export for customer report."""
    output = io.StringIO()
    writer = csv.writer(output)
    
    # Write header
    writer.writerow(['Customer Report'])
    writer.writerow(['Generated:', datetime.now().strftime('%Y-%m-%d %H:%M:%S')])
    writer.writerow([])
    
    if customer_report.get('date_range'):
        writer.writerow(['Date Range:', f"{customer_report['date_range']['start_date']} to {customer_report['date_range']['end_date']}"])
        writer.writerow([])
    
    # Write summary
    writer.writerow(['Summary'])
    summary = customer_report.get('summary', {})
    writer.writerow(['Total Customers', summary.get('total_customers', 0)])
    writer.writerow(['Total Customer Revenue', f"${summary.get('total_customer_revenue', 0):.2f}"])
    writer.writerow(['Total Customer Transactions', summary.get('total_customer_transactions', 0)])
    writer.writerow(['Average Customer Value', f"${summary.get('avg_customer_value', 0):.2f}"])
    writer.writerow([])
    
    # Write customer details
    writer.writerow(['Customer Details'])
    writer.writerow(['Customer Name', 'Email', 'Phone', 'Transactions', 'Total Spent', 'Avg Transaction', 'Items Purchased', 'Last Purchase'])
    
    for customer in customer_report.get('customers', []):
        writer.writerow([
            customer.get('customer_name', 'Anonymous'),
            customer.get('customer_email', ''),
            customer.get('customer_phone', ''),
            customer['transaction_count'],
            f"${customer['total_spent']:.2f}",
            f"${customer['avg_transaction_value']:.2f}",
            customer['total_items_purchased'],
            customer.get('last_purchase_date', '')[:10] if customer.get('last_purchase_date') else ''
        ])
    
    # Create response
    response = make_response(output.getvalue())
    response.headers['Content-Type'] = 'text/csv'
    response.headers['Content-Disposition'] = f'attachment; filename=customer_report_{datetime.now().strftime("%Y%m%d")}.csv'
    return response


# Helper functions for PDF exports

def _export_sales_pdf(sales_summary, daily_trend):
    """Generate PDF export for sales report."""
    buffer = io.BytesIO()
    doc = SimpleDocTemplate(buffer, pagesize=letter)
    styles = getSampleStyleSheet()
    story = []
    
    # Title
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=18,
        spaceAfter=30,
        alignment=1  # Center alignment
    )
    story.append(Paragraph("Sales Report", title_style))
    story.append(Paragraph(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}", styles['Normal']))
    story.append(Spacer(1, 20))
    
    # Date range
    if sales_summary.get('date_range'):
        story.append(Paragraph(f"Date Range: {sales_summary['date_range']['start_date']} to {sales_summary['date_range']['end_date']}", styles['Normal']))
        story.append(Spacer(1, 20))
    
    # Summary table
    story.append(Paragraph("Summary Metrics", styles['Heading2']))
    summary = sales_summary.get('summary', {})
    summary_data = [
        ['Metric', 'Value'],
        ['Total Revenue', f"${summary.get('total_revenue', 0):.2f}"],
        ['Total Transactions', str(summary.get('total_transactions', 0))],
        ['Average Transaction Value', f"${summary.get('avg_transaction_value', 0):.2f}"],
        ['Total Items Sold', str(summary.get('total_items_sold', 0))],
        ['Profit Margin', f"{summary.get('profit_margin', 0):.1f}%"]
    ]
    
    summary_table = Table(summary_data)
    summary_table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, 0), 14),
        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
        ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
        ('GRID', (0, 0), (-1, -1), 1, colors.black)
    ]))
    story.append(summary_table)
    story.append(Spacer(1, 30))
    
    # Daily trend table (limited to fit on page)
    if daily_trend.get('success') and daily_trend.get('trend_data'):
        story.append(Paragraph("Daily Sales Trend", styles['Heading2']))
        trend_data = [['Date', 'Transactions', 'Revenue', 'Net Sales']]
        
        # Limit to last 10 days to fit on page
        for day in daily_trend['trend_data'][-10:]:
            trend_data.append([
                day['date'],
                str(day['transaction_count']),
                f"${day['total_revenue']:.2f}",
                f"${day['net_sales']:.2f}"
            ])
        
        trend_table = Table(trend_data)
        trend_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 12),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        story.append(trend_table)
    
    doc.build(story)
    buffer.seek(0)
    
    response = make_response(buffer.getvalue())
    response.headers['Content-Type'] = 'application/pdf'
    response.headers['Content-Disposition'] = f'attachment; filename=sales_report_{datetime.now().strftime("%Y%m%d")}.pdf'
    return response


def _export_products_pdf(product_report):
    """Generate PDF export for product performance report."""
    buffer = io.BytesIO()
    doc = SimpleDocTemplate(buffer, pagesize=A4)
    styles = getSampleStyleSheet()
    story = []
    
    # Title
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=18,
        spaceAfter=30,
        alignment=1
    )
    story.append(Paragraph("Product Performance Report", title_style))
    story.append(Paragraph(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}", styles['Normal']))
    story.append(Spacer(1, 20))
    
    # Date range
    if product_report.get('date_range'):
        story.append(Paragraph(f"Date Range: {product_report['date_range']['start_date']} to {product_report['date_range']['end_date']}", styles['Normal']))
        story.append(Spacer(1, 20))
    
    # Summary
    story.append(Paragraph("Summary", styles['Heading2']))
    summary = product_report.get('summary', {})
    summary_data = [
        ['Metric', 'Value'],
        ['Total Products', str(summary.get('total_products', 0))],
        ['Total Revenue', f"${summary.get('total_revenue', 0):.2f}"],
        ['Total Profit', f"${summary.get('total_profit', 0):.2f}"],
        ['Total Quantity Sold', str(summary.get('total_quantity_sold', 0))]
    ]
    
    summary_table = Table(summary_data)
    summary_table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, 0), 12),
        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
        ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
        ('GRID', (0, 0), (-1, -1), 1, colors.black)
    ]))
    story.append(summary_table)
    story.append(Spacer(1, 30))
    
    # Product details (limited to fit on page)
    story.append(Paragraph("Top Products", styles['Heading2']))
    product_data = [['Product', 'Qty Sold', 'Revenue', 'Profit', 'Margin %']]
    
    # Limit to top 15 products to fit on page
    for product in product_report.get('products', [])[:15]:
        product_data.append([
            product['product_name'][:20] + '...' if len(product['product_name']) > 20 else product['product_name'],
            str(product['total_sold']),
            f"${product['total_revenue']:.2f}",
            f"${product['total_profit']:.2f}",
            f"{product['profit_margin']:.1f}%"
        ])
    
    product_table = Table(product_data)
    product_table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, 0), 10),
        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
        ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
        ('GRID', (0, 0), (-1, -1), 1, colors.black)
    ]))
    story.append(product_table)
    
    doc.build(story)
    buffer.seek(0)
    
    response = make_response(buffer.getvalue())
    response.headers['Content-Type'] = 'application/pdf'
    response.headers['Content-Disposition'] = f'attachment; filename=product_performance_{datetime.now().strftime("%Y%m%d")}.pdf'
    return response


def _export_inventory_pdf(inventory_report):
    """Generate PDF export for inventory report."""
    buffer = io.BytesIO()
    doc = SimpleDocTemplate(buffer, pagesize=A4)
    styles = getSampleStyleSheet()
    story = []
    
    # Title
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=18,
        spaceAfter=30,
        alignment=1
    )
    story.append(Paragraph("Inventory Report", title_style))
    story.append(Paragraph(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}", styles['Normal']))
    story.append(Spacer(1, 20))
    
    # Summary
    story.append(Paragraph("Summary", styles['Heading2']))
    summary = inventory_report.get('summary', {})
    summary_data = [
        ['Metric', 'Value'],
        ['Total Products', str(summary.get('total_products', 0))],
        ['In Stock', str(summary.get('in_stock_count', 0))],
        ['Low Stock', str(summary.get('low_stock_count', 0))],
        ['Out of Stock', str(summary.get('out_of_stock_count', 0))],
        ['Total Inventory Value', f"${summary.get('total_inventory_value', 0):.2f}"]
    ]
    
    summary_table = Table(summary_data)
    summary_table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, 0), 12),
        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
        ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
        ('GRID', (0, 0), (-1, -1), 1, colors.black)
    ]))
    story.append(summary_table)
    story.append(Spacer(1, 30))
    
    # Low stock items
    inventory_status = inventory_report.get('inventory_status', {})
    if inventory_status.get('low_stock'):
        story.append(Paragraph("Low Stock Items", styles['Heading2']))
        low_stock_data = [['Product', 'Current Stock', 'Min Stock', 'Stock Value']]
        
        for product in inventory_status['low_stock'][:10]:  # Limit to 10 items
            low_stock_data.append([
                product['product_name'][:25] + '...' if len(product['product_name']) > 25 else product['product_name'],
                str(product['current_stock']),
                str(product.get('minimum_stock', 'Not set')),
                f"${product['stock_value']:.2f}"
            ])
        
        low_stock_table = Table(low_stock_data)
        low_stock_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        story.append(low_stock_table)
    
    doc.build(story)
    buffer.seek(0)
    
    response = make_response(buffer.getvalue())
    response.headers['Content-Type'] = 'application/pdf'
    response.headers['Content-Disposition'] = f'attachment; filename=inventory_report_{datetime.now().strftime("%Y%m%d")}.pdf'
    return response


def _export_customers_pdf(customer_report):
    """Generate PDF export for customer report."""
    buffer = io.BytesIO()
    doc = SimpleDocTemplate(buffer, pagesize=A4)
    styles = getSampleStyleSheet()
    story = []
    
    # Title
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=18,
        spaceAfter=30,
        alignment=1
    )
    story.append(Paragraph("Customer Report", title_style))
    story.append(Paragraph(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}", styles['Normal']))
    story.append(Spacer(1, 20))
    
    # Date range
    if customer_report.get('date_range'):
        story.append(Paragraph(f"Date Range: {customer_report['date_range']['start_date']} to {customer_report['date_range']['end_date']}", styles['Normal']))
        story.append(Spacer(1, 20))
    
    # Summary
    story.append(Paragraph("Summary", styles['Heading2']))
    summary = customer_report.get('summary', {})
    summary_data = [
        ['Metric', 'Value'],
        ['Total Customers', str(summary.get('total_customers', 0))],
        ['Total Customer Revenue', f"${summary.get('total_customer_revenue', 0):.2f}"],
        ['Total Customer Transactions', str(summary.get('total_customer_transactions', 0))],
        ['Average Customer Value', f"${summary.get('avg_customer_value', 0):.2f}"]
    ]
    
    summary_table = Table(summary_data)
    summary_table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, 0), 12),
        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
        ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
        ('GRID', (0, 0), (-1, -1), 1, colors.black)
    ]))
    story.append(summary_table)
    story.append(Spacer(1, 30))
    
    # Top customers
    story.append(Paragraph("Top Customers", styles['Heading2']))
    customer_data = [['Customer', 'Transactions', 'Total Spent', 'Avg Transaction']]
    
    for customer in customer_report.get('customers', [])[:15]:  # Limit to 15 customers
        customer_name = customer.get('customer_name', 'Anonymous')
        if len(customer_name) > 20:
            customer_name = customer_name[:20] + '...'
        
        customer_data.append([
            customer_name,
            str(customer['transaction_count']),
            f"${customer['total_spent']:.2f}",
            f"${customer['avg_transaction_value']:.2f}"
        ])
    
    customer_table = Table(customer_data)
    customer_table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, 0), 10),
        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
        ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
        ('GRID', (0, 0), (-1, -1), 1, colors.black)
    ]))
    story.append(customer_table)
    
    doc.build(story)
    buffer.seek(0)
    
    response = make_response(buffer.getvalue())
    response.headers['Content-Type'] = 'application/pdf'
    response.headers['Content-Disposition'] = f'attachment; filename=customer_report_{datetime.now().strftime("%Y%m%d")}.pdf'
    return response