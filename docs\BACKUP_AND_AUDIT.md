# Backup and Audit System Documentation

## Overview

The SaaS POS System includes a comprehensive backup and audit logging system designed to ensure data integrity, security compliance, and operational transparency. This system provides automated backups, detailed audit trails, and data retention management.

## Table of Contents

1. [Architecture](#architecture)
2. [Audit Logging](#audit-logging)
3. [Backup System](#backup-system)
4. [Data Retention](#data-retention)
5. [Configuration](#configuration)
6. [CLI Commands](#cli-commands)
7. [Integration Guide](#integration-guide)
8. [Security Considerations](#security-considerations)
9. [Troubleshooting](#troubleshooting)
10. [API Reference](#api-reference)

## Architecture

### Components

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Application   │    │  Audit Service  │    │ Backup Service  │
│     Routes      │───▶│                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
                       ┌─────────────────┐    ┌─────────────────┐
                       │   Audit Logs    │    │  Backup Logs    │
                       │   (Database)    │    │   (Database)    │
                       └─────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
                       ┌─────────────────┐    ┌─────────────────┐
                       │ Data Retention  │    │  Backup Files   │
                       │    Manager      │    │  (File System)  │
                       └─────────────────┘    └─────────────────┘
```

### Database Schema

#### Audit Log Table
```sql
CREATE TABLE audit_log (
    id INTEGER PRIMARY KEY,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    action ENUM(...) NOT NULL,
    severity ENUM('LOW', 'MEDIUM', 'HIGH', 'CRITICAL') NOT NULL,
    user_id INTEGER REFERENCES user(id),
    tenant_id INTEGER NOT NULL REFERENCES tenant(id),
    ip_address VARCHAR(45),
    user_agent TEXT,
    request_method VARCHAR(10),
    request_url TEXT,
    description TEXT NOT NULL,
    old_values JSON,
    new_values JSON,
    resource_type VARCHAR(50),
    resource_id VARCHAR(50),
    success BOOLEAN NOT NULL DEFAULT TRUE,
    error_message TEXT,
    session_id VARCHAR(100),
    correlation_id VARCHAR(100)
);
```

#### Backup Log Table
```sql
CREATE TABLE backup_log (
    id INTEGER PRIMARY KEY,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    backup_type VARCHAR(20) NOT NULL,
    backup_status VARCHAR(20) NOT NULL DEFAULT 'in_progress',
    backup_filename VARCHAR(255) NOT NULL,
    backup_path TEXT NOT NULL,
    backup_size BIGINT,
    started_at DATETIME NOT NULL,
    completed_at DATETIME,
    duration_seconds INTEGER,
    tenant_id INTEGER REFERENCES tenant(id),
    tables_backed_up JSON,
    records_count INTEGER,
    error_message TEXT,
    error_details JSON,
    checksum VARCHAR(64),
    verified BOOLEAN NOT NULL DEFAULT FALSE,
    verification_date DATETIME
);
```

## Audit Logging

### Audit Actions

The system tracks the following types of actions:

#### Authentication Actions
- `LOGIN` - Successful user login
- `LOGOUT` - User logout
- `LOGIN_FAILED` - Failed login attempt
- `PASSWORD_RESET` - Password reset request
- `ACCOUNT_LOCKED` - Account locked due to failed attempts

#### User Management Actions
- `USER_CREATED` - New user account created
- `USER_UPDATED` - User account modified
- `USER_DELETED` - User account deleted
- `USER_ROLE_CHANGED` - User role/permissions changed

#### Product/Inventory Actions
- `PRODUCT_CREATED` - New product added
- `PRODUCT_UPDATED` - Product information modified
- `PRODUCT_DELETED` - Product removed
- `INVENTORY_ADJUSTED` - Inventory levels adjusted

#### Transaction Actions
- `TRANSACTION_CREATED` - New transaction processed
- `TRANSACTION_UPDATED` - Transaction modified
- `TRANSACTION_VOIDED` - Transaction voided/cancelled
- `PAYMENT_PROCESSED` - Payment processed

#### System Actions
- `BACKUP_CREATED` - Database backup created
- `BACKUP_RESTORED` - Database restored from backup
- `DATA_EXPORTED` - Data exported/accessed
- `SETTINGS_CHANGED` - System settings modified

#### Security Actions
- `UNAUTHORIZED_ACCESS` - Unauthorized access attempt
- `SUSPICIOUS_ACTIVITY` - Suspicious behavior detected
- `DATA_BREACH_ATTEMPT` - Potential data breach attempt

### Severity Levels

- **LOW** - Routine operations (product views, normal logins)
- **MEDIUM** - Important operations (data modifications, user management)
- **HIGH** - Critical operations (failed logins, permission changes)
- **CRITICAL** - Security incidents (unauthorized access, data breaches)

### Usage Examples

#### Basic Audit Logging
```python
from app.services.audit_service import AuditService
from app.models.audit import AuditAction, AuditSeverity

# Simple audit log
AuditService.log(
    action=AuditAction.PRODUCT_CREATED,
    description="New product 'Coffee Mug' created",
    severity=AuditSeverity.MEDIUM,
    resource_type='product',
    resource_id=123
)
```

#### Authentication Logging
```python
# Successful login
AuditService.log_authentication(
    action=AuditAction.LOGIN,
    user_email='<EMAIL>',
    success=True,
    user_id=user.id,
    tenant_id=user.tenant_id
)

# Failed login
AuditService.log_authentication(
    action=AuditAction.LOGIN_FAILED,
    user_email='<EMAIL>',
    success=False,
    error_message='Invalid password'
)
```

#### Data Change Logging
```python
# Log data modifications with before/after values
old_values = {'name': 'Old Product Name', 'price': 10.00}
new_values = {'name': 'New Product Name', 'price': 15.00}

AuditService.log_data_change(
    action=AuditAction.PRODUCT_UPDATED,
    resource_type='product',
    resource_id=product.id,
    description=f"Product {product.name} updated",
    old_values=old_values,
    new_values=new_values
)
```

#### Using Decorators
```python
from app.utils.audit_decorators import audit_action, audit_data_change

@audit_action(
    action=AuditAction.PRODUCT_CREATED,
    description="Product '{result.name}' created",
    severity=AuditSeverity.MEDIUM,
    resource_type='product'
)
def create_product(data):
    product = Product(**data)
    product.save()
    return product

@audit_data_change(
    action=AuditAction.PRODUCT_UPDATED,
    resource_type='product',
    get_resource_id=lambda args, kwargs, result: result.id,
    get_old_values=lambda args, kwargs: Product.query.get(args[0]).to_dict()
)
def update_product(product_id, **updates):
    product = Product.query.get(product_id)
    for key, value in updates.items():
        setattr(product, key, value)
    product.save()
    return product
```

### Querying Audit Logs

```python
# Get recent audit logs for a tenant
logs = AuditService.get_audit_logs(
    tenant_id=1,
    limit=50,
    action=AuditAction.LOGIN,
    severity=AuditSeverity.HIGH,
    start_date=datetime.utcnow() - timedelta(days=7)
)

# Get security events
security_events = AuditService.get_security_events(
    tenant_id=1,
    hours=24
)

# Get user activity
user_activity = AuditService.get_user_activity(
    user_id=123,
    tenant_id=1,
    days=30
)

# Get audit summary
summary = AuditService.get_audit_summary(
    tenant_id=1,
    days=30
)
```

### Suspicious Activity Detection

The system automatically detects suspicious patterns:

```python
suspicious_patterns = AuditService.detect_suspicious_activity(tenant_id=1)

# Returns patterns like:
# [
#     {
#         'type': 'multiple_failed_logins',
#         'description': 'Multiple failed login attempts (5) from IP *************',
#         'severity': 'high',
#         'ip_address': '*************',
#         'count': 5
#     },
#     {
#         'type': 'high_volume_transactions',
#         'description': 'High volume of transactions (150) from user 123',
#         'severity': 'medium',
#         'user_id': 123,
#         'count': 150
#     }
# ]
```

## Backup System

### Backup Types

- **Full Backup** - Complete database backup
- **Tenant Backup** - Backup specific to one tenant
- **Incremental Backup** - Only changes since last backup (planned feature)

### Creating Backups

#### Programmatic Backup Creation
```python
from app.services.backup_service import BackupService

backup_service = BackupService()

# Create full system backup
backup_log = backup_service.create_full_backup()

# Create tenant-specific backup
backup_log = backup_service.create_full_backup(tenant_id=1)

# Scheduled automated backup
backup_log = backup_service.schedule_automated_backup()
```

#### Backup Process Flow
1. Create backup log entry with 'in_progress' status
2. Generate backup filename with timestamp
3. Execute database dump (SQLite or PostgreSQL)
4. Compress backup file with gzip
5. Calculate SHA-256 checksum
6. Update backup log with completion details
7. Log audit event for backup creation

### Backup Verification

```python
# Verify backup integrity
is_valid = backup_service.verify_backup(backup_log.id)

# Manual verification with checksum
backup_log.verify_backup(calculated_checksum)
```

### Backup Restoration

```python
# Restore from backup
success = backup_service.restore_backup(
    backup_log_id=123,
    target_tenant_id=1  # Optional: restore to different tenant
)
```

### Backup Management

```python
# Get backup system status
status = backup_service.get_backup_status()
# Returns: {
#     'total_backups': 25,
#     'successful_backups': 24,
#     'success_rate': 96.0,
#     'last_successful_backup': '2024-01-15T10:30:00',
#     'total_backup_size_mb': 150.5,
#     'recent_backups': [...]
# }

# Get recent backups
recent_backups = BackupLog.get_recent_backups(limit=10)

# Cleanup old backups
deleted_count = backup_service.cleanup_old_backups(retention_days=30)
```

## Data Retention

### Retention Policies

Default retention periods (configurable via environment variables):

- **Audit Logs**: 90 days (`AUDIT_LOG_RETENTION_DAYS`)
- **Backup Logs**: 30 days (`BACKUP_LOG_RETENTION_DAYS`)
- **Backup Files**: 30 days (`BACKUP_FILE_RETENTION_DAYS`)
- **Session Logs**: 7 days (`SESSION_LOG_RETENTION_DAYS`)
- **Error Logs**: 30 days (`ERROR_LOG_RETENTION_DAYS`)
- **Transaction Logs**: 365 days (`TRANSACTION_LOG_RETENTION_DAYS`)
- **User Activity Logs**: 180 days (`USER_ACTIVITY_RETENTION_DAYS`)

### Data Retention Management

```python
from app.utils.data_retention import DataRetentionManager

manager = DataRetentionManager()

# Run cleanup (dry run)
result = manager.run_cleanup(dry_run=True)

# Run actual cleanup
result = manager.run_cleanup(dry_run=False)

# Get retention status
status = manager.get_retention_status()

# Optimize database after cleanup
optimize_result = manager.optimize_database()
```

### Automated Cleanup Scheduling

```python
# Get schedule configurations
daily_schedule = manager.schedule_cleanup('daily')
weekly_schedule = manager.schedule_cleanup('weekly')
monthly_schedule = manager.schedule_cleanup('monthly')

# Example cron configurations:
# Daily: '0 2 * * *' (2 AM daily)
# Weekly: '0 3 * * 0' (3 AM on Sundays)
# Monthly: '0 4 1 * *' (4 AM on 1st of month)
```

## Configuration

### Environment Variables

```bash
# Backup Configuration
BACKUP_DIR=/path/to/backups
AUTO_BACKUP_ENABLED=true
AUTO_BACKUP_SCHEDULE=daily

# Retention Configuration
AUDIT_LOG_RETENTION_DAYS=90
BACKUP_LOG_RETENTION_DAYS=30
BACKUP_FILE_RETENTION_DAYS=30
SESSION_LOG_RETENTION_DAYS=7
ERROR_LOG_RETENTION_DAYS=30
TRANSACTION_LOG_RETENTION_DAYS=365
USER_ACTIVITY_RETENTION_DAYS=180
```

### Application Configuration

```python
# config.py
class Config:
    # Backup and audit settings
    BACKUP_DIR = os.environ.get('BACKUP_DIR', 'backups')
    AUDIT_LOG_RETENTION_DAYS = int(os.environ.get('AUDIT_LOG_RETENTION_DAYS', 90))
    BACKUP_LOG_RETENTION_DAYS = int(os.environ.get('BACKUP_LOG_RETENTION_DAYS', 30))
    BACKUP_FILE_RETENTION_DAYS = int(os.environ.get('BACKUP_FILE_RETENTION_DAYS', 30))
    AUTO_BACKUP_ENABLED = os.environ.get('AUTO_BACKUP_ENABLED', 'true').lower() == 'true'
    AUTO_BACKUP_SCHEDULE = os.environ.get('AUTO_BACKUP_SCHEDULE', 'daily')
```

## CLI Commands

### Backup Commands

```bash
# Create backup
flask backup create
flask backup create --tenant-id 1
flask backup create --type full

# List backups
flask backup list
flask backup list --limit 20

# Restore backup
flask backup restore --backup-id 123
flask backup restore --backup-id 123 --target-tenant-id 1

# Verify backup
flask backup verify --backup-id 123

# Backup status
flask backup status

# Cleanup old backups
flask backup cleanup --retention-days 30
```

### Audit Commands

```bash
# View audit logs
flask audit logs --tenant-id 1
flask audit logs --tenant-id 1 --limit 100
flask audit logs --tenant-id 1 --action login
flask audit logs --tenant-id 1 --severity high
flask audit logs --tenant-id 1 --user-id 123
flask audit logs --tenant-id 1 --days 7

# Security events
flask audit security
flask audit security --tenant-id 1 --hours 24

# Audit summary
flask audit summary --tenant-id 1
flask audit summary --tenant-id 1 --days 30

# Export audit logs
flask audit export --tenant-id 1 --start-date 2024-01-01 --end-date 2024-01-31
flask audit export --tenant-id 1 --start-date 2024-01-01 --end-date 2024-01-31 --output audit_export.json

# Cleanup old audit logs
flask audit cleanup --retention-days 90
```

### Data Retention Commands

```bash
# Run cleanup
flask retention cleanup
flask retention cleanup --dry-run

# Retention status
flask retention status
```

## Integration Guide

### Adding Audit Logging to Existing Routes

#### Method 1: Using Decorators (Recommended)

```python
from app.utils.audit_decorators import audit_action, audit_authentication
from app.models.audit import AuditAction, AuditSeverity

@app.route('/products', methods=['POST'])
@login_required
@audit_action(
    action=AuditAction.PRODUCT_CREATED,
    description="Product '{result.name}' created",
    severity=AuditSeverity.MEDIUM,
    resource_type='product'
)
def create_product():
    # Your existing code
    product = Product(**request.json)
    product.save()
    return product

@app.route('/auth/login', methods=['POST'])
@audit_authentication(AuditAction.LOGIN)
def login():
    # Your existing login code
    user = authenticate_user(email, password)
    return user
```

#### Method 2: Manual Logging

```python
from app.services.audit_service import AuditService

@app.route('/products/<int:product_id>', methods=['DELETE'])
@login_required
def delete_product(product_id):
    try:
        product = Product.query.get_or_404(product_id)
        product_name = product.name
        
        product.delete()
        
        # Manual audit logging
        AuditService.log(
            action=AuditAction.PRODUCT_DELETED,
            description=f"Product '{product_name}' deleted",
            severity=AuditSeverity.HIGH,
            resource_type='product',
            resource_id=product_id,
            old_values={'name': product_name, 'id': product_id}
        )
        
        return {'success': True}
        
    except Exception as e:
        # Log the failure
        AuditService.log(
            action=AuditAction.PRODUCT_DELETED,
            description=f"Failed to delete product {product_id}: {str(e)}",
            severity=AuditSeverity.HIGH,
            resource_type='product',
            resource_id=product_id,
            success=False,
            error_message=str(e)
        )
        raise
```

### Correlation IDs for Related Operations

```python
import uuid

@app.route('/products/bulk-update', methods=['POST'])
@login_required
def bulk_update_products():
    correlation_id = str(uuid.uuid4())
    
    # Log start of bulk operation
    AuditService.log(
        action=AuditAction.PRODUCT_UPDATED,
        description=f"Bulk update started for {len(products)} products",
        correlation_id=correlation_id
    )
    
    for product_data in products:
        try:
            # Update product
            update_product(product_data)
            
            # Log individual update with same correlation ID
            AuditService.log(
                action=AuditAction.PRODUCT_UPDATED,
                description=f"Product {product_data['id']} updated in bulk operation",
                correlation_id=correlation_id,
                resource_type='product',
                resource_id=product_data['id']
            )
        except Exception as e:
            # Log individual failure
            AuditService.log(
                action=AuditAction.PRODUCT_UPDATED,
                description=f"Product {product_data['id']} update failed in bulk operation",
                correlation_id=correlation_id,
                success=False,
                error_message=str(e)
            )
```

### Automated Backup Setup

#### Using Cron Jobs

```bash
# Add to crontab (crontab -e)
# Daily backup at 2 AM
0 2 * * * cd /path/to/app && flask backup create

# Weekly full backup on Sundays at 3 AM
0 3 * * 0 cd /path/to/app && flask backup create --type full

# Monthly cleanup on 1st of month at 4 AM
0 4 1 * * cd /path/to/app && flask retention cleanup
```

#### Using Celery (Recommended for Production)

```python
from celery import Celery
from app.services.backup_service import BackupService
from app.utils.data_retention import DataRetentionManager

@celery.task
def create_automated_backup():
    backup_service = BackupService()
    return backup_service.schedule_automated_backup()

@celery.task
def cleanup_old_data():
    manager = DataRetentionManager()
    return manager.run_cleanup(dry_run=False)

# Schedule tasks
from celery.schedules import crontab

celery.conf.beat_schedule = {
    'daily-backup': {
        'task': 'create_automated_backup',
        'schedule': crontab(hour=2, minute=0),  # 2 AM daily
    },
    'weekly-cleanup': {
        'task': 'cleanup_old_data',
        'schedule': crontab(hour=3, minute=0, day_of_week=0),  # 3 AM Sundays
    },
}
```

## Security Considerations

### Data Protection

1. **Encryption at Rest**
   - Backup files should be encrypted when stored
   - Consider using database-level encryption for audit logs

2. **Access Control**
   - Limit access to backup files and audit logs
   - Use role-based permissions for audit log viewing

3. **Network Security**
   - Secure backup storage locations
   - Use encrypted connections for remote backups

### Audit Log Integrity

1. **Immutability**
   - Audit logs should not be modifiable after creation
   - Consider using append-only storage

2. **Tampering Detection**
   - Implement checksums for audit log integrity
   - Regular integrity verification

3. **Backup Security**
   - Verify backup integrity with checksums
   - Store backups in secure, separate locations

### Compliance Considerations

1. **Data Retention Compliance**
   - Configure retention periods according to regulations
   - Ensure secure deletion of expired data

2. **Audit Trail Requirements**
   - Maintain complete audit trails for compliance
   - Regular audit log reviews and reporting

3. **Privacy Protection**
   - Avoid logging sensitive personal information
   - Implement data anonymization where required

## Troubleshooting

### Common Issues

#### Backup Failures

**Issue**: Backup creation fails with permission errors
```
Solution: Check backup directory permissions
chmod 755 /path/to/backups
chown app:app /path/to/backups
```

**Issue**: SQLite database locked during backup
```
Solution: Ensure no long-running transactions
Check for deadlocks in application code
Consider using WAL mode for SQLite
```

**Issue**: PostgreSQL backup fails with authentication error
```
Solution: Check database credentials and permissions
Ensure PGPASSWORD environment variable is set
Verify pg_dump is installed and accessible
```

#### Audit Log Issues

**Issue**: Audit logs not being created
```
Solution: Check database connectivity
Verify audit service is properly initialized
Check for exceptions in application logs
```

**Issue**: Performance issues with large audit logs
```
Solution: Implement audit log cleanup
Add database indexes for common queries
Consider partitioning audit log table
```

**Issue**: Missing request context in audit logs
```
Solution: Ensure audit logging is called within request context
Use application context for background tasks
Check Flask-Login configuration
```

#### Data Retention Issues

**Issue**: Cleanup process running too slowly
```
Solution: Increase batch size for deletions
Run cleanup during off-peak hours
Consider database optimization
```

**Issue**: Disk space not freed after cleanup
```
Solution: Run VACUUM on SQLite databases
Check for database fragmentation
Verify file system cleanup
```

### Debugging

#### Enable Debug Logging

```python
import logging

# Enable debug logging for backup and audit services
logging.getLogger('app.services.backup_service').setLevel(logging.DEBUG)
logging.getLogger('app.services.audit_service').setLevel(logging.DEBUG)
logging.getLogger('app.utils.data_retention').setLevel(logging.DEBUG)
```

#### Verify System Status

```python
# Check audit system status
from app.services.audit_service import AuditService
summary = AuditService.get_audit_summary(tenant_id=1)
print(f"Total audit events: {summary['total_events']}")

# Check backup system status
from app.services.backup_service import BackupService
backup_service = BackupService()
status = backup_service.get_backup_status()
print(f"Total backups: {status['total_backups']}")
print(f"Success rate: {status['success_rate']}%")

# Check retention status
from app.utils.data_retention import DataRetentionManager
manager = DataRetentionManager()
status = manager.get_retention_status()
print("Retention policies:", status['retention_policies'])
```

#### Test Backup and Restore

```python
# Test backup creation and verification
backup_service = BackupService()
backup_log = backup_service.create_full_backup()
print(f"Backup created: {backup_log.backup_filename}")

# Verify backup
is_valid = backup_service.verify_backup(backup_log.id)
print(f"Backup valid: {is_valid}")

# Test restore (use with caution!)
# success = backup_service.restore_backup(backup_log.id)
```

## API Reference

### AuditService Methods

```python
class AuditService:
    @staticmethod
    def log(action, description, severity=AuditSeverity.LOW, **kwargs) -> AuditLog
    
    @staticmethod
    def log_authentication(action, user_email, success=True, **kwargs) -> AuditLog
    
    @staticmethod
    def log_data_change(action, resource_type, resource_id, description, **kwargs) -> AuditLog
    
    @staticmethod
    def log_transaction(action, transaction_id, amount, **kwargs) -> AuditLog
    
    @staticmethod
    def log_security_event(action, description, severity=AuditSeverity.HIGH, **kwargs) -> AuditLog
    
    @staticmethod
    def log_system_event(action, description, severity=AuditSeverity.LOW, **kwargs) -> AuditLog
    
    @staticmethod
    def get_audit_logs(tenant_id, limit=100, **filters) -> List[AuditLog]
    
    @staticmethod
    def get_security_events(tenant_id=None, hours=24) -> List[AuditLog]
    
    @staticmethod
    def get_user_activity(user_id, tenant_id, days=30) -> List[AuditLog]
    
    @staticmethod
    def get_audit_summary(tenant_id, days=30) -> Dict[str, Any]
    
    @staticmethod
    def detect_suspicious_activity(tenant_id, hours=24) -> List[Dict[str, Any]]
    
    @staticmethod
    def cleanup_old_audit_logs(retention_days=90) -> int
    
    @staticmethod
    def export_audit_logs(tenant_id, start_date, end_date, format='json') -> str
```

### BackupService Methods

```python
class BackupService:
    def create_full_backup(self, tenant_id=None) -> BackupLog
    
    def restore_backup(self, backup_log_id, target_tenant_id=None) -> bool
    
    def verify_backup(self, backup_log_id) -> bool
    
    def schedule_automated_backup(self, backup_type='full', tenant_id=None) -> BackupLog
    
    def cleanup_old_backups(self, retention_days=30) -> int
    
    def get_backup_status(self) -> Dict[str, Any]
```

### DataRetentionManager Methods

```python
class DataRetentionManager:
    def run_cleanup(self, dry_run=False) -> Dict[str, Any]
    
    def get_retention_status(self) -> Dict[str, Any]
    
    def schedule_cleanup(self, schedule_type='daily') -> Dict[str, Any]
    
    def optimize_database(self, dry_run=False) -> Dict[str, Any]
```

### Model Methods

```python
class AuditLog:
    @classmethod
    def log_action(cls, action, tenant_id, description, **kwargs) -> 'AuditLog'
    
    @classmethod
    def get_logs_for_tenant(cls, tenant_id, **filters) -> List['AuditLog']
    
    @classmethod
    def get_security_events(cls, tenant_id=None, hours=24) -> List['AuditLog']
    
    @classmethod
    def cleanup_old_logs(cls, retention_days=90) -> int

class BackupLog:
    def mark_completed(self, backup_size=None, records_count=None, checksum=None)
    
    def mark_failed(self, error_message, error_details=None)
    
    def verify_backup(self, checksum) -> bool
    
    @classmethod
    def get_recent_backups(cls, limit=10, **filters) -> List['BackupLog']
    
    @classmethod
    def cleanup_old_backups(cls, retention_days=30) -> int
```

---

## Quick Start Checklist

1. **Setup**
   - [ ] Run database migrations: `flask db upgrade`
   - [ ] Configure environment variables
   - [ ] Create backup directory: `mkdir -p backups`

2. **Test Basic Functionality**
   - [ ] Create test audit log: `python -c "from app.services.audit_service import AuditService; AuditService.log_system_event('SETTINGS_CHANGED', 'Test log')"`
   - [ ] Create test backup: `flask backup create`
   - [ ] Verify backup: `flask backup verify --backup-id 1`

3. **Integration**
   - [ ] Add audit decorators to critical routes
   - [ ] Set up automated backup schedule
   - [ ] Configure data retention policies

4. **Monitoring**
   - [ ] Set up backup monitoring alerts
   - [ ] Configure audit log review process
   - [ ] Test disaster recovery procedures

For additional support or questions, refer to the troubleshooting section or contact the development team.