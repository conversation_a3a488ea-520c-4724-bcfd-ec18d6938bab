#!/usr/bin/env python3
"""
Verification script for backup and audit logging functionality.

This script tests the basic functionality of the backup and audit system
to ensure everything is working correctly.
"""

import os
import sys
import tempfile
from datetime import datetime, timed<PERSON><PERSON>

# Add the app directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.services.backup_service import BackupService
from app.services.audit_service import AuditService
from app.utils.data_retention import DataRetentionManager
from app.models.audit import AuditLog, BackupLog, AuditAction, AuditSeverity
from app.models.user import User, Tenant
from config import DevelopmentConfig


def test_audit_logging():
    """Test basic audit logging functionality."""
    print("🔍 Testing audit logging...")
    
    # Create test tenant
    tenant = Tenant(
        name='Test Business',
        business_type='retail',
        email='<EMAIL>'
    )
    tenant.save()
    
    # Create test user
    user = User.create_user(
        email='<EMAIL>',
        password='password123',
        first_name='Test',
        last_name='User',
        tenant_id=tenant.id
    )
    
    # Test basic audit logging
    audit_log = AuditService.log(
        action=AuditAction.USER_CREATED,
        description='Test user created for verification',
        user_id=user.id,
        tenant_id=tenant.id,
        severity=AuditSeverity.MEDIUM
    )
    
    assert audit_log is not None
    assert audit_log.action == AuditAction.USER_CREATED
    assert audit_log.user_id == user.id
    assert audit_log.tenant_id == tenant.id
    
    print("✅ Basic audit logging works")
    
    # Test authentication logging
    auth_log = AuditService.log_authentication(
        action=AuditAction.LOGIN,
        user_email=user.email,
        success=True,
        user_id=user.id,
        tenant_id=tenant.id
    )
    
    assert auth_log.action == AuditAction.LOGIN
    assert auth_log.success is True
    
    print("✅ Authentication logging works")
    
    # Test data change logging
    old_values = {'name': 'Old Product'}
    new_values = {'name': 'New Product', 'price': 19.99}
    
    change_log = AuditService.log_data_change(
        action=AuditAction.PRODUCT_UPDATED,
        resource_type='product',
        resource_id=123,
        description='Product updated for verification',
        old_values=old_values,
        new_values=new_values,
        user_id=user.id,
        tenant_id=tenant.id
    )
    
    assert change_log.old_values == old_values
    assert change_log.new_values == new_values
    assert change_log.resource_type == 'product'
    
    print("✅ Data change logging works")
    
    # Test getting audit logs
    logs = AuditService.get_audit_logs(tenant_id=tenant.id, limit=10)
    assert len(logs) >= 3
    
    print("✅ Audit log retrieval works")
    
    # Test audit summary
    summary = AuditService.get_audit_summary(tenant_id=tenant.id)
    assert summary['total_events'] >= 3
    assert 'events_by_severity' in summary
    assert 'events_by_action' in summary
    
    print("✅ Audit summary works")
    
    return tenant, user


def test_backup_functionality():
    """Test backup system functionality."""
    print("\n💾 Testing backup functionality...")
    
    # Create backup service with temporary directory
    backup_service = BackupService()
    temp_dir = tempfile.mkdtemp()
    backup_service.backup_dir = temp_dir
    
    try:
        # Test backup creation
        backup_log = backup_service.create_full_backup()
        
        assert backup_log is not None
        assert backup_log.backup_type == 'full'
        assert backup_log.backup_status == 'completed'
        assert os.path.exists(backup_log.backup_path)
        
        print("✅ Backup creation works")
        
        # Test backup verification
        is_valid = backup_service.verify_backup(backup_log.id)
        assert is_valid is True
        
        print("✅ Backup verification works")
        
        # Test backup status
        status = backup_service.get_backup_status()
        assert 'total_backups' in status
        assert status['total_backups'] >= 1
        
        print("✅ Backup status reporting works")
        
        # Test getting recent backups
        recent_backups = BackupLog.get_recent_backups(limit=5)
        assert len(recent_backups) >= 1
        
        print("✅ Backup log retrieval works")
        
    finally:
        # Cleanup temporary directory
        import shutil
        shutil.rmtree(temp_dir, ignore_errors=True)
    
    return backup_log


def test_data_retention():
    """Test data retention functionality."""
    print("\n🧹 Testing data retention...")
    
    retention_manager = DataRetentionManager()
    
    # Test retention policies
    policies = retention_manager.retention_policies
    assert 'audit_logs' in policies
    assert 'backup_logs' in policies
    assert policies['audit_logs'] > 0
    
    print("✅ Retention policies loaded")
    
    # Test retention status
    status = retention_manager.get_retention_status()
    assert 'retention_policies' in status
    assert 'current_data_sizes' in status
    
    print("✅ Retention status works")
    
    # Test dry run cleanup
    result = retention_manager.run_cleanup(dry_run=True)
    assert result['success'] is True
    assert result['dry_run'] is True
    assert 'operations' in result
    
    print("✅ Dry run cleanup works")
    
    # Test schedule configurations
    daily_schedule = retention_manager.schedule_cleanup('daily')
    assert 'cron' in daily_schedule
    assert 'operations' in daily_schedule
    
    print("✅ Cleanup scheduling works")


def test_integration():
    """Test integration between audit and backup systems."""
    print("\n🔗 Testing system integration...")
    
    # Create some audit logs
    tenant = Tenant.query.first()
    if not tenant:
        tenant = Tenant(name='Integration Test', business_type='retail')
        tenant.save()
    
    # Log backup creation
    AuditService.log_system_event(
        action=AuditAction.BACKUP_CREATED,
        description='Integration test backup',
        additional_data={'test': True}
    )
    
    # Log data export
    AuditService.log(
        action=AuditAction.DATA_EXPORTED,
        description='Integration test data export',
        tenant_id=tenant.id,
        severity=AuditSeverity.MEDIUM
    )
    
    # Test suspicious activity detection
    suspicious_patterns = AuditService.detect_suspicious_activity(tenant_id=tenant.id)
    assert isinstance(suspicious_patterns, list)
    
    print("✅ System integration works")
    
    # Test audit log export
    start_date = datetime.utcnow() - timedelta(days=1)
    end_date = datetime.utcnow()
    
    json_export = AuditService.export_audit_logs(
        tenant_id=tenant.id,
        start_date=start_date,
        end_date=end_date,
        format='json'
    )
    
    assert isinstance(json_export, str)
    assert 'export_info' in json_export
    
    print("✅ Audit log export works")


def test_cli_commands():
    """Test CLI command functionality."""
    print("\n⚡ Testing CLI commands...")
    
    from flask.testing import FlaskCliRunner
    
    app = create_app(DevelopmentConfig)
    runner = FlaskCliRunner(app)
    
    # Test backup status command
    result = runner.invoke(args=['backup', 'status'])
    assert result.exit_code == 0
    assert 'Backup System Status' in result.output
    
    print("✅ Backup CLI commands work")
    
    # Test audit summary command (need tenant ID)
    tenant = Tenant.query.first()
    if tenant:
        result = runner.invoke(args=['audit', 'summary', '--tenant-id', str(tenant.id)])
        assert result.exit_code == 0
        assert 'Audit Summary' in result.output
        
        print("✅ Audit CLI commands work")
    
    # Test retention status command
    result = runner.invoke(args=['retention', 'status'])
    assert result.exit_code == 0
    assert 'Data Retention Status' in result.output
    
    print("✅ Retention CLI commands work")


def main():
    """Run all verification tests."""
    print("🚀 Starting backup and audit system verification...\n")
    
    # Create Flask app and database
    app = create_app(DevelopmentConfig)
    
    with app.app_context():
        # Create tables
        db.create_all()
        
        try:
            # Run tests
            tenant, user = test_audit_logging()
            backup_log = test_backup_functionality()
            test_data_retention()
            test_integration()
            test_cli_commands()
            
            print("\n🎉 All tests passed! Backup and audit system is working correctly.")
            
            # Print summary
            print("\n📊 System Summary:")
            print(f"   • Audit logs created: {AuditLog.query.count()}")
            print(f"   • Backup logs created: {BackupLog.query.count()}")
            print(f"   • Test tenant ID: {tenant.id}")
            print(f"   • Test user ID: {user.id}")
            
            # Show recent audit activity
            print("\n📋 Recent Audit Activity:")
            recent_logs = AuditLog.query.order_by(AuditLog.created_at.desc()).limit(5).all()
            for log in recent_logs:
                print(f"   • {log.created_at.strftime('%H:%M:%S')} - {log.action.value}: {log.description}")
            
            print("\n✅ Verification completed successfully!")
            
        except Exception as e:
            print(f"\n❌ Verification failed: {str(e)}")
            import traceback
            traceback.print_exc()
            return 1
        
        finally:
            # Cleanup test data
            try:
                db.session.query(AuditLog).delete()
                db.session.query(BackupLog).delete()
                db.session.query(User).delete()
                db.session.query(Tenant).delete()
                db.session.commit()
                print("\n🧹 Test data cleaned up")
            except:
                pass
    
    return 0


if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)