"""Unit tests for Product and Category models."""

import pytest
from decimal import Decimal
from app import create_app, db
from app.models.user import Tenant
from app.models.product import Product, Category, InventoryMovement
from config import TestingConfig


@pytest.fixture
def app():
    """Create test application."""
    app = create_app(TestingConfig)
    return app


@pytest.fixture
def client(app):
    """Create test client."""
    return app.test_client()


@pytest.fixture
def app_context(app):
    """Create application context."""
    with app.app_context():
        db.create_all()
        yield app
        db.session.remove()
        db.drop_all()


@pytest.fixture
def sample_tenant(app_context):
    """Create a sample tenant for testing."""
    tenant = Tenant.create(
        name='Test Store',
        business_type='retail',
        email='<EMAIL>'
    )
    return tenant


@pytest.fixture
def sample_category(app_context, sample_tenant):
    """Create a sample category for testing."""
    category = Category.create(
        name='Electronics',
        description='Electronic devices and accessories',
        tenant_id=sample_tenant.id,
        color='#3498db',
        icon='electronics'
    )
    return category


@pytest.fixture
def sample_product(app_context, sample_tenant, sample_category):
    """Create a sample product for testing."""
    product = Product.create(
        name='Smartphone',
        description='Latest model smartphone',
        sku='PHONE-001',
        barcode='1234567890123',
        category_id=sample_category.id,
        tenant_id=sample_tenant.id,
        cost_price=Decimal('300.00'),
        selling_price=Decimal('500.00'),
        current_stock=50,
        minimum_stock=10,
        tax_rate=Decimal('0.0825')  # 8.25%
    )
    return product


class TestCategoryModel:
    """Test cases for Category model."""
    
    def test_category_creation(self, app_context, sample_tenant):
        """Test basic category creation."""
        category = Category.create(
            name='Food & Beverages',
            description='All food and drink items',
            tenant_id=sample_tenant.id,
            color='#e74c3c',
            icon='food',
            sort_order=1
        )
        
        assert category.id is not None
        assert category.name == 'Food & Beverages'
        assert category.description == 'All food and drink items'
        assert category.tenant_id == sample_tenant.id
        assert category.color == '#e74c3c'
        assert category.icon == 'food'
        assert category.sort_order == 1
        assert category.is_active is True
        assert category.parent_id is None
    
    def test_category_hierarchy(self, app_context, sample_tenant):
        """Test category parent-child relationships."""
        # Create parent category
        parent = Category.create(
            name='Beverages',
            tenant_id=sample_tenant.id
        )
        
        # Create child categories
        hot_drinks = Category.create(
            name='Hot Drinks',
            parent_id=parent.id,
            tenant_id=sample_tenant.id
        )
        
        cold_drinks = Category.create(
            name='Cold Drinks',
            parent_id=parent.id,
            tenant_id=sample_tenant.id
        )
        
        # Test relationships
        assert hot_drinks.parent == parent
        assert cold_drinks.parent == parent
        assert len(parent.subcategories) == 2
        assert hot_drinks in parent.subcategories
        assert cold_drinks in parent.subcategories
    
    def test_category_full_path(self, app_context, sample_tenant):
        """Test getting full category path."""
        # Create hierarchy: Food > Beverages > Hot Drinks
        food = Category.create(name='Food', tenant_id=sample_tenant.id)
        beverages = Category.create(name='Beverages', parent_id=food.id, tenant_id=sample_tenant.id)
        hot_drinks = Category.create(name='Hot Drinks', parent_id=beverages.id, tenant_id=sample_tenant.id)
        
        assert food.get_full_path() == 'Food'
        assert beverages.get_full_path() == 'Food > Beverages'
        assert hot_drinks.get_full_path() == 'Food > Beverages > Hot Drinks'
    
    def test_category_product_count(self, app_context, sample_tenant, sample_category):
        """Test getting product count for category."""
        # Initially no products
        assert sample_category.get_product_count() == 0
        
        # Add products
        Product.create(
            name='Product 1',
            category_id=sample_category.id,
            tenant_id=sample_tenant.id,
            cost_price=Decimal('10.00'),
            selling_price=Decimal('15.00')
        )
        
        Product.create(
            name='Product 2',
            category_id=sample_category.id,
            tenant_id=sample_tenant.id,
            cost_price=Decimal('20.00'),
            selling_price=Decimal('30.00')
        )
        
        # Add inactive product (should not be counted)
        inactive_product = Product.create(
            name='Inactive Product',
            category_id=sample_category.id,
            tenant_id=sample_tenant.id,
            cost_price=Decimal('5.00'),
            selling_price=Decimal('8.00')
        )
        inactive_product.is_active = False
        inactive_product.save()
        
        assert sample_category.get_product_count() == 2
    
    def test_category_deletion_check(self, app_context, sample_tenant, sample_category):
        """Test category deletion safety check."""
        # Empty category can be deleted
        assert sample_category.can_be_deleted() is True
        
        # Add product - now cannot be deleted
        Product.create(
            name='Test Product',
            category_id=sample_category.id,
            tenant_id=sample_tenant.id,
            cost_price=Decimal('10.00'),
            selling_price=Decimal('15.00')
        )
        
        assert sample_category.can_be_deleted() is False
    
    def test_get_root_categories(self, app_context, sample_tenant):
        """Test getting root categories."""
        # Create root categories
        cat1 = Category.create(name='Category 1', tenant_id=sample_tenant.id, sort_order=2)
        cat2 = Category.create(name='Category 2', tenant_id=sample_tenant.id, sort_order=1)
        
        # Create child category (should not appear in root)
        Category.create(name='Child Category', parent_id=cat1.id, tenant_id=sample_tenant.id)
        
        root_categories = Category.get_root_categories(sample_tenant.id)
        
        assert len(root_categories) == 2
        # Should be sorted by sort_order, then name
        assert root_categories[0] == cat2  # sort_order = 1
        assert root_categories[1] == cat1  # sort_order = 2


class TestProductModel:
    """Test cases for Product model."""
    
    def test_product_creation(self, app_context, sample_tenant, sample_category):
        """Test basic product creation."""
        product = Product.create(
            name='Test Product',
            description='A test product',
            sku='TEST-001',
            barcode='9876543210987',
            category_id=sample_category.id,
            tenant_id=sample_tenant.id,
            cost_price=Decimal('25.50'),
            selling_price=Decimal('39.99'),
            current_stock=100,
            minimum_stock=20,
            maximum_stock=500,
            unit_of_measure='pieces',
            weight=Decimal('0.250'),
            tax_rate=Decimal('0.1000'),  # 10%
            tax_inclusive=True
        )
        
        assert product.id is not None
        assert product.name == 'Test Product'
        assert product.description == 'A test product'
        assert product.sku == 'TEST-001'
        assert product.barcode == '9876543210987'
        assert product.category_id == sample_category.id
        assert product.tenant_id == sample_tenant.id
        assert product.cost_price == Decimal('25.50')
        assert product.selling_price == Decimal('39.99')
        assert product.current_stock == 100
        assert product.minimum_stock == 20
        assert product.maximum_stock == 500
        assert product.unit_of_measure == 'pieces'
        assert product.weight == Decimal('0.250')
        assert product.tax_rate == Decimal('0.1000')
        assert product.tax_inclusive is True
        assert product.is_active is True
        assert product.track_inventory is True
    
    def test_product_profit_calculations(self, app_context, sample_product):
        """Test profit margin and amount calculations."""
        # Cost: $300, Selling: $500
        profit_amount = sample_product.get_profit_amount()
        profit_margin = sample_product.get_profit_margin()
        
        assert profit_amount == Decimal('200.00')
        assert abs(profit_margin - Decimal('66.67')) < Decimal('0.01')  # 66.67%
    
    def test_product_tax_calculations(self, app_context, sample_tenant):
        """Test tax calculations for tax-inclusive and tax-exclusive products."""
        # Tax-exclusive product
        exclusive_product = Product.create(
            name='Tax Exclusive',
            tenant_id=sample_tenant.id,
            selling_price=Decimal('100.00'),
            tax_rate=Decimal('0.1000'),  # 10%
            tax_inclusive=False,
            cost_price=Decimal('50.00')
        )
        
        assert exclusive_product.get_tax_amount() == Decimal('10.00')
        assert exclusive_product.get_price_with_tax() == Decimal('110.00')
        assert exclusive_product.get_price_without_tax() == Decimal('100.00')
        
        # Tax-inclusive product
        inclusive_product = Product.create(
            name='Tax Inclusive',
            tenant_id=sample_tenant.id,
            selling_price=Decimal('110.00'),
            tax_rate=Decimal('0.1000'),  # 10%
            tax_inclusive=True,
            cost_price=Decimal('50.00')
        )
        
        tax_amount = inclusive_product.get_tax_amount()
        assert abs(tax_amount - Decimal('10.00')) < Decimal('0.01')
        assert inclusive_product.get_price_with_tax() == Decimal('110.00')
        price_without_tax = inclusive_product.get_price_without_tax()
        assert abs(price_without_tax - Decimal('100.00')) < Decimal('0.01')
    
    def test_product_stock_checks(self, app_context, sample_product):
        """Test stock level checks."""
        # Current stock: 50, Minimum: 10
        assert sample_product.is_low_stock() is False
        assert sample_product.is_out_of_stock() is False
        assert sample_product.can_sell_quantity(30) is True
        assert sample_product.can_sell_quantity(60) is False
        
        # Reduce stock to minimum level
        sample_product.current_stock = 10
        sample_product.save()
        assert sample_product.is_low_stock() is True
        assert sample_product.is_out_of_stock() is False
        
        # Reduce stock to zero
        sample_product.current_stock = 0
        sample_product.save()
        assert sample_product.is_low_stock() is True
        assert sample_product.is_out_of_stock() is True
        assert sample_product.can_sell_quantity(1) is False
    
    def test_product_stock_adjustments(self, app_context, sample_product):
        """Test stock adjustment methods."""
        initial_stock = sample_product.current_stock  # 50
        
        # Increase stock
        movement1 = sample_product.increase_stock(20, 'restock')
        assert sample_product.current_stock == 70
        assert movement1.quantity == 20
        assert movement1.old_stock == initial_stock
        assert movement1.new_stock == 70
        assert movement1.reason == 'restock'
        
        # Reduce stock
        movement2 = sample_product.reduce_stock(15, 'sale')
        assert sample_product.current_stock == 55
        assert movement2.quantity == -15
        assert movement2.old_stock == 70
        assert movement2.new_stock == 55
        assert movement2.reason == 'sale'
        
        # Manual adjustment
        movement3 = sample_product.adjust_stock(-5, 'damaged_goods')
        assert sample_product.current_stock == 50
        assert movement3.quantity == -5
        assert movement3.reason == 'damaged_goods'
    
    def test_product_stock_value_calculations(self, app_context, sample_product):
        """Test stock value calculations."""
        # Current stock: 50, Cost: $300, Selling: $500
        cost_value = sample_product.get_stock_value()
        retail_value = sample_product.get_retail_stock_value()
        
        assert cost_value == Decimal('15000.00')  # 50 * $300
        assert retail_value == Decimal('25000.00')  # 50 * $500
    
    def test_product_search(self, app_context, sample_tenant, sample_category):
        """Test product search functionality."""
        # Create test products
        Product.create(
            name='iPhone 13',
            sku='APPLE-IP13',
            barcode='1111111111111',
            category_id=sample_category.id,
            tenant_id=sample_tenant.id,
            cost_price=Decimal('600.00'),
            selling_price=Decimal('999.00')
        )
        
        Product.create(
            name='Samsung Galaxy',
            sku='SAMSUNG-GAL',
            barcode='2222222222222',
            category_id=sample_category.id,
            tenant_id=sample_tenant.id,
            cost_price=Decimal('500.00'),
            selling_price=Decimal('799.00')
        )
        
        # Search by name
        results = Product.search_products(sample_tenant.id, 'iPhone')
        assert len(results) == 1
        assert results[0].name == 'iPhone 13'
        
        # Search by SKU
        results = Product.search_products(sample_tenant.id, 'SAMSUNG')
        assert len(results) == 1
        assert results[0].name == 'Samsung Galaxy'
        
        # Search by barcode
        results = Product.search_products(sample_tenant.id, '1111111111111')
        assert len(results) == 1
        assert results[0].name == 'iPhone 13'
        
        # Search with no results
        results = Product.search_products(sample_tenant.id, 'NonExistent')
        assert len(results) == 0
    
    def test_low_stock_products(self, app_context, sample_tenant, sample_category):
        """Test getting low stock products."""
        # Create products with different stock levels
        low_stock1 = Product.create(
            name='Low Stock 1',
            category_id=sample_category.id,
            tenant_id=sample_tenant.id,
            current_stock=5,
            minimum_stock=10,
            cost_price=Decimal('10.00'),
            selling_price=Decimal('15.00')
        )
        
        low_stock2 = Product.create(
            name='Low Stock 2',
            category_id=sample_category.id,
            tenant_id=sample_tenant.id,
            current_stock=2,
            minimum_stock=10,
            cost_price=Decimal('20.00'),
            selling_price=Decimal('30.00')
        )
        
        normal_stock = Product.create(
            name='Normal Stock',
            category_id=sample_category.id,
            tenant_id=sample_tenant.id,
            current_stock=50,
            minimum_stock=10,
            cost_price=Decimal('30.00'),
            selling_price=Decimal('45.00')
        )
        
        low_stock_products = Product.get_low_stock_products(sample_tenant.id)
        
        assert len(low_stock_products) == 2
        # Should be ordered by current stock ascending
        assert low_stock_products[0] == low_stock2  # stock: 2
        assert low_stock_products[1] == low_stock1  # stock: 5
        assert normal_stock not in low_stock_products
    
    def test_out_of_stock_products(self, app_context, sample_tenant, sample_category):
        """Test getting out of stock products."""
        # Create products with different stock levels
        out_of_stock = Product.create(
            name='Out of Stock',
            category_id=sample_category.id,
            tenant_id=sample_tenant.id,
            current_stock=0,
            minimum_stock=5,
            cost_price=Decimal('10.00'),
            selling_price=Decimal('15.00')
        )
        
        in_stock = Product.create(
            name='In Stock',
            category_id=sample_category.id,
            tenant_id=sample_tenant.id,
            current_stock=10,
            minimum_stock=5,
            cost_price=Decimal('20.00'),
            selling_price=Decimal('30.00')
        )
        
        out_of_stock_products = Product.get_out_of_stock_products(sample_tenant.id)
        
        assert len(out_of_stock_products) == 1
        assert out_of_stock_products[0] == out_of_stock
        assert in_stock not in out_of_stock_products
    
    def test_product_validation(self, app_context, sample_tenant):
        """Test product data validation."""
        # Test negative cost price
        with pytest.raises(ValueError, match="Cost price cannot be negative"):
            Product.create(
                name='Invalid Product',
                tenant_id=sample_tenant.id,
                cost_price=Decimal('-10.00'),
                selling_price=Decimal('15.00')
            )
        
        # Test negative selling price
        with pytest.raises(ValueError, match="Selling price cannot be negative"):
            Product.create(
                name='Invalid Product',
                tenant_id=sample_tenant.id,
                cost_price=Decimal('10.00'),
                selling_price=Decimal('-15.00')
            )
        
        # Test invalid tax rate
        with pytest.raises(ValueError, match="Tax rate must be between 0 and 1"):
            Product.create(
                name='Invalid Product',
                tenant_id=sample_tenant.id,
                cost_price=Decimal('10.00'),
                selling_price=Decimal('15.00'),
                tax_rate=Decimal('1.5')  # 150% - invalid
            )


class TestInventoryMovementModel:
    """Test cases for InventoryMovement model."""
    
    def test_inventory_movement_creation(self, app_context, sample_product):
        """Test inventory movement record creation."""
        movement = InventoryMovement.create(
            product_id=sample_product.id,
            tenant_id=sample_product.tenant_id,
            movement_type='sale',
            quantity=-5,
            old_stock=50,
            new_stock=45,
            reason='Customer purchase',
            reference_id='TXN-001'
        )
        
        assert movement.id is not None
        assert movement.product_id == sample_product.id
        assert movement.tenant_id == sample_product.tenant_id
        assert movement.movement_type == 'sale'
        assert movement.quantity == -5
        assert movement.old_stock == 50
        assert movement.new_stock == 45
        assert movement.reason == 'Customer purchase'
        assert movement.reference_id == 'TXN-001'
    
    def test_get_product_history(self, app_context, sample_product):
        """Test getting inventory movement history for a product."""
        # Create several movements
        for i in range(5):
            InventoryMovement.create(
                product_id=sample_product.id,
                tenant_id=sample_product.tenant_id,
                movement_type='adjustment',
                quantity=i + 1,
                old_stock=50 + i,
                new_stock=50 + i + 1,
                reason=f'Adjustment {i + 1}'
            )
        
        history = InventoryMovement.get_product_history(sample_product.id, limit=3)
        
        assert len(history) == 3
        # Should be ordered by created_at desc (most recent first)
        assert history[0].reason == 'Adjustment 5'
        assert history[1].reason == 'Adjustment 4'
        assert history[2].reason == 'Adjustment 3'
    
    def test_get_movements_by_type(self, app_context, sample_product):
        """Test getting movements by type."""
        # Create different types of movements
        InventoryMovement.create(
            product_id=sample_product.id,
            tenant_id=sample_product.tenant_id,
            movement_type='sale',
            quantity=-10,
            old_stock=50,
            new_stock=40
        )
        
        InventoryMovement.create(
            product_id=sample_product.id,
            tenant_id=sample_product.tenant_id,
            movement_type='restock',
            quantity=20,
            old_stock=40,
            new_stock=60
        )
        
        InventoryMovement.create(
            product_id=sample_product.id,
            tenant_id=sample_product.tenant_id,
            movement_type='sale',
            quantity=-5,
            old_stock=60,
            new_stock=55
        )
        
        sale_movements = InventoryMovement.get_movements_by_type(
            sample_product.tenant_id, 'sale'
        )
        
        assert len(sale_movements) == 2
        for movement in sale_movements:
            assert movement.movement_type == 'sale'
        
        restock_movements = InventoryMovement.get_movements_by_type(
            sample_product.tenant_id, 'restock'
        )
        
        assert len(restock_movements) == 1
        assert restock_movements[0].movement_type == 'restock'