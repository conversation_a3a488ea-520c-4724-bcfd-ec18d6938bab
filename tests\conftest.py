"""Shared test fixtures for SaaS POS System tests."""

import pytest
from app import create_app, db
from config import TestingConfig


@pytest.fixture
def app():
    """Create test application."""
    app = create_app(TestingConfig)
    
    with app.app_context():
        db.create_all()
        yield app
        db.session.remove()
        db.drop_all()


@pytest.fixture
def client(app):
    """Create test client."""
    return app.test_client()


@pytest.fixture
def app_context(app):
    """Create application context."""
    with app.app_context():
        yield app


@pytest.fixture
def request_context(app):
    """Create request context for session testing."""
    with app.test_request_context():
        yield app