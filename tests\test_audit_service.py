"""Tests for audit service functionality."""

import json
from datetime import datetime, timedelta
import pytest
from unittest.mock import patch, MagicMock
from flask import request
from flask_login import login_user
from app import create_app, db
from app.services.audit_service import AuditService
from app.models.audit import AuditLog, AuditAction, AuditSeverity
from app.models.user import User, Tenant
from config import TestingConfig


@pytest.fixture
def app():
    """Create test application."""
    app = create_app(TestingConfig)
    with app.app_context():
        db.create_all()
        yield app
        db.drop_all()


@pytest.fixture
def client(app):
    """Create test client."""
    return app.test_client()


@pytest.fixture
def sample_tenant(app):
    """Create sample tenant for testing."""
    with app.app_context():
        tenant = Tenant(
            name='Test Business',
            business_type='retail',
            email='<EMAIL>'
        )
        tenant.save()
        return tenant


@pytest.fixture
def sample_user(app, sample_tenant):
    """Create sample user for testing."""
    with app.app_context():
        user = User.create_user(
            email='<EMAIL>',
            password='password123',
            first_name='Test',
            last_name='User',
            tenant_id=sample_tenant.id
        )
        return user


class TestAuditService:
    """Test cases for AuditService."""
    
    def test_basic_audit_log_creation(self, app, sample_tenant, sample_user):
        """Test basic audit log creation."""
        with app.app_context():
            audit_log = AuditService.log(
                action=AuditAction.USER_CREATED,
                description='Test user created',
                user_id=sample_user.id,
                tenant_id=sample_tenant.id
            )
            
            assert audit_log is not None
            assert audit_log.action == AuditAction.USER_CREATED
            assert audit_log.description == 'Test user created'
            assert audit_log.user_id == sample_user.id
            assert audit_log.tenant_id == sample_tenant.id
            assert audit_log.severity == AuditSeverity.LOW
            assert audit_log.success is True
    
    def test_audit_log_with_request_context(self, app, sample_tenant, sample_user):
        """Test audit log creation with request context."""
        with app.test_request_context('/', headers={'User-Agent': 'Test Browser'}):
            audit_log = AuditService.log(
                action=AuditAction.LOGIN,
                description='User login',
                user_id=sample_user.id,
                tenant_id=sample_tenant.id
            )
            
            assert audit_log.user_agent == 'Test Browser'
            assert audit_log.request_method == 'GET'
            assert audit_log.request_url is not None
    
    def test_get_client_ip_with_proxy(self, app):
        """Test client IP detection with proxy headers."""
        with app.test_request_context('/', headers={'X-Forwarded-For': '***********, ********'}):
            ip = AuditService._get_client_ip()
            assert ip == '***********'
        
        with app.test_request_context('/', headers={'X-Real-IP': '***********'}):
            ip = AuditService._get_client_ip()
            assert ip == '***********'
    
    def test_log_authentication_success(self, app, sample_tenant, sample_user):
        """Test logging successful authentication."""
        with app.app_context():
            audit_log = AuditService.log_authentication(
                action=AuditAction.LOGIN,
                user_email=sample_user.email,
                success=True,
                user_id=sample_user.id,
                tenant_id=sample_tenant.id
            )
            
            assert audit_log.action == AuditAction.LOGIN
            assert audit_log.success is True
            assert audit_log.severity == AuditSeverity.MEDIUM
            assert sample_user.email in audit_log.description
    
    def test_log_authentication_failure(self, app, sample_tenant):
        """Test logging failed authentication."""
        with app.app_context():
            audit_log = AuditService.log_authentication(
                action=AuditAction.LOGIN_FAILED,
                user_email='<EMAIL>',
                success=False,
                error_message='Invalid credentials',
                tenant_id=sample_tenant.id
            )
            
            assert audit_log.action == AuditAction.LOGIN_FAILED
            assert audit_log.success is False
            assert audit_log.severity == AuditSeverity.HIGH
            assert audit_log.error_message == 'Invalid credentials'
    
    def test_log_data_change(self, app, sample_tenant, sample_user):
        """Test logging data changes."""
        with app.app_context():
            old_values = {'name': 'Old Product'}
            new_values = {'name': 'New Product', 'price': 19.99}
            
            audit_log = AuditService.log_data_change(
                action=AuditAction.PRODUCT_UPDATED,
                resource_type='product',
                resource_id=123,
                description='Product updated',
                old_values=old_values,
                new_values=new_values,
                user_id=sample_user.id,
                tenant_id=sample_tenant.id
            )
            
            assert audit_log.resource_type == 'product'
            assert audit_log.resource_id == '123'
            assert audit_log.old_values == old_values
            assert audit_log.new_values == new_values
    
    def test_log_transaction(self, app, sample_tenant, sample_user):
        """Test logging transaction events."""
        with app.app_context():
            audit_log = AuditService.log_transaction(
                action=AuditAction.TRANSACTION_CREATED,
                transaction_id=456,
                amount=99.99,
                user_id=sample_user.id,
                tenant_id=sample_tenant.id
            )
            
            assert audit_log.resource_type == 'transaction'
            assert audit_log.resource_id == '456'
            assert audit_log.new_values['amount'] == 99.99
            assert '$99.99' in audit_log.description
    
    def test_log_security_event(self, app, sample_tenant):
        """Test logging security events."""
        with app.app_context():
            audit_log = AuditService.log_security_event(
                action=AuditAction.UNAUTHORIZED_ACCESS,
                description='Unauthorized access attempt',
                severity=AuditSeverity.CRITICAL,
                tenant_id=sample_tenant.id,
                additional_data={'ip': '***********00', 'attempts': 5}
            )
            
            assert audit_log.action == AuditAction.UNAUTHORIZED_ACCESS
            assert audit_log.severity == AuditSeverity.CRITICAL
            assert audit_log.success is False
            assert audit_log.new_values['ip'] == '***********00'
    
    def test_log_system_event(self, app):
        """Test logging system events."""
        with app.app_context():
            audit_log = AuditService.log_system_event(
                action=AuditAction.BACKUP_CREATED,
                description='System backup created',
                additional_data={'backup_size': '100MB'}
            )
            
            assert audit_log.tenant_id == 0  # System-wide
            assert audit_log.new_values['backup_size'] == '100MB'
    
    def test_get_audit_logs_filtering(self, app, sample_tenant, sample_user):
        """Test getting audit logs with filtering."""
        with app.app_context():
            # Create test audit logs
            AuditService.log(
                action=AuditAction.LOGIN,
                description='Login event',
                user_id=sample_user.id,
                tenant_id=sample_tenant.id,
                severity=AuditSeverity.MEDIUM
            )
            
            AuditService.log(
                action=AuditAction.PRODUCT_CREATED,
                description='Product created',
                user_id=sample_user.id,
                tenant_id=sample_tenant.id,
                severity=AuditSeverity.LOW
            )
            
            # Test filtering by action
            login_logs = AuditService.get_audit_logs(
                tenant_id=sample_tenant.id,
                action=AuditAction.LOGIN
            )
            assert len(login_logs) == 1
            assert login_logs[0].action == AuditAction.LOGIN
            
            # Test filtering by severity
            medium_logs = AuditService.get_audit_logs(
                tenant_id=sample_tenant.id,
                severity=AuditSeverity.MEDIUM
            )
            assert len(medium_logs) == 1
            assert medium_logs[0].severity == AuditSeverity.MEDIUM
            
            # Test filtering by user
            user_logs = AuditService.get_audit_logs(
                tenant_id=sample_tenant.id,
                user_id=sample_user.id
            )
            assert len(user_logs) == 2
    
    def test_get_security_events(self, app, sample_tenant):
        """Test getting security events."""
        with app.app_context():
            # Create security events
            AuditService.log_security_event(
                action=AuditAction.LOGIN_FAILED,
                description='Failed login',
                tenant_id=sample_tenant.id
            )
            
            AuditService.log_security_event(
                action=AuditAction.UNAUTHORIZED_ACCESS,
                description='Unauthorized access',
                tenant_id=sample_tenant.id
            )
            
            # Create non-security event
            AuditService.log(
                action=AuditAction.PRODUCT_CREATED,
                description='Product created',
                tenant_id=sample_tenant.id
            )
            
            security_events = AuditService.get_security_events(tenant_id=sample_tenant.id)
            assert len(security_events) == 2
            
            actions = [event.action for event in security_events]
            assert AuditAction.LOGIN_FAILED in actions
            assert AuditAction.UNAUTHORIZED_ACCESS in actions
            assert AuditAction.PRODUCT_CREATED not in actions
    
    def test_get_user_activity(self, app, sample_tenant, sample_user):
        """Test getting user activity."""
        with app.app_context():
            # Create user activities
            for i in range(3):
                AuditService.log(
                    action=AuditAction.PRODUCT_CREATED,
                    description=f'Product {i} created',
                    user_id=sample_user.id,
                    tenant_id=sample_tenant.id
                )
            
            # Create activity for different user
            other_user = User.create_user(
                email='<EMAIL>',
                password='password123',
                first_name='Other',
                last_name='User',
                tenant_id=sample_tenant.id
            )
            
            AuditService.log(
                action=AuditAction.PRODUCT_UPDATED,
                description='Product updated by other user',
                user_id=other_user.id,
                tenant_id=sample_tenant.id
            )
            
            user_activity = AuditService.get_user_activity(
                user_id=sample_user.id,
                tenant_id=sample_tenant.id
            )
            
            assert len(user_activity) == 3
            for activity in user_activity:
                assert activity.user_id == sample_user.id
    
    def test_get_audit_summary(self, app, sample_tenant, sample_user):
        """Test getting audit summary."""
        with app.app_context():
            # Create various audit logs
            AuditService.log(
                action=AuditAction.LOGIN,
                description='Login',
                user_id=sample_user.id,
                tenant_id=sample_tenant.id,
                severity=AuditSeverity.MEDIUM
            )
            
            AuditService.log(
                action=AuditAction.LOGIN_FAILED,
                description='Failed login',
                tenant_id=sample_tenant.id,
                severity=AuditSeverity.HIGH,
                success=False
            )
            
            AuditService.log(
                action=AuditAction.PRODUCT_CREATED,
                description='Product created',
                user_id=sample_user.id,
                tenant_id=sample_tenant.id,
                severity=AuditSeverity.LOW
            )
            
            summary = AuditService.get_audit_summary(tenant_id=sample_tenant.id)
            
            assert summary['total_events'] == 3
            assert summary['failed_events'] == 1
            assert summary['success_rate'] == 66.67
            assert summary['unique_users'] == 1
            
            # Check severity counts
            assert summary['events_by_severity']['low'] == 1
            assert summary['events_by_severity']['medium'] == 1
            assert summary['events_by_severity']['high'] == 1
            
            # Check action counts
            assert summary['events_by_action']['login'] == 1
            assert summary['events_by_action']['login_failed'] == 1
            assert summary['events_by_action']['product_created'] == 1
    
    def test_detect_suspicious_activity(self, app, sample_tenant, sample_user):
        """Test suspicious activity detection."""
        with app.app_context():
            # Create multiple failed logins from same IP
            for i in range(6):
                with app.test_request_context('/', environ_base={'REMOTE_ADDR': '***********00'}):
                    AuditService.log_authentication(
                        action=AuditAction.LOGIN_FAILED,
                        user_email=f'user{i}@example.com',
                        success=False,
                        tenant_id=sample_tenant.id
                    )
            
            # Create high volume transactions
            for i in range(101):
                AuditService.log_transaction(
                    action=AuditAction.TRANSACTION_CREATED,
                    transaction_id=i,
                    amount=10.0,
                    user_id=sample_user.id,
                    tenant_id=sample_tenant.id
                )
            
            suspicious_patterns = AuditService.detect_suspicious_activity(
                tenant_id=sample_tenant.id
            )
            
            # Should detect multiple failed logins and high volume transactions
            pattern_types = [pattern['type'] for pattern in suspicious_patterns]
            assert 'multiple_failed_logins' in pattern_types
            assert 'high_volume_transactions' in pattern_types
    
    def test_cleanup_old_audit_logs(self, app, sample_tenant):
        """Test cleanup of old audit logs."""
        with app.app_context():
            # Create old audit log
            old_log = AuditLog(
                action=AuditAction.LOGIN,
                description='Old login',
                tenant_id=sample_tenant.id,
                created_at=datetime.utcnow() - timedelta(days=100)
            )
            old_log.save()
            
            # Create recent audit log
            recent_log = AuditLog(
                action=AuditAction.LOGIN,
                description='Recent login',
                tenant_id=sample_tenant.id
            )
            recent_log.save()
            
            deleted_count = AuditService.cleanup_old_audit_logs(retention_days=90)
            
            assert deleted_count >= 1
            assert AuditLog.query.get(old_log.id) is None
            assert AuditLog.query.get(recent_log.id) is not None
    
    def test_export_audit_logs(self, app, sample_tenant, sample_user):
        """Test exporting audit logs."""
        with app.app_context():
            # Create test audit logs
            start_date = datetime.utcnow() - timedelta(days=1)
            end_date = datetime.utcnow()
            
            AuditService.log(
                action=AuditAction.LOGIN,
                description='Login event',
                user_id=sample_user.id,
                tenant_id=sample_tenant.id
            )
            
            AuditService.log(
                action=AuditAction.PRODUCT_CREATED,
                description='Product created',
                user_id=sample_user.id,
                tenant_id=sample_tenant.id
            )
            
            json_export = AuditService.export_audit_logs(
                tenant_id=sample_tenant.id,
                start_date=start_date,
                end_date=end_date,
                format='json'
            )
            
            export_data = json.loads(json_export)
            
            assert 'export_info' in export_data
            assert 'audit_logs' in export_data
            assert export_data['export_info']['tenant_id'] == sample_tenant.id
            assert export_data['export_info']['total_records'] == 2
            assert len(export_data['audit_logs']) == 2
            
            # Check that export action was logged
            export_logs = AuditLog.query.filter_by(action=AuditAction.DATA_EXPORTED).all()
            assert len(export_logs) >= 1


class TestAuditLog:
    """Test cases for AuditLog model."""
    
    def test_audit_log_creation(self, app, sample_tenant, sample_user):
        """Test audit log creation."""
        with app.app_context():
            audit_log = AuditLog.log_action(
                action=AuditAction.USER_CREATED,
                tenant_id=sample_tenant.id,
                description='Test audit log',
                user_id=sample_user.id,
                severity=AuditSeverity.MEDIUM,
                ip_address='***********',
                success=True
            )
            
            assert audit_log.id is not None
            assert audit_log.action == AuditAction.USER_CREATED
            assert audit_log.tenant_id == sample_tenant.id
            assert audit_log.user_id == sample_user.id
            assert audit_log.severity == AuditSeverity.MEDIUM
            assert audit_log.ip_address == '***********'
            assert audit_log.success is True
    
    def test_get_logs_for_tenant(self, app, sample_tenant, sample_user):
        """Test getting logs for specific tenant."""
        with app.app_context():
            # Create logs for the tenant
            for i in range(5):
                AuditLog.log_action(
                    action=AuditAction.PRODUCT_CREATED,
                    tenant_id=sample_tenant.id,
                    description=f'Product {i} created',
                    user_id=sample_user.id
                )
            
            # Create logs for different tenant
            other_tenant = Tenant(name='Other Business', business_type='retail')
            other_tenant.save()
            
            AuditLog.log_action(
                action=AuditAction.PRODUCT_CREATED,
                tenant_id=other_tenant.id,
                description='Product created for other tenant'
            )
            
            tenant_logs = AuditLog.get_logs_for_tenant(tenant_id=sample_tenant.id)
            
            assert len(tenant_logs) == 5
            for log in tenant_logs:
                assert log.tenant_id == sample_tenant.id
    
    def test_get_security_events(self, app, sample_tenant):
        """Test getting security events."""
        with app.app_context():
            # Create security events
            security_actions = [
                AuditAction.LOGIN_FAILED,
                AuditAction.UNAUTHORIZED_ACCESS,
                AuditAction.SUSPICIOUS_ACTIVITY
            ]
            
            for action in security_actions:
                AuditLog.log_action(
                    action=action,
                    tenant_id=sample_tenant.id,
                    description=f'{action.value} event'
                )
            
            # Create non-security event
            AuditLog.log_action(
                action=AuditAction.PRODUCT_CREATED,
                tenant_id=sample_tenant.id,
                description='Product created'
            )
            
            security_events = AuditLog.get_security_events(tenant_id=sample_tenant.id)
            
            assert len(security_events) == 3
            for event in security_events:
                assert event.action in security_actions
    
    def test_cleanup_old_logs(self, app, sample_tenant):
        """Test cleanup of old audit logs."""
        with app.app_context():
            # Create old logs
            old_date = datetime.utcnow() - timedelta(days=100)
            
            for i in range(10):
                old_log = AuditLog(
                    action=AuditAction.LOGIN,
                    tenant_id=sample_tenant.id,
                    description=f'Old log {i}',
                    created_at=old_date
                )
                old_log.save()
            
            # Create recent logs
            for i in range(5):
                recent_log = AuditLog(
                    action=AuditAction.LOGIN,
                    tenant_id=sample_tenant.id,
                    description=f'Recent log {i}'
                )
                recent_log.save()
            
            deleted_count = AuditLog.cleanup_old_logs(retention_days=90)
            
            assert deleted_count == 10
            
            # Check that only recent logs remain
            remaining_logs = AuditLog.query.filter_by(tenant_id=sample_tenant.id).all()
            assert len(remaining_logs) == 5
            
            for log in remaining_logs:
                assert log.created_at > datetime.utcnow() - timedelta(days=90)