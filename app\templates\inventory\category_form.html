{% extends "base.html" %}

{% block title %}{% if category %}Edit Category{% else %}Add Category{% endif %} - SaaS POS System{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <!-- Header -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-3xl font-bold text-gray-900">
                {% if category %}Edit Category{% else %}Add Category{% endif %}
            </h1>
            <p class="text-gray-600 mt-1">
                {% if category %}Update category information{% else %}Create a new product category{% endif %}
            </p>
        </div>
        <div class="flex space-x-4">
            <a href="{{ url_for('inventory.categories') }}" 
               class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg font-medium">
                Back to Categories
            </a>
        </div>
    </div>

    <!-- Flash Messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category_msg, message in messages %}
                <div class="mb-4 p-4 rounded-lg {% if category_msg == 'error' %}bg-red-100 text-red-700 border border-red-300{% else %}bg-green-100 text-green-700 border border-green-300{% endif %}">
                    {{ message }}
                </div>
            {% endfor %}
        {% endif %}
    {% endwith %}

    <!-- Category Form -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div class="lg:col-span-2">
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">Category Information</h3>
                
                <form method="POST" class="space-y-6">
                    <!-- Category Name -->
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 mb-1">
                            Category Name <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="name" id="name" required
                               value="{{ category.name if category else '' }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                               placeholder="Enter category name">
                    </div>

                    <!-- Description -->
                    <div>
                        <label for="description" class="block text-sm font-medium text-gray-700 mb-1">
                            Description
                        </label>
                        <textarea name="description" id="description" rows="3"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                  placeholder="Enter category description">{{ category.description if category else '' }}</textarea>
                    </div>

                    <!-- Parent Category -->
                    <div>
                        <label for="parent_id" class="block text-sm font-medium text-gray-700 mb-1">
                            Parent Category
                        </label>
                        <select name="parent_id" id="parent_id"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="">No Parent (Root Category)</option>
                            {% for cat in categories %}
                                {% if not category or cat.id != category.id %}
                                    <option value="{{ cat.id }}" 
                                            {% if category and category.parent_id == cat.id %}selected{% endif %}>
                                        {{ cat.name }}
                                    </option>
                                {% endif %}
                            {% endfor %}
                        </select>
                        <p class="mt-1 text-sm text-gray-500">Select a parent category to create a subcategory</p>
                    </div>

                    <!-- Icon -->
                    <div>
                        <label for="icon" class="block text-sm font-medium text-gray-700 mb-1">
                            Icon (Emoji)
                        </label>
                        <input type="text" name="icon" id="icon" maxlength="2"
                               value="{{ category.icon if category else '' }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                               placeholder="🏷️">
                        <p class="mt-1 text-sm text-gray-500">Optional emoji icon for the category</p>
                    </div>

                    <!-- Color -->
                    <div>
                        <label for="color" class="block text-sm font-medium text-gray-700 mb-1">
                            Color
                        </label>
                        <div class="flex space-x-2">
                            <input type="text" name="color" id="color"
                                   value="{{ category.color if category else '' }}"
                                   class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                   placeholder="#3B82F6 or blue">
                            <input type="color" id="color-picker"
                                   value="{{ category.color if category and category.color.startswith('#') else '#3B82F6' }}"
                                   class="w-12 h-10 border border-gray-300 rounded cursor-pointer">
                        </div>
                        <p class="mt-1 text-sm text-gray-500">Color for category identification (hex code or color name)</p>
                    </div>

                    <!-- Sort Order -->
                    <div>
                        <label for="sort_order" class="block text-sm font-medium text-gray-700 mb-1">
                            Sort Order
                        </label>
                        <input type="number" name="sort_order" id="sort_order" min="0"
                               value="{{ category.sort_order if category else '0' }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <p class="mt-1 text-sm text-gray-500">Lower numbers appear first in lists</p>
                    </div>

                    <!-- Active Status -->
                    <div class="flex items-center">
                        <input type="checkbox" name="is_active" id="is_active"
                               {% if not category or category.is_active %}checked{% endif %}
                               class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                        <label for="is_active" class="ml-2 text-sm text-gray-700">
                            Active category
                        </label>
                    </div>

                    <!-- Form Actions -->
                    <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                        <a href="{{ url_for('inventory.categories') }}" 
                           class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-6 py-2 rounded-lg font-medium">
                            Cancel
                        </a>
                        <button type="submit" 
                                class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium">
                            {% if category %}Update Category{% else %}Create Category{% endif %}
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Preview -->
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Preview</h3>
                
                <div id="category-preview" class="border border-gray-200 rounded-lg p-4">
                    <div class="flex items-center space-x-3">
                        <div id="preview-icon" class="text-2xl">
                            {% if category and category.icon %}{{ category.icon }}{% else %}🏷️{% endif %}
                        </div>
                        <div class="flex-1">
                            <div id="preview-name" class="font-medium text-gray-900">
                                {% if category %}{{ category.name }}{% else %}Category Name{% endif %}
                            </div>
                            <div id="preview-description" class="text-sm text-gray-500">
                                {% if category and category.description %}{{ category.description }}{% else %}Category description will appear here{% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div id="preview-parent" class="mt-3 text-xs text-gray-400">
                        {% if category and category.parent_id %}
                            {% for cat in categories %}
                                {% if cat.id == category.parent_id %}
                                    Parent: {{ cat.name }}
                                {% endif %}
                            {% endfor %}
                        {% else %}
                            Root Category
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Common Icons -->
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Common Icons</h3>
                <div class="grid grid-cols-4 gap-2">
                    <button type="button" onclick="setIcon('🏷️')" class="p-2 text-2xl hover:bg-gray-100 rounded">🏷️</button>
                    <button type="button" onclick="setIcon('📱')" class="p-2 text-2xl hover:bg-gray-100 rounded">📱</button>
                    <button type="button" onclick="setIcon('👕')" class="p-2 text-2xl hover:bg-gray-100 rounded">👕</button>
                    <button type="button" onclick="setIcon('🍔')" class="p-2 text-2xl hover:bg-gray-100 rounded">🍔</button>
                    <button type="button" onclick="setIcon('📚')" class="p-2 text-2xl hover:bg-gray-100 rounded">📚</button>
                    <button type="button" onclick="setIcon('🏠')" class="p-2 text-2xl hover:bg-gray-100 rounded">🏠</button>
                    <button type="button" onclick="setIcon('🚗')" class="p-2 text-2xl hover:bg-gray-100 rounded">🚗</button>
                    <button type="button" onclick="setIcon('💊')" class="p-2 text-2xl hover:bg-gray-100 rounded">💊</button>
                    <button type="button" onclick="setIcon('🎮')" class="p-2 text-2xl hover:bg-gray-100 rounded">🎮</button>
                    <button type="button" onclick="setIcon('🎵')" class="p-2 text-2xl hover:bg-gray-100 rounded">🎵</button>
                    <button type="button" onclick="setIcon('⚽')" class="p-2 text-2xl hover:bg-gray-100 rounded">⚽</button>
                    <button type="button" onclick="setIcon('🌱')" class="p-2 text-2xl hover:bg-gray-100 rounded">🌱</button>
                </div>
            </div>

            <!-- Color Presets -->
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Color Presets</h3>
                <div class="grid grid-cols-4 gap-2">
                    <button type="button" onclick="setColor('#3B82F6')" class="w-8 h-8 rounded bg-blue-500 hover:ring-2 hover:ring-blue-300"></button>
                    <button type="button" onclick="setColor('#10B981')" class="w-8 h-8 rounded bg-green-500 hover:ring-2 hover:ring-green-300"></button>
                    <button type="button" onclick="setColor('#F59E0B')" class="w-8 h-8 rounded bg-yellow-500 hover:ring-2 hover:ring-yellow-300"></button>
                    <button type="button" onclick="setColor('#EF4444')" class="w-8 h-8 rounded bg-red-500 hover:ring-2 hover:ring-red-300"></button>
                    <button type="button" onclick="setColor('#8B5CF6')" class="w-8 h-8 rounded bg-purple-500 hover:ring-2 hover:ring-purple-300"></button>
                    <button type="button" onclick="setColor('#06B6D4')" class="w-8 h-8 rounded bg-cyan-500 hover:ring-2 hover:ring-cyan-300"></button>
                    <button type="button" onclick="setColor('#F97316')" class="w-8 h-8 rounded bg-orange-500 hover:ring-2 hover:ring-orange-300"></button>
                    <button type="button" onclick="setColor('#84CC16')" class="w-8 h-8 rounded bg-lime-500 hover:ring-2 hover:ring-lime-300"></button>
                </div>
            </div>

            {% if category %}
            <!-- Category Stats -->
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Category Stats</h3>
                <div class="space-y-3">
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600">Products:</span>
                        <span class="text-sm font-medium text-gray-900">{{ category.products|length if category.products else 0 }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600">Created:</span>
                        <span class="text-sm font-medium text-gray-900">{{ category.created_at.strftime('%Y-%m-%d') }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600">Updated:</span>
                        <span class="text-sm font-medium text-gray-900">{{ category.updated_at.strftime('%Y-%m-%d') }}</span>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const nameInput = document.getElementById('name');
    const descriptionInput = document.getElementById('description');
    const iconInput = document.getElementById('icon');
    const colorInput = document.getElementById('color');
    const colorPicker = document.getElementById('color-picker');
    const parentSelect = document.getElementById('parent_id');
    
    const previewName = document.getElementById('preview-name');
    const previewDescription = document.getElementById('preview-description');
    const previewIcon = document.getElementById('preview-icon');
    const previewParent = document.getElementById('preview-parent');

    function updatePreview() {
        previewName.textContent = nameInput.value || 'Category Name';
        previewDescription.textContent = descriptionInput.value || 'Category description will appear here';
        previewIcon.textContent = iconInput.value || '🏷️';
        
        const selectedParent = parentSelect.options[parentSelect.selectedIndex];
        if (selectedParent.value) {
            previewParent.textContent = `Parent: ${selectedParent.text}`;
        } else {
            previewParent.textContent = 'Root Category';
        }
    }

    // Update preview on input changes
    nameInput.addEventListener('input', updatePreview);
    descriptionInput.addEventListener('input', updatePreview);
    iconInput.addEventListener('input', updatePreview);
    parentSelect.addEventListener('change', updatePreview);

    // Color picker synchronization
    colorPicker.addEventListener('input', function() {
        colorInput.value = this.value;
    });

    colorInput.addEventListener('input', function() {
        if (this.value.startsWith('#')) {
            colorPicker.value = this.value;
        }
    });
});

function setIcon(icon) {
    document.getElementById('icon').value = icon;
    document.getElementById('preview-icon').textContent = icon;
}

function setColor(color) {
    document.getElementById('color').value = color;
    document.getElementById('color-picker').value = color;
}
</script>
{% endblock %}