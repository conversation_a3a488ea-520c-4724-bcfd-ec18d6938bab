"""Caching utilities for SaaS POS System."""

import functools
import json
import hashlib
from datetime import datetime, timedelta
from flask import current_app, g
from app import cache, redis_client


def cache_key(prefix, *args, **kwargs):
    """Generate cache key from prefix and arguments."""
    key_parts = [prefix]
    key_parts.extend(str(arg) for arg in args)
    key_parts.extend(f"{k}:{v}" for k, v in sorted(kwargs.items()))
    key = ":".join(key_parts)
    
    # Hash long keys to avoid Redis key length limits
    if len(key) > 250:
        key_hash = hashlib.md5(key.encode()).hexdigest()
        return f"{prefix}:hash:{key_hash}"
    
    return key


def cached(timeout=300, key_prefix=None, tenant_specific=True):
    """Decorator to cache function results with tenant isolation."""
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            if not current_app.config.get('CACHE_TYPE') or current_app.config['CACHE_TYPE'] == 'null':
                return func(*args, **kwargs)
            
            # Generate cache key
            prefix = key_prefix or f"{func.__module__}.{func.__name__}"
            
            # Add tenant isolation if enabled and tenant_id is available
            if tenant_specific and hasattr(g, 'current_tenant_id'):
                prefix = f"tenant:{g.current_tenant_id}:{prefix}"
            
            key = cache_key(prefix, *args, **kwargs)
            
            # Try to get from cache
            result = cache.get(key)
            if result is not None:
                return result
            
            # Execute function and cache result
            result = func(*args, **kwargs)
            cache.set(key, result, timeout=timeout)
            return result
        
        return wrapper
    return decorator


def cache_product_data(tenant_id, product_id, data, timeout=600):
    """Cache product data with tenant isolation."""
    key = f"tenant:{tenant_id}:product:{product_id}"
    cache.set(key, data, timeout=timeout)


def get_cached_product_data(tenant_id, product_id):
    """Get cached product data."""
    key = f"tenant:{tenant_id}:product:{product_id}"
    return cache.get(key)


def cache_inventory_summary(tenant_id, summary_data, timeout=300):
    """Cache inventory summary data."""
    key = f"tenant:{tenant_id}:inventory:summary"
    cache.set(key, summary_data, timeout=timeout)


def get_cached_inventory_summary(tenant_id):
    """Get cached inventory summary."""
    key = f"tenant:{tenant_id}:inventory:summary"
    return cache.get(key)


def cache_sales_metrics(tenant_id, date_range, metrics, timeout=900):
    """Cache sales metrics for dashboard."""
    date_key = f"{date_range['start']}_{date_range['end']}"
    key = f"tenant:{tenant_id}:metrics:sales:{date_key}"
    cache.set(key, metrics, timeout=timeout)


def get_cached_sales_metrics(tenant_id, date_range):
    """Get cached sales metrics."""
    date_key = f"{date_range['start']}_{date_range['end']}"
    key = f"tenant:{tenant_id}:metrics:sales:{date_key}"
    return cache.get(key)


def cache_report_data(tenant_id, report_type, filters, data, timeout=1800):
    """Cache report data with filter-specific keys."""
    filter_hash = hashlib.md5(json.dumps(filters, sort_keys=True).encode()).hexdigest()
    key = f"tenant:{tenant_id}:report:{report_type}:{filter_hash}"
    cache.set(key, data, timeout=timeout)


def get_cached_report_data(tenant_id, report_type, filters):
    """Get cached report data."""
    filter_hash = hashlib.md5(json.dumps(filters, sort_keys=True).encode()).hexdigest()
    key = f"tenant:{tenant_id}:report:{report_type}:{filter_hash}"
    return cache.get(key)


def invalidate_cache_pattern(pattern):
    """Invalidate all cache keys matching pattern."""
    if not redis_client:
        return
    
    try:
        keys = redis_client.keys(pattern)
        if keys:
            redis_client.delete(*keys)
            current_app.logger.info(f"Invalidated {len(keys)} cache keys matching pattern: {pattern}")
    except Exception as e:
        current_app.logger.warning(f"Cache invalidation failed for pattern {pattern}: {e}")


def tenant_cache_key(tenant_id, key):
    """Generate tenant-specific cache key."""
    return f"tenant:{tenant_id}:{key}"


def invalidate_tenant_cache(tenant_id):
    """Invalidate all cache for a specific tenant."""
    pattern = f"tenant:{tenant_id}:*"
    invalidate_cache_pattern(pattern)


def invalidate_product_cache(tenant_id, product_id=None):
    """Invalidate product-related cache entries."""
    if product_id:
        # Invalidate specific product
        pattern = f"tenant:{tenant_id}:product:{product_id}*"
    else:
        # Invalidate all products for tenant
        pattern = f"tenant:{tenant_id}:product:*"
    
    invalidate_cache_pattern(pattern)
    
    # Also invalidate inventory summary as it depends on product data
    cache.delete(f"tenant:{tenant_id}:inventory:summary")


def invalidate_inventory_cache(tenant_id):
    """Invalidate inventory-related cache entries."""
    patterns = [
        f"tenant:{tenant_id}:inventory:*",
        f"tenant:{tenant_id}:product:*",
        f"tenant:{tenant_id}:metrics:*"
    ]
    
    for pattern in patterns:
        invalidate_cache_pattern(pattern)


def invalidate_sales_cache(tenant_id):
    """Invalidate sales and metrics cache entries."""
    patterns = [
        f"tenant:{tenant_id}:metrics:*",
        f"tenant:{tenant_id}:report:*"
    ]
    
    for pattern in patterns:
        invalidate_cache_pattern(pattern)


def warm_cache_for_tenant(tenant_id):
    """Pre-warm cache with frequently accessed data for a tenant."""
    try:
        # Import here to avoid circular imports
        from app.services.inventory_service import InventoryService
        from app.services.report_service import ReportService
        
        inventory_service = InventoryService()
        report_service = ReportService()
        
        # Warm up inventory summary
        inventory_service.get_inventory_summary(tenant_id)
        
        # Warm up today's sales metrics
        today = datetime.now().date()
        date_range = {'start': today, 'end': today}
        report_service.get_sales_metrics(tenant_id, date_range)
        
        current_app.logger.info(f"Cache warmed for tenant {tenant_id}")
        
    except Exception as e:
        current_app.logger.warning(f"Cache warming failed for tenant {tenant_id}: {e}")


def get_cache_stats():
    """Get cache statistics for monitoring."""
    if not redis_client:
        return None
    
    try:
        info = redis_client.info()
        return {
            'connected_clients': info.get('connected_clients', 0),
            'used_memory': info.get('used_memory_human', '0B'),
            'keyspace_hits': info.get('keyspace_hits', 0),
            'keyspace_misses': info.get('keyspace_misses', 0),
            'hit_rate': (
                info.get('keyspace_hits', 0) / 
                max(info.get('keyspace_hits', 0) + info.get('keyspace_misses', 0), 1)
            ) * 100
        }
    except Exception as e:
        current_app.logger.warning(f"Failed to get cache stats: {e}")
        return None


def cache_with_lock(key, func, timeout=300, lock_timeout=30):
    """Cache function result with distributed locking to prevent cache stampede."""
    if not redis_client:
        return func()
    
    # Check if value is already cached
    cached_value = cache.get(key)
    if cached_value is not None:
        return cached_value
    
    # Try to acquire lock
    lock_key = f"lock:{key}"
    lock_acquired = redis_client.set(lock_key, "1", nx=True, ex=lock_timeout)
    
    if lock_acquired:
        try:
            # We got the lock, compute and cache the value
            result = func()
            cache.set(key, result, timeout=timeout)
            return result
        finally:
            # Release the lock
            redis_client.delete(lock_key)
    else:
        # Someone else is computing, wait a bit and try cache again
        import time
        time.sleep(0.1)
        cached_value = cache.get(key)
        if cached_value is not None:
            return cached_value
        
        # If still not cached, compute without caching to avoid blocking
        return func()