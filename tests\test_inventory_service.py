"""Unit tests for InventoryService."""

import pytest
from datetime import datetime, timedelta
from decimal import Decimal
from unittest.mock import patch, MagicMock

from app import db
from app.models.user import User, Tenant
from app.models.product import Product, Category, InventoryMovement
from app.models.transaction import Transaction, TransactionItem
from app.services.inventory_service import (
    InventoryService, 
    InventoryError, 
    InsufficientStockError,
    ProductNotFoundError,
    CategoryNotFoundError,
    DuplicateProductError
)


class TestInventoryService:
    """Test cases for InventoryService."""
    
    @pytest.fixture
    def sample_tenant(self, app_context):
        """Create a sample tenant for testing."""
        tenant = Tenant.create(
            name="Test Business",
            business_type="retail",
            email="<EMAIL>"
        )
        return tenant
    
    @pytest.fixture
    def sample_user(self, app_context, sample_tenant):
        """Create a sample user for testing."""
        user = User.create_user(
            email="<EMAIL>",
            password="password123",
            first_name="Test",
            last_name="User",
            tenant_id=sample_tenant.id,
            role="manager"
        )
        return user
    
    @pytest.fixture
    def sample_category(self, app_context, sample_tenant):
        """Create a sample category for testing."""
        with patch('app.models.base.get_current_tenant', return_value=sample_tenant.id):
            category = Category.create(
                tenant_id=sample_tenant.id,
                name="Electronics",
                description="Electronic products"
            )
        return category
    
    @pytest.fixture
    def sample_product(self, app_context, sample_tenant, sample_category):
        """Create a sample product for testing."""
        with patch('app.models.base.get_current_tenant', return_value=sample_tenant.id):
            product = Product.create(
                tenant_id=sample_tenant.id,
                name="Test Product",
                sku="TEST001",
                barcode="123456789",
                category_id=sample_category.id,
                cost_price=Decimal('10.00'),
                selling_price=Decimal('15.00'),
                current_stock=100,
                minimum_stock=10
            )
        return product
    
    def test_create_product_success(self, app_context, sample_tenant):
        """Test successful product creation."""
        with patch('app.models.base.get_current_tenant', return_value=sample_tenant.id):
            product_data = {
                'name': 'New Product',
                'sku': 'NEW001',
                'cost_price': 5.00,
                'selling_price': 10.00,
                'current_stock': 50,
                'minimum_stock': 5
            }
            
            result = InventoryService.create_product(product_data)
            
            assert result['success'] is True
            assert 'product' in result
            assert result['product']['name'] == 'New Product'
            assert result['product']['sku'] == 'NEW001'
            
            # Verify product was created in database
            product = Product.query.filter_by(sku='NEW001').first()
            assert product is not None
            assert product.name == 'New Product'
    
    def test_create_product_duplicate_sku(self, app_context, sample_tenant, sample_product):
        """Test product creation with duplicate SKU."""
        with patch('app.models.base.get_current_tenant', return_value=sample_tenant.id):
            product_data = {
                'name': 'Duplicate Product',
                'sku': sample_product.sku,  # Use existing SKU
                'cost_price': 5.00,
                'selling_price': 10.00
            }
            
            result = InventoryService.create_product(product_data)
            
            assert result['success'] is False
            assert 'already exists' in result['error']
    
    def test_create_product_duplicate_barcode(self, app_context, sample_tenant, sample_product):
        """Test product creation with duplicate barcode."""
        with patch('app.models.base.get_current_tenant', return_value=sample_tenant.id):
            product_data = {
                'name': 'Duplicate Product',
                'sku': 'UNIQUE001',
                'barcode': sample_product.barcode,  # Use existing barcode
                'cost_price': 5.00,
                'selling_price': 10.00
            }
            
            result = InventoryService.create_product(product_data)
            
            assert result['success'] is False
            assert 'already exists' in result['error']
    
    def test_create_product_invalid_category(self, app_context, sample_tenant):
        """Test product creation with invalid category."""
        with patch('app.models.base.get_current_tenant', return_value=sample_tenant.id):
            product_data = {
                'name': 'New Product',
                'category_id': 99999,  # Non-existent category
                'cost_price': 5.00,
                'selling_price': 10.00
            }
            
            result = InventoryService.create_product(product_data)
            
            assert result['success'] is False
            assert 'Category not found' in result['error']
    
    def test_update_product_success(self, app_context, sample_tenant, sample_product):
        """Test successful product update."""
        with patch('app.models.base.get_current_tenant', return_value=sample_tenant.id):
            update_data = {
                'name': 'Updated Product Name',
                'selling_price': 20.00,
                'minimum_stock': 15
            }
            
            result = InventoryService.update_product(sample_product.id, update_data)
            
            assert result['success'] is True
            assert result['product']['name'] == 'Updated Product Name'
            assert result['product']['selling_price'] == 20.00
            assert result['product']['minimum_stock'] == 15
    
    def test_update_product_not_found(self, app_context, sample_tenant):
        """Test updating non-existent product."""
        with patch('app.models.base.get_current_tenant', return_value=sample_tenant.id):
            result = InventoryService.update_product(99999, {'name': 'Updated'})
            
            assert result['success'] is False
            assert 'Product not found' in result['error']
    
    def test_delete_product_success(self, app_context, sample_tenant, sample_product):
        """Test successful product deletion (soft delete)."""
        with patch('app.models.base.get_current_tenant', return_value=sample_tenant.id):
            result = InventoryService.delete_product(sample_product.id)
            
            assert result['success'] is True
            
            # Verify product is soft deleted
            product = Product.query.get(sample_product.id)
            assert product.is_active is False
    
    def test_get_product_success(self, app_context, sample_tenant, sample_product):
        """Test getting a single product."""
        with patch('app.models.base.get_current_tenant', return_value=sample_tenant.id):
            result = InventoryService.get_product(sample_product.id)
            
            assert result is not None
            assert result['id'] == sample_product.id
            assert result['name'] == sample_product.name
            assert result['sku'] == sample_product.sku
    
    def test_get_product_not_found(self, app_context, sample_tenant):
        """Test getting non-existent product."""
        with patch('app.models.base.get_current_tenant', return_value=sample_tenant.id):
            result = InventoryService.get_product(99999)
            
            assert result is None
    
    def test_get_products_with_pagination(self, app_context, sample_tenant, sample_category):
        """Test getting products with pagination."""
        with patch('app.models.base.get_current_tenant', return_value=sample_tenant.id):
            # Create multiple products
            for i in range(15):
                Product.create(
                    tenant_id=sample_tenant.id,
                    name=f"Product {i}",
                    sku=f"PROD{i:03d}",
                    category_id=sample_category.id,
                    cost_price=Decimal('5.00'),
                    selling_price=Decimal('10.00'),
                    current_stock=20
                )
            
            result = InventoryService.get_products(page=1, per_page=10)
            
            assert result['success'] is True
            assert len(result['products']) == 10
            assert result['pagination']['total'] == 15
            assert result['pagination']['pages'] == 2
            assert result['pagination']['has_next'] is True
    
    def test_get_products_with_search(self, app_context, sample_tenant, sample_category):
        """Test getting products with search filter."""
        with patch('app.models.base.get_current_tenant', return_value=sample_tenant.id):
            # Create products with different names
            Product.create(
                tenant_id=sample_tenant.id,
                name="Apple iPhone",
                sku="APPLE001",
                category_id=sample_category.id,
                cost_price=Decimal('500.00'),
                selling_price=Decimal('800.00')
            )
            Product.create(
                tenant_id=sample_tenant.id,
                name="Samsung Galaxy",
                sku="SAMSUNG001",
                category_id=sample_category.id,
                cost_price=Decimal('400.00'),
                selling_price=Decimal('700.00')
            )
            
            result = InventoryService.get_products(search="Apple")
            
            assert result['success'] is True
            assert len(result['products']) == 1
            assert result['products'][0]['name'] == "Apple iPhone"
    
    def test_get_products_low_stock_only(self, app_context, sample_tenant, sample_category):
        """Test getting only low stock products."""
        with patch('app.models.base.get_current_tenant', return_value=sample_tenant.id):
            # Create products with different stock levels
            Product.create(
                tenant_id=sample_tenant.id,
                name="High Stock Product",
                sku="HIGH001",
                category_id=sample_category.id,
                current_stock=100,
                minimum_stock=10,
                cost_price=Decimal('5.00'),
                selling_price=Decimal('10.00')
            )
            Product.create(
                tenant_id=sample_tenant.id,
                name="Low Stock Product",
                sku="LOW001",
                category_id=sample_category.id,
                current_stock=5,
                minimum_stock=10,
                cost_price=Decimal('5.00'),
                selling_price=Decimal('10.00')
            )
            
            result = InventoryService.get_products(low_stock_only=True)
            
            assert result['success'] is True
            assert len(result['products']) == 1
            assert result['products'][0]['name'] == "Low Stock Product"
    
    def test_adjust_stock_increase(self, app_context, sample_tenant, sample_product):
        """Test increasing stock level."""
        with patch('app.models.base.get_current_tenant', return_value=sample_tenant.id):
            original_stock = sample_product.current_stock
            
            result = InventoryService.adjust_stock(
                sample_product.id, 
                quantity=20, 
                reason="restock"
            )
            
            assert result['success'] is True
            assert result['product']['current_stock'] == original_stock + 20
            assert result['movement']['quantity'] == 20
            assert result['movement']['old_stock'] == original_stock
            assert result['movement']['new_stock'] == original_stock + 20
    
    def test_adjust_stock_decrease(self, app_context, sample_tenant, sample_product):
        """Test decreasing stock level."""
        with patch('app.models.base.get_current_tenant', return_value=sample_tenant.id):
            original_stock = sample_product.current_stock
            
            result = InventoryService.adjust_stock(
                sample_product.id, 
                quantity=-10, 
                reason="sale"
            )
            
            assert result['success'] is True
            assert result['product']['current_stock'] == original_stock - 10
            assert result['movement']['quantity'] == -10
    
    def test_adjust_stock_product_not_found(self, app_context, sample_tenant):
        """Test adjusting stock for non-existent product."""
        with patch('app.models.base.get_current_tenant', return_value=sample_tenant.id):
            result = InventoryService.adjust_stock(99999, quantity=10)
            
            assert result['success'] is False
            assert 'Product not found' in result['error']
    
    def test_get_low_stock_alerts(self, app_context, sample_tenant, sample_category):
        """Test getting low stock alerts."""
        with patch('app.models.base.get_current_tenant', return_value=sample_tenant.id):
            # Create products with different stock levels
            Product.create(
                tenant_id=sample_tenant.id,
                name="Out of Stock",
                sku="OUT001",
                category_id=sample_category.id,
                current_stock=0,
                minimum_stock=10,
                cost_price=Decimal('5.00'),
                selling_price=Decimal('10.00')
            )
            Product.create(
                tenant_id=sample_tenant.id,
                name="Critical Stock",
                sku="CRIT001",
                category_id=sample_category.id,
                current_stock=2,
                minimum_stock=10,
                cost_price=Decimal('5.00'),
                selling_price=Decimal('10.00')
            )
            Product.create(
                tenant_id=sample_tenant.id,
                name="Low Stock",
                sku="LOW001",
                category_id=sample_category.id,
                current_stock=8,
                minimum_stock=10,
                cost_price=Decimal('5.00'),
                selling_price=Decimal('10.00')
            )
            
            result = InventoryService.get_low_stock_alerts()
            
            assert result['success'] is True
            assert result['summary']['total_products'] == 3
            assert result['summary']['critical_count'] == 1  # Out of stock
            assert result['summary']['high_count'] == 1     # Critical stock (20% of minimum)
            assert result['summary']['low_count'] == 1      # Low stock
    
    def test_get_inventory_summary(self, app_context, sample_tenant, sample_category):
        """Test getting inventory summary."""
        with patch('app.models.base.get_current_tenant', return_value=sample_tenant.id):
            # Create products with different characteristics
            Product.create(
                tenant_id=sample_tenant.id,
                name="Product 1",
                sku="PROD001",
                category_id=sample_category.id,
                current_stock=50,
                minimum_stock=10,
                cost_price=Decimal('10.00'),
                selling_price=Decimal('20.00')
            )
            Product.create(
                tenant_id=sample_tenant.id,
                name="Product 2",
                sku="PROD002",
                category_id=sample_category.id,
                current_stock=5,  # Low stock
                minimum_stock=10,
                cost_price=Decimal('15.00'),
                selling_price=Decimal('30.00')
            )
            
            result = InventoryService.get_inventory_summary()
            
            assert result['success'] is True
            assert result['summary']['total_products'] == 2
            assert result['summary']['low_stock_count'] == 1
            assert result['summary']['total_cost_value'] == 575.0  # (50*10) + (5*15)
            assert result['summary']['total_retail_value'] == 1150.0  # (50*20) + (5*30)
            assert len(result['category_breakdown']) == 1
    
    def test_create_category_success(self, app_context, sample_tenant):
        """Test successful category creation."""
        with patch('app.models.base.get_current_tenant', return_value=sample_tenant.id):
            category_data = {
                'name': 'New Category',
                'description': 'A new category for testing',
                'color': '#FF0000',
                'sort_order': 1
            }
            
            result = InventoryService.create_category(category_data)
            
            assert result['success'] is True
            assert result['category']['name'] == 'New Category'
            assert result['category']['color'] == '#FF0000'
    
    def test_create_category_with_parent(self, app_context, sample_tenant, sample_category):
        """Test creating category with parent."""
        with patch('app.models.base.get_current_tenant', return_value=sample_tenant.id):
            category_data = {
                'name': 'Subcategory',
                'parent_id': sample_category.id
            }
            
            result = InventoryService.create_category(category_data)
            
            assert result['success'] is True
            assert result['category']['parent_id'] == sample_category.id
    
    def test_create_category_invalid_parent(self, app_context, sample_tenant):
        """Test creating category with invalid parent."""
        with patch('app.models.base.get_current_tenant', return_value=sample_tenant.id):
            category_data = {
                'name': 'Subcategory',
                'parent_id': 99999  # Non-existent parent
            }
            
            result = InventoryService.create_category(category_data)
            
            assert result['success'] is False
            assert 'Parent category not found' in result['error']
    
    def test_update_category_success(self, app_context, sample_tenant, sample_category):
        """Test successful category update."""
        with patch('app.models.base.get_current_tenant', return_value=sample_tenant.id):
            update_data = {
                'name': 'Updated Category',
                'description': 'Updated description'
            }
            
            result = InventoryService.update_category(sample_category.id, update_data)
            
            assert result['success'] is True
            assert result['category']['name'] == 'Updated Category'
            assert result['category']['description'] == 'Updated description'
    
    def test_update_category_circular_parent(self, app_context, sample_tenant, sample_category):
        """Test preventing circular parent reference."""
        with patch('app.models.base.get_current_tenant', return_value=sample_tenant.id):
            update_data = {
                'parent_id': sample_category.id  # Self as parent
            }
            
            result = InventoryService.update_category(sample_category.id, update_data)
            
            assert result['success'] is False
            assert 'cannot be its own parent' in result['error']
    
    def test_delete_category_success(self, app_context, sample_tenant, sample_category):
        """Test successful category deletion."""
        with patch('app.models.base.get_current_tenant', return_value=sample_tenant.id):
            result = InventoryService.delete_category(sample_category.id)
            
            assert result['success'] is True
            
            # Verify category is soft deleted
            category = Category.query.get(sample_category.id)
            assert category.is_active is False
    
    def test_delete_category_with_products(self, app_context, sample_tenant, sample_category, sample_product):
        """Test deleting category that has products."""
        with patch('app.models.base.get_current_tenant', return_value=sample_tenant.id):
            result = InventoryService.delete_category(sample_category.id)
            
            assert result['success'] is False
            assert 'cannot be deleted' in result['error']
    
    def test_get_categories_success(self, app_context, sample_tenant, sample_category):
        """Test getting categories list."""
        with patch('app.models.base.get_current_tenant', return_value=sample_tenant.id):
            result = InventoryService.get_categories()
            
            assert result['success'] is True
            assert len(result['categories']) >= 1
            assert any(cat['id'] == sample_category.id for cat in result['categories'])
            assert 'category_tree' in result
    
    @patch('app.services.inventory_service.db.session.query')
    def test_get_product_performance_analytics(self, mock_query, app_context, sample_tenant):
        """Test getting product performance analytics."""
        with patch('app.models.base.get_current_tenant', return_value=sample_tenant.id):
            # Mock the complex query result
            mock_result = MagicMock()
            mock_result.id = 1
            mock_result.name = "Test Product"
            mock_result.sku = "TEST001"
            mock_result.selling_price = Decimal('15.00')
            mock_result.cost_price = Decimal('10.00')
            mock_result.current_stock = 50
            mock_result.total_sold = 100
            mock_result.total_revenue = Decimal('1500.00')
            mock_result.transaction_count = 20
            mock_result.avg_quantity_per_sale = 5.0
            
            mock_query.return_value.join.return_value.join.return_value.filter.return_value.group_by.return_value.order_by.return_value.limit.return_value.all.return_value = [mock_result]
            
            result = InventoryService.get_product_performance_analytics()
            
            assert result['success'] is True
            assert 'performance_data' in result
            assert 'summary' in result
    
    def test_no_tenant_context(self, app_context):
        """Test service methods without tenant context."""
        with patch('app.models.base.get_current_tenant', return_value=None):
            result = InventoryService.create_product({'name': 'Test'})
            assert result['success'] is False
            assert 'No tenant context' in result['error']
            
            result = InventoryService.get_products()
            assert result['success'] is False
            assert 'No tenant context' in result['error']
            
            result = InventoryService.get_low_stock_alerts()
            assert result['success'] is False
            assert 'No tenant context' in result['error']
    
    @patch('app.services.inventory_service.current_app.logger')
    def test_error_handling_and_logging(self, mock_logger, app_context, sample_tenant):
        """Test error handling and logging."""
        with patch('app.models.base.get_current_tenant', return_value=sample_tenant.id):
            # Force an exception by mocking db.session.rollback to raise
            with patch('app.db.session.rollback', side_effect=Exception("Database error")):
                result = InventoryService.create_product({'name': 'Test'})
                
                assert result['success'] is False
                assert 'error occurred' in result['error']
                mock_logger.error.assert_called()
    
    def test_product_to_dict_conversion(self, app_context, sample_tenant, sample_product):
        """Test product to dictionary conversion."""
        with patch('app.models.base.get_current_tenant', return_value=sample_tenant.id):
            product_dict = InventoryService._product_to_dict(sample_product)
            
            assert product_dict['id'] == sample_product.id
            assert product_dict['name'] == sample_product.name
            assert product_dict['sku'] == sample_product.sku
            assert product_dict['current_stock'] == sample_product.current_stock
            assert 'is_low_stock' in product_dict
            assert 'profit_margin' in product_dict
            assert 'created_at' in product_dict
    
    def test_category_to_dict_conversion(self, app_context, sample_tenant, sample_category):
        """Test category to dictionary conversion."""
        with patch('app.models.base.get_current_tenant', return_value=sample_tenant.id):
            category_dict = InventoryService._category_to_dict(sample_category)
            
            assert category_dict['id'] == sample_category.id
            assert category_dict['name'] == sample_category.name
            assert 'product_count' in category_dict
            assert 'full_path' in category_dict
            assert 'created_at' in category_dict
    
    @patch('app.services.inventory_service.cache')
    def test_cache_clearing(self, mock_cache, app_context, sample_tenant):
        """Test cache clearing functionality."""
        InventoryService._clear_product_cache(sample_tenant.id)
        
        # Verify cache.delete was called multiple times
        assert mock_cache.delete.call_count > 0
        
        # Check that tenant-specific cache keys were used
        call_args_list = [call[0][0] for call in mock_cache.delete.call_args_list]
        assert any(f'tenant_{sample_tenant.id}' in key for key in call_args_list)