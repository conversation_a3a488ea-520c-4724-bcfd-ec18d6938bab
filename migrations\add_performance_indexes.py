"""Add performance indexes for database optimization.

This script adds comprehensive indexes to improve query performance
for the SaaS POS system.
"""

from alembic import op
import sqlalchemy as sa


def upgrade():
    """Add performance indexes."""
    
    # Tenant table indexes
    op.create_index('idx_tenant_subscription_status', 'tenant', ['subscription_status'])
    op.create_index('idx_tenant_business_type', 'tenant', ['business_type'])
    op.create_index('idx_tenant_subscription_expires', 'tenant', ['subscription_expires'])
    op.create_index('idx_tenant_name', 'tenant', ['name'])
    
    # User table indexes
    op.create_index('idx_user_tenant_active', 'user', ['tenant_id', 'is_active'])
    op.create_index('idx_user_tenant_role', 'user', ['tenant_id', 'role'])
    op.create_index('idx_user_email_tenant', 'user', ['email', 'tenant_id'])
    op.create_index('idx_user_last_login', 'user', ['last_login'])
    op.create_index('idx_user_locked_until', 'user', ['locked_until'])
    op.create_index('idx_user_email_verification', 'user', ['email_verification_token'])
    op.create_index('idx_user_password_reset', 'user', ['password_reset_token'])
    
    # Category table indexes
    op.create_index('idx_category_tenant_active', 'category', ['tenant_id', 'is_active'])
    op.create_index('idx_category_tenant_parent', 'category', ['tenant_id', 'parent_id'])
    op.create_index('idx_category_sort_order', 'category', ['tenant_id', 'sort_order'])
    op.create_index('idx_category_name_search', 'category', ['name'])
    
    # Product table indexes
    op.create_index('idx_product_tenant_active', 'product', ['tenant_id', 'is_active'])
    op.create_index('idx_product_tenant_category', 'product', ['tenant_id', 'category_id'])
    op.create_index('idx_product_name_search', 'product', ['name'])
    op.create_index('idx_product_low_stock', 'product', ['tenant_id', 'track_inventory', 'current_stock', 'minimum_stock'])
    op.create_index('idx_product_featured', 'product', ['tenant_id', 'is_featured', 'is_active'])
    op.create_index('idx_product_sort_order', 'product', ['tenant_id', 'sort_order'])
    
    # Inventory movement table indexes
    op.create_index('idx_inventory_tenant_product', 'inventory_movement', ['tenant_id', 'product_id'])
    op.create_index('idx_inventory_tenant_type', 'inventory_movement', ['tenant_id', 'movement_type'])
    op.create_index('idx_inventory_tenant_date', 'inventory_movement', ['tenant_id', 'created_at'])
    op.create_index('idx_inventory_product_date', 'inventory_movement', ['product_id', 'created_at'])
    op.create_index('idx_inventory_reference', 'inventory_movement', ['reference_id'])
    
    # Transaction table indexes
    op.create_index('idx_transaction_tenant_status', 'transaction', ['tenant_id', 'status'])
    op.create_index('idx_transaction_tenant_date', 'transaction', ['tenant_id', 'transaction_date'])
    op.create_index('idx_transaction_tenant_completed', 'transaction', ['tenant_id', 'completed_at'])
    op.create_index('idx_transaction_user_tenant', 'transaction', ['user_id', 'tenant_id'])
    op.create_index('idx_transaction_customer_email', 'transaction', ['customer_email'])
    op.create_index('idx_transaction_payment_method', 'transaction', ['payment_method'])
    op.create_index('idx_transaction_table_number', 'transaction', ['table_number'])
    op.create_index('idx_transaction_order_type', 'transaction', ['order_type'])
    op.create_index('idx_transaction_daily_sales', 'transaction', ['tenant_id', 'status', 'completed_at'])
    
    # Transaction item table indexes
    op.create_index('idx_transaction_item_tenant_transaction', 'transaction_item', ['tenant_id', 'transaction_id'])
    op.create_index('idx_transaction_item_tenant_product', 'transaction_item', ['tenant_id', 'product_id'])
    op.create_index('idx_transaction_item_transaction_product', 'transaction_item', ['transaction_id', 'product_id'])
    op.create_index('idx_transaction_item_product_date', 'transaction_item', ['product_id', 'created_at'])


def downgrade():
    """Remove performance indexes."""
    
    # Drop all the indexes we created
    indexes_to_drop = [
        # Tenant indexes
        'idx_tenant_subscription_status',
        'idx_tenant_business_type', 
        'idx_tenant_subscription_expires',
        'idx_tenant_name',
        
        # User indexes
        'idx_user_tenant_active',
        'idx_user_tenant_role',
        'idx_user_email_tenant',
        'idx_user_last_login',
        'idx_user_locked_until',
        'idx_user_email_verification',
        'idx_user_password_reset',
        
        # Category indexes
        'idx_category_tenant_active',
        'idx_category_tenant_parent',
        'idx_category_sort_order',
        'idx_category_name_search',
        
        # Product indexes
        'idx_product_tenant_active',
        'idx_product_tenant_category',
        'idx_product_name_search',
        'idx_product_low_stock',
        'idx_product_featured',
        'idx_product_sort_order',
        
        # Inventory movement indexes
        'idx_inventory_tenant_product',
        'idx_inventory_tenant_type',
        'idx_inventory_tenant_date',
        'idx_inventory_product_date',
        'idx_inventory_reference',
        
        # Transaction indexes
        'idx_transaction_tenant_status',
        'idx_transaction_tenant_date',
        'idx_transaction_tenant_completed',
        'idx_transaction_user_tenant',
        'idx_transaction_customer_email',
        'idx_transaction_payment_method',
        'idx_transaction_table_number',
        'idx_transaction_order_type',
        'idx_transaction_daily_sales',
        
        # Transaction item indexes
        'idx_transaction_item_tenant_transaction',
        'idx_transaction_item_tenant_product',
        'idx_transaction_item_transaction_product',
        'idx_transaction_item_product_date',
    ]
    
    for index_name in indexes_to_drop:
        try:
            op.drop_index(index_name)
        except Exception:
            # Index might not exist, continue
            pass